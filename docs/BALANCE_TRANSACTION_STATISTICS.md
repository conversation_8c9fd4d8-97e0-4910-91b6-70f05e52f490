# Balance Transaction Statistics Feature

## Overview

This feature provides comprehensive statistics and analytics for balance transactions, allowing users to get insights into transaction patterns, amounts, and trends over different time periods.

## Endpoints

### 1. Time-Period Statistics
```
GET /v1/balance/transactions/statistics
```

### 2. All-Time Statistics
```
GET /v1/balance/transactions/statistics/alltime
```

## Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `period` | string | Time period for statistics (`daily`, `monthly`, `yearly`) | `daily` |
| `start_date` | string | Start date in YYYY-MM-DD format | Last 30 days |
| `end_date` | string | End date in YYYY-MM-DD format | Current date |
| `merchant_id` | string | Filter by specific merchant ID | - |
| `profile_id` | string | Filter by specific profile ID | - |
| `types` | string | Comma-separated transaction type IDs | - |
| `include_types` | boolean | Include breakdown by transaction types | `false` |
| `include_merchants` | boolean | Include breakdown by merchants | `false` |
| `include_daily` | boolean | Include daily breakdown | `false` |
| `include_monthly` | boolean | Include monthly breakdown | `false` |

## Response Structure

```json
{
  "period": "daily",
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-01-31T23:59:59Z",
  "total_amount": 150000.50,
  "total_count": 1250,
  "by_type": [
    {
      "type": 100,
      "type_name": "income",
      "total_amount": 120000.00,
      "total_count": 800,
      "percentage": 80.0
    }
  ],
  "by_merchant": [
    {
      "merchant_id": "507f1f77bcf86cd799439011",
      "merchant_name": "Merchant A",
      "total_amount": 75000.25,
      "total_count": 500,
      "percentage": 50.0
    }
  ],
  "daily_breakdown": [
    {
      "date": "2024-01-01T00:00:00Z",
      "total_amount": 5000.00,
      "total_count": 45
    }
  ],
  "monthly_breakdown": [
    {
      "year": 2024,
      "month": 1,
      "total_amount": 150000.50,
      "total_count": 1250
    }
  ]
}
```

## Transaction Types

| Type ID | Name | Description |
|---------|------|-------------|
| 100 | income | Transaction income |
| 101 | subsidy | Subsidy transactions |
| 102 | feature_fee | Feature fee transactions |
| 103 | completed | Completed transactions |
| 104 | commission | Commission transactions |
| 105 | platform_fee | Platform fee transactions |
| 106 | third_party_fee | Third party fee transactions |
| 200 | disburse_merchant | Merchant disbursement |
| 400 | buy_coin | Coin purchase |
| 401 | disburse_coin | Coin disbursement |

## Example Requests

### 1. Basic Daily Statistics
```bash
curl "http://localhost:8080/v1/balance/transactions/statistics?period=daily"
```

### 2. Monthly Statistics with Type Breakdown
```bash
curl "http://localhost:8080/v1/balance/transactions/statistics?period=monthly&include_types=true"
```

### 3. Custom Date Range with All Breakdowns
```bash
curl "http://localhost:8080/v1/balance/transactions/statistics?start_date=2024-01-01&end_date=2024-01-31&include_types=true&include_merchants=true&include_daily=true"
```

### 4. Filter by Specific Transaction Types
```bash
curl "http://localhost:8080/v1/balance/transactions/statistics?types=100,101,102&include_types=true"
```

### 5. Merchant-Specific Statistics
```bash
curl "http://localhost:8080/v1/balance/transactions/statistics?merchant_id=507f1f77bcf86cd799439011&include_daily=true"
```

### 6. All-Time Statistics with Top Types and Merchants
```bash
curl "http://localhost:8080/v1/balance/transactions/statistics/alltime?include_types=true&include_merchants=true&top_limit=5"
```

### 7. All-Time Statistics with Trends
```bash
curl "http://localhost:8080/v1/balance/transactions/statistics/alltime?include_trends=true"
```

### 8. All-Time Statistics for Specific Merchant
```bash
curl "http://localhost:8080/v1/balance/transactions/statistics/alltime?merchant_id=507f1f77bcf86cd799439011&include_types=true&include_trends=true"
```

## All-Time Statistics Response

```json
{
  "total_income": 1500000.75,
  "total_expenses": 250000.25,
  "net_amount": 1250000.50,
  "total_transactions": 15000,
  "average_transaction_amount": 83.33,
  "first_transaction_date": "2023-01-01T00:00:00Z",
  "last_transaction_date": "2024-12-31T23:59:59Z",
  "top_transaction_types": [
    {
      "type": 100,
      "type_name": "income",
      "total_amount": 1200000.00,
      "total_count": 8000,
      "percentage": 80.0
    }
  ],
  "top_merchants": [
    {
      "merchant_id": "507f1f77bcf86cd799439011",
      "total_amount": 750000.25,
      "total_count": 5000,
      "percentage": 60.0
    }
  ],
  "monthly_trends": [
    {
      "year": 2024,
      "month": 1,
      "total_amount": 100000.00,
      "total_count": 800,
      "growth_rate": 5.5
    }
  ],
  "yearly_trends": [
    {
      "year": 2024,
      "total_amount": 1250000.50,
      "total_count": 15000,
      "growth_rate": 15.2
    }
  ]
}
```

## All-Time Statistics Query Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `merchant_id` | string | Filter by specific merchant ID | - |
| `profile_id` | string | Filter by specific profile ID | - |
| `types` | string | Comma-separated transaction type IDs | - |
| `include_types` | boolean | Include top transaction types | `false` |
| `include_merchants` | boolean | Include top merchants | `false` |
| `include_trends` | boolean | Include monthly/yearly trends | `false` |
| `top_limit` | integer | Limit for top results (1-50) | `10` |

## Implementation Details

### Architecture
- **Repository Layer**: Handles MongoDB aggregation queries
- **Service Layer**: Business logic and data processing
- **Handler Layer**: HTTP request/response handling

### Database Aggregation
The feature uses MongoDB aggregation pipelines for efficient data processing:
- Match stage for filtering
- Group stage for aggregation
- Sort stage for ordering results

### Performance Considerations
- Indexes on `created_at`, `company_id`, `merchant_id`, `type` fields recommended
- Large date ranges may impact performance
- Consider implementing caching for frequently accessed statistics

## Security
- Role-based access control (Customer role required)
- Company-level data isolation
- Input validation and sanitization

## Error Handling
- Invalid date formats return 400 Bad Request
- Missing permissions return 403 Forbidden
- Database errors return 500 Internal Server Error
