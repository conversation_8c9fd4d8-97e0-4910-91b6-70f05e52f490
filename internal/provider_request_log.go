package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type ProviderRequestLog struct {
	ID            string     `json:"_id"`
	CompanyID     string     `json:"company_id"`
	Provider      string     `json:"provider"`
	URL           string     `json:"url"`
	TransactionID string     `json:"transaction_id"`
	ExternalID    string     `json:"external_id"`
	SubAccountID  string     `json:"sub_account_id"`
	RequestBody   string     `json:"request_body"`
	ResponseBody  string     `json:"response_body"`
	StatusCode    int        `json:"status_code"`
	Status        string     `json:"status"`
	CreatedAt     *time.Time `json:"created_at"`
	UpdatedAt     *time.Time `json:"updated_at"`
}

type ProviderRequestLogPagin struct {
	Limit        int                  `json:"limit"`
	Page         int                  `json:"page"`
	Sort         string               `json:"sort"`
	TotalRecords int                  `json:"total_records"`
	TotalPages   int                  `json:"total_pages"`
	Records      []ProviderRequestLog `json:"records"`
}

func (b ProviderRequestLog) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b ProviderRequestLog) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *ProviderRequestLog) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateProviderRequestLog struct {
	CompanyID     string  `json:"company_id,omitempty"`
	Provider      string  `json:"provider,omitempty"`
	URL           *string `json:"url,omitempty"`
	TransactionID *string `json:"transaction_id"`
	ExternalID    *string `json:"external_id"`
	SubAccountID  *string `json:"sub_account_id"`
	RequestBody   *string `json:"request_body"`
	ResponseBody  *string `json:"response_body"`
	StatusCode    *int    `json:"status_code"`
	Status        string  `json:"status,omitempty"`
}

func (ct CreateProviderRequestLog) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.CompanyID, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type PayloadProviderRequestLog struct {
	CompanyID     string `json:"company_id,omitempty"`
	Provider      string `json:"provider,omitempty"`
	URL           string `json:"url,omitempty"`
	TransactionID string `json:"transaction_id"`
	ExternalID    string `json:"external_id"`
	SubAccountID  string `json:"sub_account_id"`
	RequestBody   string `json:"request_body"`
	ResponseBody  string `json:"response_body"`
	StatusCode    int    `json:"status_code"`
}
