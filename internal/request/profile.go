package request

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"reflect"
	"sort"
	"time"

	"github.com/carlmjohnson/requests"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"go.uber.org/zap"
)

type User struct {
	ID                  string     `json:"_id"`
	ParentRole          int        `json:"parent_role"`
	ParentID            string     `json:"parent_id"`
	ParentUsername      string     `json:"parent_username"`
	Role                int        `json:"role"`
	Username            string     `json:"username"`
	LoginID             string     `json:"login_id"`
	Alias               string     `json:"alias"`
	FirstName           string     `json:"first_name"`
	LastName            string     `json:"last_name"`
	NickName            string     `json:"nickname"`
	Email               string     `json:"email"`
	MobilePhone         string     `json:"mobile_phone"`
	MobilePhoneCountry  string     `json:"mobile_phone_country"`
	MobilePhoneCode     string     `json:"mobile_phone_code"`
	MobilePhoneNumber   string     `json:"mobile_phone_number"`
	MobilePhoneVerified bool       `json:"mobile_phone_verified"`
	Gender              int        `json:"gender"`
	Type                int        `json:"type"`
	Status              bool       `json:"status"`
	ParentBlocked       bool       `json:"parent_blocked"`
	CreatedIP           string     `json:"created_ip"`
	LastLoggedIn        *time.Time `json:"last_logged_in"`
	FirstTransaction    bool       `json:"first_transaction"`
	Group               string     `json:"group"`
	Avatar              string     `json:"avatar"`
	Bookmark            string     `json:"bookmark"`
	Bank                string     `json:"bank"`
	Mtc                 string     `json:"mtc"`
	AccountNumber       string     `json:"account_number"`
	AccountName         string     `json:"account_name"`
	LastLoggedInIP      string     `json:"last_logged_in_ip"`
	IsSocialMedia       bool       `json:"is_social_media"`
	TermsAccepted       bool       `json:"terms_accepted"`
	Referrer            string     `json:"referrer"`
	ReferrerCode        string     `json:"referrer_code"`
	ReferrerID          string     `json:"referrer_id"`
	MarketingCode       string     `json:"marketing_code"`
	MarketingID         string     `json:"marketing_id"`
	VoucherCode         string     `json:"voucher_code"`
	VoucherID           string     `json:"voucher_id"`
	CashbackID          string     `json:"cashback_id"`
	CreatedAt           *time.Time `json:"created_at"`
	UpdatedAt           *time.Time `json:"updated_at"`
}

type Session struct {
	ID            string     `json:"_id"`
	Parent        string     `json:"parent"`
	User          string     `json:"user"`
	Username      string     `json:"username"`
	LoginID       string     `json:"login_id"`
	Session       string     `json:"session"`
	Guest         bool       `json:"guest"`
	Online        bool       `json:"online"`
	HistoricalMtc string     `json:"historical_mtc"`
	RecentMtc     string     `json:"recent_mtc"`
	CreatedIP     string     `json:"created_ip"`
	ValidUntil    *time.Time `json:"valid_until"`
	LastAct       *time.Time `json:"last_act"`
	CreatedAt     *time.Time `json:"created_at"`
	UpdatedAt     *time.Time `json:"updated_at"`
}

type UserSecurityProtected struct {
	ID            string     `json:"_id"`
	User          string     `json:"user"`
	LoginID       string     `json:"login_id"`
	Alias         string     `json:"alias"`
	ResetLoginID  bool       `json:"reset_login_id"`
	ResetPassword bool       `json:"reset_password"`
	Pin           string     `json:"pin"`
	ResetPin      bool       `json:"reset_pin"`
	WhitelistIP   string     `json:"whitelist_ip"`
	CreatedIP     string     `json:"created_ip"`
	CreatedAt     *time.Time `json:"created_at"`
	UpdatedAt     *time.Time `json:"updated_at"`
}

type UserSocialProvider struct {
	ID          string     `json:"_id"`
	User        string     `json:"user"`
	Parent      string     `json:"parent_id"`
	LoginID     string     `json:"login_id"`
	Alias       string     `json:"alias"`
	Provider    string     `json:"provider"`
	Email       string     `json:"email"`
	Name        string     `json:"name"`
	FirstName   string     `json:"firstname"`
	LastName    string     `json:"lastname"`
	NickName    string     `json:"nickname"`
	Description string     `json:"description"`
	UserID      string     `json:"user_id"`
	AvatarURL   string     `json:"avatar_url"`
	Location    string     `json:"location"`
	WhitelistIP string     `json:"whitelist_ip"`
	CreatedIP   string     `json:"created_ip"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
}

type DataProfile struct {
	Profile         *User                  `json:"profile"`
	Session         *Session               `json:"session"`
	Security        *UserSecurityProtected `json:"security,omitempty"`
	Url             *string                `json:"url,omitempty"`
	SocialProviders *[]UserSocialProvider  `json:"social_providers,omitempty"`
}

type ResponseProfile struct {
	Code      int         `json:"code"`
	Data      DataProfile `json:"data"`
	Messages  any         `json:"messages"`
	RequestID string      `json:"request_id"`
}

type CreateUser struct {
	Company             string `json:"company,omitempty"`
	Password            string `json:"password,omitempty"`
	Pin                 string `json:"pin,omitempty"`
	ParentRole          int    `json:"parent_role,omitempty"`
	ParentID            string `json:"parent_id,omitempty"`
	ParentUsername      string `json:"parent_username,omitempty"`
	Role                int    `json:"role,omitempty"`
	Username            string `json:"username,omitempty"`
	LoginID             string `json:"login_id,omitempty"`
	Alias               string `json:"alias,omitempty"`
	FirstName           string `json:"first_name,omitempty"`
	LastName            string `json:"last_name,omitempty"`
	NickName            string `json:"nickname,omitempty"`
	Email               string `json:"email,omitempty"`
	MobilePhone         string `json:"mobile_phone,omitempty"`
	MobilePhoneCountry  string `json:"mobile_phone_country,omitempty"`
	MobilePhoneCode     string `json:"mobile_phone_code,omitempty"`
	MobilePhoneNumber   string `json:"mobile_phone_number,omitempty"`
	MobilePhoneVerified bool   `json:"mobile_phone_verified,omitempty"`
	Gender              int    `json:"gender,omitempty"`
	Type                int    `json:"type,omitempty"`
	Status              bool   `json:"status,omitempty"`
	ParentBlocked       bool   `json:"parent_blocked,omitempty"`
	CreatedIP           string `json:"created_ip,omitempty"`
	FirstTransaction    bool   `json:"first_transaction,omitempty"`
	Group               string `json:"group,omitempty"`
	Avatar              string `json:"avatar,omitempty"`
	Bookmark            string `json:"bookmark,omitempty"`
	Bank                string `json:"bank,omitempty"`
	Mtc                 string `json:"mtc,omitempty"`
	AccountNumber       string `json:"account_number,omitempty"`
	AccountName         string `json:"account_name,omitempty"`
	LastLoggedInIP      string `json:"last_logged_in_ip,omitempty"`
	IsSocialMedia       bool   `json:"is_social_media,omitempty"`
	Referrer            string `json:"referrer,omitempty"`
	ReferrerCode        string `json:"referrer_code,omitempty"`
	ReferrerID          string `json:"referrer_id,omitempty"`
	MarketingID         string `json:"marketing_id,omitempty"`
	MarketingCode       string `json:"marketing_code,omitempty"`
	VoucherID           string `json:"voucher_id,omitempty"`
	VoucherCode         string `json:"voucher_code,omitempty"`
	CashbackID          string `json:"cashback_id,omitempty"`
	Cashback            string `json:"cashback,omitempty"`
	CreateMerchant      bool   `json:"create_merchant,omitempty"`
	TermsAccepted       bool   `json:"terms_accepted,omitempty"`
	Hash                string `json:"hash,omitempty"`
	IsGroup             bool   `json:"is_group,omitempty"`
	Bypass              string `json:"bypass,omitempty"`
}

func (u CreateUser) GenerateHash(secret string) (CreateUser, error) {
	var strToHash string
	fields := reflect.TypeOf(u)

	// Get the field names
	var fieldNames []string
	for i := 0; i < fields.NumField(); i++ {
		fieldNames = append(fieldNames, fields.Field(i).Name)
	}
	sort.Strings(fieldNames)
	fmt.Println("fieldNames", fieldNames)
	// Iterate through sorted field names to ensure consistency
	for _, fieldName := range fieldNames {
		fieldValue := reflect.ValueOf(u).FieldByName(fieldName).Interface()
		if fieldValue != "" || fieldValue != nil {
			strToHash += fmt.Sprintf(".%v", fieldValue)
		}
	}

	// Generate the hash
	fmt.Println("strToHash", strToHash[1:])
	fmt.Println("secret", secret)
	u.Hash = pkg.GenerateHash(secret, strToHash[1:])
	return u, nil
}

func (t *Request) CreateProfile(ctx context.Context, identifier Identifier, body CreateUser) (*ResponseProfile, error) {
	if identifier.GetAccessToken() == "" {
		return nil, fmt.Errorf("client access token empty")
	}

	var response ResponseProfile
	t.util.Logger.Info("Request.CreateProfile.Body", zap.Any("Body", body))
	// Create the multipart body
	api := requests.New(t.config).
		BaseURL(t.baseURL).
		Path("/profile").
		Method(http.MethodPost).
		BodyJSON(body)
	// Set the Authorization header and fetch the response
	err := api.Header("Authorization", identifier.GetAccessToken()).
		ToJSON(&response).
		Fetch(ctx)

	if err != nil {
		return nil, riot.WrapErrorfLog(t.util.Logger, err, riot.ErrorCodeUnknown, "Request.CreateProfile.api.ToJSON")
	}

	t.util.Logger.Info("Request.Upload.Response", zap.Any("Response", response))

	return &response, nil
}

type DataSession struct {
	Session string `json:"session"`
}

type ResponseProfileSession struct {
	Code      int         `json:"code"`
	Data      DataSession `json:"data"`
	Messages  any         `json:"messages"`
	RequestID string      `json:"request_id"`
}

func (t *Request) GetProfileSession(ctx context.Context, identifier Identifier, profileID string) (*ResponseProfileSession, error) {
	if identifier.GetAccessToken() == "" {
		return nil, fmt.Errorf("client access token empty")
	}

	var response ResponseProfileSession
	t.util.Logger.Info("Request.GetProfileSession", zap.Any("Param", profileID))
	// Create the multipart body
	api := requests.New(t.config).
		BaseURL(t.baseURL).
		Path(fmt.Sprintf("/profile/%s/sessions", profileID)).
		Method(http.MethodGet).
		Param("bypass", "test")

	// Set the Authorization header and fetch the response
	err := api.Header("Authorization", identifier.GetAccessToken()).
		ToJSON(&response).
		Fetch(ctx)

	if err != nil {
		return nil, riot.WrapErrorfLog(t.util.Logger, err, riot.ErrorCodeUnknown, "Request.GetProfileSession.api.ToJSON")
	}

	t.util.Logger.Info("Request.GetProfileSession.Response", zap.Any("Response", response))

	return &response, nil
}

type ResponseProfileDetail struct {
	Code      int    `json:"code"`
	Data      User   `json:"data"`
	Messages  any    `json:"messages"`
	RequestID string `json:"request_id"`
}

func (t *Request) GetProfileByID(ctx context.Context, identifier Identifier, profileID string) (*ResponseProfileDetail, error) {
	if identifier.GetAccessToken() == "" {
		return nil, fmt.Errorf("client access token empty")
	}

	var response ResponseProfileDetail
	t.util.Logger.Info("Request.ResponseProfileSession", zap.Any("Param", profileID))
	// Create the multipart body
	api := requests.New(t.config).
		BaseURL(t.baseURL).
		Path(fmt.Sprintf("/profile/%s", profileID)).
		Method(http.MethodGet).
		Param("bypass", "test")

	// Set the Authorization header and fetch the response
	err := api.Header("Authorization", identifier.GetAccessToken()).
		ToJSON(&response).
		Fetch(ctx)

	if err != nil {
		return nil, riot.WrapErrorfLog(t.util.Logger, err, riot.ErrorCodeUnknown, "Request.GetProfileByID.api.ToJSON")
	}

	t.util.Logger.Info("Request.GetProfileSession.Response", zap.Any("Response", response))

	return &response, nil
}

func (t *Request) AuthProvider(ctx context.Context, identifier Identifier, provider string, queries url.Values) (*ResponseProfile, error) {
	if identifier.GetAccessToken() == "" {
		return nil, fmt.Errorf("client access token empty")
	}

	var response ResponseProfile
	// Create the multipart body
	api := requests.New(t.config).
		BaseURL(t.baseURL).
		Path(fmt.Sprintf("/auth/social/%s", provider)).
		Method(http.MethodGet).
		Params(queries)
	// Set the Authorization header and fetch the response
	err := api.Header("Authorization", identifier.GetAccessToken()).
		ToJSON(&response).
		Fetch(ctx)

	if err != nil {
		return nil, riot.WrapErrorfLog(t.util.Logger, err, riot.ErrorCodeUnknown, "Request.AuthProvider.api.ToJSON")
	}

	t.util.Logger.Info("Request.AuthProvider.Response", zap.Any("Response", response))

	return &response, nil
}

type UpdateProfile struct {
	Username       string `json:"username"`
	Password       string `json:"password"`
	Pin            string `json:"pin"`
	Avatar         string `json:"avatar"`
	FirstName      string `json:"first_name"`
	LastName       string `json:"last_name"`
	NickName       string `json:"nickname"`
	MobilePhone    string `json:"mobile_phone"`
	ReferrerCode   string `json:"referrer_code"`
	MarketingCode  string `json:"marketing_code"`
	VoucherCode    string `json:"voucher_code"`
	CreateMerchant bool   `json:"create_merchant"`
	TermsAccepted  bool   `json:"terms_accepted"`
	Session        string `json:"session"`
	Hash           string `json:"hash"`
	ByPass         string `json:"bypass"`
}

func (t *Request) EditUserSocial(ctx context.Context, identifier Identifier, profileID string, body UpdateProfile) (*ResponseProfile, error) {
	if identifier.GetAccessToken() == "" {
		return nil, fmt.Errorf("client access token empty")
	}

	var response ResponseProfile

	body.Hash, _ = pkg.HashStruct(identifier.GetClientSecret(), body)

	t.util.Logger.Info("Request.EditUserSocial.Body", zap.Any("Body", body))
	// Create the multipart body
	api := requests.New(t.config).
		BaseURL(t.baseURL).
		Path("/profile/" + profileID + "/socials").
		Method(http.MethodPut).
		BodyJSON(body)
	// Set the Authorization header and fetch the response
	err := api.Header("Authorization", identifier.GetAccessToken()).
		ToJSON(&response).
		Fetch(ctx)

	if err != nil {
		return nil, riot.WrapErrorfLog(t.util.Logger, err, riot.ErrorCodeUnknown, "Request.EditUserSocial.api.ToJSON")
	}

	t.util.Logger.Info("Request.Upload.Response", zap.Any("Response", response))

	return &ResponseProfile{
		Code: response.Code,
		Data: DataProfile{
			Profile: response.Data.Profile,
			Session: response.Data.Session,
		},
		Messages:  response.Messages,
		RequestID: response.RequestID,
	}, nil
}
