package rabbitmq

import (
	"context"
	"fmt"
	"time"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot"
	r "github.com/continue-team/riot/rabbitmq"
	"go.uber.org/zap"
)

type Publisher struct {
	util   riot.Util
	broker *r.Broker
}

func NewPublisher(util riot.Util, rmq *riot.RabbitMQ) (*Publisher, error) {

	broker, err := r.New<PERSON>roker(util, rmq)

	if err != nil {
		return nil, err
	}

	return &Publisher{
		util:   util,
		broker: broker,
	}, nil
}

func (t *Publisher) PublishChangeData(ctx context.Context, changeData internal.CreateMerchant) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtChangeData,
		ID:         changeData.ProfileID,
		ActionDate: time.Now().UTC().String(),
		Data:       changeData,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtChangeData), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "fame.route.frontend", payload)
}

func (t *Publisher) PublishProviderRequestLog(ctx context.Context, providerRequestLog internal.PayloadProviderRequestLog) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtProviderRequestLog,
		ID:         providerRequestLog.CompanyID,
		ActionDate: time.Now().UTC().String(),
		Data:       providerRequestLog,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtProviderRequestLog), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishQrPayment(ctx context.Context, payloadQrPayment internal.PayloadWebhookQRPayment) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtQrPayment,
		ID:         payloadQrPayment.BusinessID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadQrPayment,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtQrPayment), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishEWalletPayment(ctx context.Context, payloadEWalletPayment internal.PayloadWebhookEWalletPayment) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtEWalletPayment,
		ID:         payloadEWalletPayment.BusinessID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadEWalletPayment,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtEWalletPayment), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishCardPayment(ctx context.Context, payloadCardPayment internal.PayloadWebhookCardPayment) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtCardPayment,
		ID:         payloadCardPayment.BusinessID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadCardPayment,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtCardPayment), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishWebhookDisburse(ctx context.Context, payloadWebhookDisbursement internal.PayloadWebhookDisbursement) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtWebhookDisbursement,
		ID:         payloadWebhookDisbursement.BusinessID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadWebhookDisbursement,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtWebhookDisbursement), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishAutoDisburse(ctx context.Context, payloadCronDisburse internal.PayloadAutoDisburse) error {

	const Exchange = "manaslu.exchange.cron"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtAutoDisbursement,
		ID:         payloadCronDisburse.MerchantID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadCronDisburse,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtAutoDisbursement), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "fame.route.backend", payload)
}

func (t *Publisher) PublishPlatformFee(ctx context.Context, payloadPlatformFee internal.PayloadPlatformFee) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtPlatformFee,
		ID:         payloadPlatformFee.TransactionID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadPlatformFee,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtQrPayment), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishTransfer(ctx context.Context, payloadTransfer internal.PayloadTransfer) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtTransfer,
		ID:         payloadTransfer.TransactionID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadTransfer,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtTransfer), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishTxnBalance(ctx context.Context, payloadTxnBalance internal.PayloadTxnBalance) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtTxnBalance,
		ID:         payloadTxnBalance.MerchantID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadTxnBalance,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtTxnBalance), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishCoinBalance(ctx context.Context, payloadTxnCoin internal.PayloadCoinBalance) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtCoinBalance,
		ID:         payloadTxnCoin.CoinID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadTxnCoin,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtCoinBalance), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}

func (t *Publisher) PublishTipCompleted(ctx context.Context, payloadTipCompleted internal.PayloadTipCompleted) error {

	const Exchange = "fame.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtTipCompleted,
		ID:         payloadTipCompleted.TransactionID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadTipCompleted,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtTipCompleted), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "fame.route.backend", payload)
}

func (t *Publisher) PublishDigitalPayCompleted(ctx context.Context, payloadDigitalCompleted internal.PayloadDigitalPayCompleted) error {

	const Exchange = "fame.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtDigitalPayCompleted,
		ID:         payloadDigitalCompleted.TransactionID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadDigitalCompleted,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtDigitalPayCompleted), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "fame.route.backend", payload)
}

func (t *Publisher) PublishPaymentMercenaryState(ctx context.Context, payloadPaymentState internal.PayloadPaymentState) error {

	const Exchange = "mercenary-order"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtPaymentState,
		ID:         payloadPaymentState.TransactionID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadPaymentState,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtPaymentState), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "mercenary-order", payload)
}

func (t *Publisher) PublishPaymentFameState(ctx context.Context, payloadPaymentState internal.PayloadPaymentState) error {

	const Exchange = "fame.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtPaymentState,
		ID:         payloadPaymentState.TransactionID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadPaymentState,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtPaymentState), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "fame.route.backend", payload)
}

func (t *Publisher) PublishUpdateMerchant(ctx context.Context, exchange, routing string, payloadUpdateMerchant internal.PayloadUpdateMerchant) error {

	// const Exchange = "hecarim.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtUpdateMerchant,
		ID:         payloadUpdateMerchant.MerchantID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadUpdateMerchant,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtUpdateMerchant), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, exchange, routing, payload)
}

func (t *Publisher) PublishSubsidy(ctx context.Context, payloadSubsidy internal.PayloadSubsidy) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtSubsidy,
		ID:         payloadSubsidy.TransactionID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadSubsidy,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtSubsidy), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}
