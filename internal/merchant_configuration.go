package internal

import (
	"time"
)

type MerchantConfiguration struct {
	ID               string            `json:"_id"`
	CompanyID        string            `json:"company_id"`
	MerchantID       string            `json:"merchant_id"`
	Slug             string            `json:"slug"`
	Name             string            `json:"name"`
	DisplayName      string            `json:"display_name"`
	Description      string            `json:"description"`
	FeeType          string            `json:"fee_type"`
	FeeValue         float64           `json:"fee_value"`
	FeeDiscountType  string            `json:"fee_discount_type"`
	FeeDiscountValue float64           `json:"fee_discount_value"`
	IsDiscount       bool              `json:"is_discount"`
	IsVoucher        bool              `json:"is_voucher"`
	VoucherCode      string            `json:"voucher_code"`
	Currency         string            `json:"currency"`
	Metadata         map[string]string `json:"metadata,omitempty"`
	Status           string            `json:"status"`
	ExpiresAt        *time.Time        `json:"expires_at"`
	CreatedAt        *time.Time        `json:"created_at"`
	UpdatedAt        *time.Time        `json:"updated_at"`
}

type MerchantConfigurationPagin struct {
	Limit        int                     `json:"limit"`
	Page         int                     `json:"page"`
	Sort         string                  `json:"sort"`
	TotalRecords int                     `json:"total_records"`
	TotalPages   int                     `json:"total_pages"`
	Records      []MerchantConfiguration `json:"records"`
}
