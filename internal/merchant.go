package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Merchant struct {
	ID                string                `json:"_id"`
	CompanyID         string                `json:"company_id"`
	ProfileID         string                `json:"profile_id"`
	Slug              string                `json:"slug"`
	Name              string                `json:"name"`
	LoginID           string                `json:"login_id"`
	Email             string                `json:"email"`
	ProfilePictureURL string                `json:"profile_picture_url"`
	SubAccounts       *[]primitive.ObjectID `json:"subaccounts"`
	Balance           float64               `json:"balance"`
	SettledBalance    float64               `json:"settled_balance"`
	TotalDisburse     float64               `json:"total_disburse"`
	ReferrerID        string                `json:"referrer_id"`
	MarketingID       string                `json:"marketing_id"`
	VoucherID         string                `json:"voucher_id"`
	Description       string                `json:"description"`
	Metadata          map[string]string     `json:"metadata"`
	Status            string                `json:"status"`
	CreatedAt         *time.Time            `json:"created_at"`
	UpdatedAt         *time.Time            `json:"updated_at"`
}

type MerchantPagin struct {
	Limit        int        `json:"limit"`
	Page         int        `json:"page"`
	Sort         string     `json:"sort"`
	TotalRecords int        `json:"total_records"`
	TotalPages   int        `json:"total_pages"`
	Records      []Merchant `json:"records"`
}

func (b Merchant) IsExist() bool {
	return b.ID != ""
}

func (b Merchant) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Merchant) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b Merchant) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b Merchant) ValidateBalance(amount float64) error {
	if b.Balance < amount {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s balance is not enough.", b.ID)),
		}
	}
	return nil
}

func (b *Merchant) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateMerchant struct {
	CompanyID         string                `json:"company_id,omitempty"`
	ProfileID         string                `json:"profile_id,omitempty"`
	Slug              *string               `json:"slug,omitempty"`
	Name              *string               `json:"name,omitempty"`
	LoginID           *string               `json:"login_id,omitempty"`
	Email             *string               `json:"email,omitempty"`
	ProfilePictureURL *string               `json:"profile_picture_url,omitempty"`
	SubAccounts       *[]primitive.ObjectID `json:"subaccounts,omitempty"`
	Description       *string               `json:"description,omitempty"`
	Balance           *float64              `json:"balance,omitempty"`
	SettledBalance    *float64              `json:"settled_balance,omitempty"`
	TotalDisburse     *float64              `json:"total_disburse,omitempty"`
	ReferrerID        string                `json:"referrer_id,omitempty"`
	MarketingID       string                `json:"marketing_id,omitempty"`
	VoucherID         string                `json:"voucher_id,omitempty"`
	Metadata          *map[string]string    `json:"metadata,omitempty"`
	Status            string                `json:"status,omitempty"`
}

func (ct CreateMerchant) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.Name, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type Identifier struct {
	ProfileID  string `json:"profile_id"`
	Period     string `json:"period"`
	State      string `json:"state"`
	MerchantID string `json:"merchant_id"`
	AuthToken  string `json:"auth_token"`
}

func (i *Identifier) Validate() error {
	// TODO: implement validation
	if i.ProfileID == "" {
		return validation.Errors{
			util.GetStructName(i): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", i.ProfileID)),
		}
	}
	return nil
}

func (i *Identifier) ValidateState() error {
	// TODO: implement validation
	if i.State == "" {
		return validation.Errors{
			"state": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", i.State)),
		}
	}
	return nil
}

func (i *Identifier) ValidatePeriod() error {
	// TODO: implement validation
	if i.Period == "" {
		return validation.Errors{
			"period": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", i.Period)),
		}
	}
	return nil
}

func (i *Identifier) ValidateMarchantID() error {
	if i.MerchantID == "" {
		return validation.Errors{
			"merchant_id": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", i.MerchantID)),
		}
	}
	return nil
}

type PayloadCreateMerchant struct {
	Username    string `json:"username,omitempty"`
	LoginID     string `json:"login_id,omitempty"`
	CompanyID   string `json:"company_id,omitempty"`
	ProfileID   string `json:"profile_id,omitempty"`
	ReferrerID  string `json:"referrer_id,omitempty"`
	MarketingID string `json:"marketing_id,omitempty"`
	VoucherID   string `json:"voucher_id,omitempty"`
}

type PayloadUpdateMerchant struct {
	CompanyID  string `json:"company_id,omitempty"`
	ProfileID  string `json:"profile_di,omitempty"`
	MerchantID string `json:"merchant_id,omitempty"`
}
