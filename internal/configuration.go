package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Configuration struct {
	ID          string            `json:"_id"`
	CompanyID   string            `json:"company_id"`
	Slug        string            `json:"slug"`
	Name        string            `json:"name"`
	DisplayName string            `json:"display_name"`
	Description string            `json:"description"`
	FeeType     string            `json:"fee_type"`
	FeeValue    float64           `json:"fee_value"`
	Currency    string            `json:"currency"`
	Metadata    map[string]string `json:"metadata,omitempty"`
	Status      string            `json:"status"`
	CreatedAt   *time.Time        `json:"created_at"`
	UpdatedAt   *time.Time        `json:"updated_at"`
}

type ConfigurationPagin struct {
	Limit        int             `json:"limit"`
	Page         int             `json:"page"`
	Sort         string          `json:"sort"`
	TotalRecords int             `json:"total_records"`
	TotalPages   int             `json:"total_pages"`
	Records      []Configuration `json:"records"`
}

func (b Configuration) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Configuration) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Configuration) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateConfiguration struct {
	CompanyID   string             `json:"company_id,omitempty"`
	Slug        string             `json:"slug,omitempty"`
	Name        *string            `json:"name,omitempty"`
	DisplayName *string            `json:"display_name,omitempty"`
	Description *string            `json:"description,omitempty"`
	FeeType     *string            `json:"fee_type,omitempty"`
	FeeValue    *float64           `json:"fee_value,omitempty"`
	Metadata    *map[string]string `json:"metadata,omitempty"`
	Currency    *string            `json:"currency,omitempty"`
	Status      string             `json:"status,omitempty"`
}

func (ct CreateConfiguration) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.CompanyID, validation.Required),
		validation.Field(&ct.Name, validation.Required),
		validation.Field(&ct.DisplayName, validation.Required),
		validation.Field(&ct.Description, validation.Required),
		validation.Field(&ct.FeeType, validation.Required),
		validation.Field(&ct.FeeValue, validation.Required),
		validation.Field(&ct.Currency, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type PayloadConfiguration struct {
	CompanyID     string `json:"company_id,omitempty"`
	TransactionID string `json:"transaction_id"`
	MerchantID    string `json:"merchant_id"`
}
