package internal

import "time"

type PayloadWebhookQRPayment struct {
	Event         string `json:"event"`
	Created       string `json:"created"`
	BusinessID    string `json:"business_id"`
	Provider      string `json:"provider"`
	TransactionID string `json:"transaction_id"`
	Data          struct {
		ID          string    `json:"id"`
		BusinessID  string    `json:"business_id"`
		Currency    string    `json:"currency"`
		Amount      float64   `json:"amount"`
		Status      string    `json:"status"`
		Updated     time.Time `json:"updated"`
		Created     time.Time `json:"created"`
		QRID        string    `json:"qr_id"`
		QRString    string    `json:"qr_string"`
		ReferenceID string    `json:"reference_id"`
		Type        string    `json:"type"`
		ChannelCode string    `json:"channel_code"`
		ExpiresAt   string    `json:"expires_at"`
		Metadata    struct {
			BranchCode string `json:"branch_code"`
		} `json:"metadata"`
		PaymentDetail struct {
			ReceiptID string `json:"receipt_id"`
			Source    string `json:"source"`
		} `json:"payment_detail"`
	} `json:"data"`
}

type PayloadWebhookCardPayment struct {
	Event         string     `json:"event"`
	Created       *time.Time `json:"created"`
	BusinessID    string     `json:"business_id"`
	Provider      string     `json:"provider"`
	TransactionID string     `json:"transaction_id"`
	Data          struct {
		ID                    string                 `json:"id"`
		Status                string                 `json:"status"`
		AuthorizedAmount      float64                `json:"authorized_amount"`
		CaptureAmount         float64                `json:"capture_amount"`
		Currency              string                 `json:"currency"`
		Metadata              map[string]interface{} `json:"metadata"`
		CreditCardTokenID     string                 `json:"credit_card_token_id"`
		BusinessID            string                 `json:"business_id"`
		MerchantID            string                 `json:"merchant_id"`
		MerchantReferenceCode string                 `json:"merchant_reference_code"`
		ExternalID            string                 `json:"external_id"`
		ECI                   string                 `json:"eci"`
		ChargeType            string                 `json:"charge_type"`
		MaskedCardNumber      string                 `json:"masked_card_number"`
		CardBrand             string                 `json:"card_brand"`
		CardType              string                 `json:"card_type"`
		Descriptor            string                 `json:"descriptor"`
		AuthorizationID       string                 `json:"authorization_id"`
		BankReconciliationID  string                 `json:"bank_reconciliation_id"`
		IssuingBankName       string                 `json:"issuing_bank_name"`
		CVNCode               string                 `json:"cvn_code"`
		ApprovalCode          string                 `json:"approval_code"`
		Created               string                 `json:"created"`
		CardFingerprint       string                 `json:"card_fingerprint"`
	} `json:"data"`
}

type PayloadWebhookDisbursement struct {
	Event         string              `json:"event"`
	Created       string              `json:"created"`
	BusinessID    string              `json:"business_id"`
	Provider      string              `json:"provider"`
	TransactionID string              `json:"transaction_id"`
	Data          WebhookDisbursement `json:"data"`
}

type WebhookDisbursement struct {
	ID                      string    `json:"id"`
	UserID                  string    `json:"user_id"`
	ExternalID              string    `json:"external_id"`
	Amount                  int       `json:"amount"`
	BankCode                string    `json:"bank_code"`
	AccountHolderName       string    `json:"account_holder_name"`
	DisbursementDescription string    `json:"disbursement_description"`
	FailureCode             string    `json:"failure_code"`
	IsInstant               bool      `json:"is_instant"`
	Status                  string    `json:"status"`
	Updated                 time.Time `json:"updated"`
	Created                 time.Time `json:"created"`
	EmailTo                 []string  `json:"email_to"`
	EmailCC                 []string  `json:"email_cc"`
	EmailBCC                []string  `json:"email_bcc"`
}

type PayloadWebhookEWalletPayment struct {
	Data struct {
		ID                 string       `json:"id"`
		Basket             any          `json:"basket"` // use appropriate type if basket is not always null
		Status             string       `json:"status"`
		Actions            Actions      `json:"actions"`
		Updated            time.Time    `json:"updated"`
		Created            time.Time    `json:"created"`
		Currency           string       `json:"currency"`
		Metadata           Metadata     `json:"metadata"`
		VoidedAt           *string      `json:"voided_at"` // nullable field
		CaptureNow         bool         `json:"capture_now"`
		CustomerID         *string      `json:"customer_id"` // nullable
		CallbackURL        string       `json:"callback_url"`
		ChannelCode        string       `json:"channel_code"`
		FailureCode        *string      `json:"failure_code"` // nullable
		ReferenceID        string       `json:"reference_id"`
		ChargeAmount       int          `json:"charge_amount"`
		CaptureAmount      int          `json:"capture_amount"`
		CheckoutMethod     string       `json:"checkout_method"`
		PaymentMethodID    *string      `json:"payment_method_id"` // nullable
		ChannelProperties  ChannelProps `json:"channel_properties"`
		IsRedirectRequired bool         `json:"is_redirect_required"`
	} `json:"data"`
	Event      string `json:"event"`
	Created    string `json:"created"`
	BusinessID string `json:"business_id"`
	Provider   string `json:"provider"`
}

type Actions struct {
	MobileWebCheckoutURL      string `json:"mobile_web_checkout_url"`
	DesktopWebCheckoutURL     string `json:"desktop_web_checkout_url"`
	MobileDeeplinkCheckoutURL string `json:"mobile_deeplink_checkout_url"`
}

type Metadata struct {
	BranchCode string `json:"branch_code"`
}

type ChannelProps struct {
	SuccessRedirectURL string `json:"success_redirect_url"`
}

type PayloadChargePayment struct {
	ReferenceID string     `json:"reference_id"`
	WebhookAt   *time.Time `json:"webhook_at,omitempty"`
	Status      string     `json:"status"`
}
