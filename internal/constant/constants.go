// constant.go
package constant

const (
	RoleCorporate  = 4
	RoleCompany    = 5
	RoleSubAccount = 6
)

const (
	TypeFolderExport  = "export"
	TypeFolderAsset   = "assets"
	TypeFolderContent = "content"
	TypeFolderApk     = "apk"
)

// Status constants
const (
	StatusDelete       = "delete"
	StatusActive       = "active"
	StatusInactive     = "inactive"
	StatusPending      = "pending"
	StatusBuildProses  = "build_proses"
	StatusBuildSuccess = "build_success"
	StatusBuildPending = "pending"
	StatusBoolActive   = "bool_active"
	StatusBoolInactive = "bool_inactive"
)

const (
	//transaction
	StatusTransactionNew                = "new"
	StatusTransactionUnpaid             = "unpaid"
	StatusTransactionPaid               = "paid"
	StatusTransactionCancelled          = "cancelled"
	StatusTransactionFailed             = "failed"
	StatusTransactionRefunded           = "refunded"
	StatusTransactionExpired            = "expired"
	StatusTransactionCompleted          = "completed"
	StatusTransactionInProgress         = "in_progress"
	StatusTransactionDisburseReady      = "disbursement_ready"
	StatusTransactionDisburseInProgress = "disbursement_in_progress"
	StatusTransactionDisburseFailed     = "disbursement_failed"
)

// TransactionType constants
const (
	TypeActive       = 99
	TypeInactive     = 98
	TypeDelete       = 97
	TypeBuildSuccess = 96
	TypeBuildProses  = 95
	TypeBuildPending = 94
	TypeBoolActive   = true
	TypeBoolInactive = false
)

const (

	/**
	* Transaction Statuses
	 */
	TypeTransactionNew                = 1
	TypeTransactionUnpaid             = 3
	TypeTransactionPaid               = 2
	TypeTransactionCancelled          = 4
	TypeTransactionExpired            = 5
	TypeTransactionCompleted          = 7
	TypeTransactionFailed             = 8
	TypeTransactionRefunded           = 9
	TypeTransactionInProgress         = 10
	TypeTransactionDisburseReady      = 11
	TypeTransactionDisburseInProgress = 12
	TypeTransactionDisburseFailed     = 13

	DisbursementStatusInProgress = 1
	DisbursementStatusCompleted  = 2
	DisbursementStatusFailed     = 3
	DisbursementStatusWaiting    = 4
	DisbursementStatusCancelled  = 5

	/**
	 * Payment Method
	 */
	PaymentMethodVA          = "VA"
	PaymentMethodCC          = "CC"
	PaymentMethodOffline     = "OFFLINE"
	PaymentMethodQRIS        = "QRIS"
	PaymentMethodPaymentCode = "PAYMENT_CODE"
	PaymentMethodKKI         = 6

	/**
	 * Xendit Tx Report Request Status
	 */
	XenditTxReportRequestStatusInProgress = 1
	XenditTxReportRequestStatusCompleted  = 2
	XenditTxReportRequestStatusFailed     = 3

	/**
	 * Platform fee statuses
	 */
	PfStatusProforma   = 1
	PfStatusPaid       = 2
	PfStatusUnpaid     = 3
	PfStatusCanceled   = 4
	PfStatusInprogress = 5

	/**
	 * Platform fee statuses
	 */
	PfPaymentStatusPending  = 1
	PfPaymentStatusActive   = 2
	PfPaymentStatusPaid     = 3
	PfPaymentStatusInactive = 4

	/**
	* Payment Statuses
	 */
	TypePaymentUnpaid                 = 0
	TypePaymentInProgress             = 1
	TypePaymentPaid                   = 2
	TypePaymentCancelled              = 3
	TypePaymentExpired                = 4
	TypePaymentDisbursementReady      = 5
	TypePaymentDisbursementInProgress = 6
	TypePaymentCompleted              = 7
	TypePaymentDisbursementFailed     = 8
)

var BoolStatusMapping = map[string]bool{
	StatusBoolActive:   TypeBoolActive,
	StatusBoolInactive: TypeBoolInactive,
}

// statusMapping is a general-purpose mapping between status names and their corresponding constants
var statusMapping = map[string]int{
	StatusDelete:                        TypeDelete,
	StatusActive:                        TypeActive,
	StatusInactive:                      TypeInactive,
	StatusBuildProses:                   TypeBuildProses,
	StatusBuildSuccess:                  TypeBuildSuccess,
	StatusBuildPending:                  TypeBuildPending,
	StatusTransactionNew:                TypeTransactionNew,
	StatusTransactionPaid:               TypeTransactionPaid,
	StatusTransactionCancelled:          TypeTransactionCancelled,
	StatusTransactionExpired:            TypeTransactionExpired,
	StatusTransactionCompleted:          TypeTransactionCompleted,
	StatusTransactionUnpaid:             TypeTransactionUnpaid,
	StatusTransactionDisburseInProgress: TypeTransactionDisburseInProgress,
	StatusTransactionDisburseFailed:     TypeTransactionDisburseFailed,
	StatusTransactionDisburseReady:      TypeTransactionDisburseReady,
	StatusTransactionFailed:             TypeTransactionFailed,
	StatusTransactionRefunded:           TypeTransactionRefunded,
	StatusTransactionInProgress:         TypeTransactionInProgress,
}

// Constant function to get the constant value based on the key
func Constant(key string) int {
	if v, ok := statusMapping[key]; ok {
		return v
	}

	return -1
}

// ReverseConstant function to get the status name based on the constant value
func ReverseConstant(value int) string {
	// Populate reverseStatusMapping by iterating over statusMapping
	reverseStatusMapping := make(map[int]string)
	for key, val := range statusMapping {
		reverseStatusMapping[val] = key
	}

	if key, ok := reverseStatusMapping[value]; ok {
		return key
	}
	return "Not Found"
}

const (
	// Route Event
	TaskEvtWebsiteCreated        = "website:event:website_created"
	TaskEvtChangeData            = "event:change_data"
	TaskEvtChangeStatusCompleted = "event:change_status_completed"

	//run every 3 minutes
	TaskEvtCronPopulateSocialIntegration = "event:cron_populate_social_integration"
	TaskEvtPopulateTiktokPostLatest      = "event:populate_tiktok_post_latest"
	TaskEvtPopulateInstagramPostLatest   = "event:populate_instagram_post_latest"
	TaskEvtSocialPost                    = "event:social_post"

	TaskEvtSocialPostStatistic = "event:social_post_statistic"

	TaskEvtCronPopulateSocialPost         = "event:cron_populate_social_post"
	TaskEvtCronPopulateFinalizeSocialPost = "event:cron_populate_finalize_social_post"

	TaskEvtPopulateTiktokUserLatest       = "event:populate_tiktok_user_latest"
	TaskEvtCronPopulateFinalizeSocialUser = "event:cron_populate_finalize_social_user"
	TaskEvtSocialUserStatistic            = "event:social_user_statistic"
	TaskEvtSocialUser                     = "event:social_user"

	TaskEvtCronInsightSocials         = "event:cron_insight_socials"
	TaskEvtCronHourUserInsightSocials = "event:cron_hour_user_insight_socials"
	TaskEvtCronDayUserInsightSocials  = "event:cron_day_user_insight_socials"
	TaskEvtCronHourPostInsightSocials = "event:cron_hour_post_insight_socials"
	TaskEvtCronDayPostInsightSocials  = "event:cron_day_post_insight_socials"
	TaskEvtUserInsightSocials         = "event:user_insight_socials"
	TaskEvtPostInsightSocials         = "event:post_insight_socials"
	TaskEvtShortURLTrack              = "event:short_url_track"
	TaskEvtShortURLTrackIP            = "event:short_url_track_ip"

	// Task Event
	TaskEvtCreateMerchant = "event:create_merchant"

	TaskEvtProviderRequestLog = "event:provider_request_log"

	TaskEvtCronPopulateDisbursement = "event:cron_populate_disbursement"
	TaskEvtQrPayment                = "event:qr_payment"
	TaskEvtEWalletPayment           = "event:ewallet_payment"
	TaskEvtCardPayment              = "event:card_payment"
	TaskEvtWebhookDisbursement      = "event:webhook_disbursement"
	TaskEvtAutoDisbursement         = "event:auto_disbursement"
	TaskEvtPlatformFee              = "event:platform_fee"
	TaskEvtTransfer                 = "event:transfer"
	TaskEvtTxnBalance               = "event:txn_balance"
	TaskEvtCoinBalance              = "event:coin_balance"
	TaskEvtSubsidy                  = "event:subsidy"
	TaskEvtTipCompleted             = "event:tip_completed"
	TaskEvtDigitalPayCompleted      = "event:digital_pay_completed"
	TaskEvtPaymentState             = "event:payment_state"
	TaskEvtUpdateMerchant           = "event:update_merchant"
)

const (
	ExchangeWebsite = "website"
)

const (
	AccountTypeIndividual = "INDIVIDUAL"
	AccountTypeBrand      = "BRAND"
	AccountTypeAgency     = "AGENCY"
)

const (
	MainHeaderIconic     = "ICONIC_HEADER"
	MainHeaderSmall      = "SMALL_HEADER"
	MainHeaderMedium     = "MEDIUM_HEADER"
	MainHeaderFullscreen = "FULLSCREEN_HEADER"
)

const (
	SocialLinksPositionHide   = "HIDE"
	SocialLinksPositionTop    = "TOP"
	SocialLinksPositionBottom = "BOTTOM"
)

const (
	ProviderTiktok    = "tiktok"
	ProviderInstagram = "instagram"
	ProviderYoutube   = "youtube"

	PeriodHourly = "hourly"
	PeriodDaily  = "daily"
)

const (
	FeatureFlag = "fame"
)

const (
	TypeLinkGroup = "GROUP"
)

const (
	FeeTypePercentage       = "percentage"
	FeeTypeFixed            = "fixed"
	PaymentMethodQRCode     = "QR"
	PaymentMethodEwallet    = "EWALLET"
	PaymentMethodCreditCard = "CREDIT_CARD"
	PaymentMethodVa         = "VA"

	TypeFeatureTip         = "TIP"
	TypeFeatureVoucher     = "VOUCHER"
	TypeFeatureVoucherGame = "VOUCHER_GAME"
	TypeFeatureCoin        = "COIN"
	TypeFeatureSubcription = "SUBSCRIPTION"
	TypeFeatureDisb        = "DISBURSEMENT"

	TypeFeePlatform = "PLATFORM_FEE"
	TypeFeeMerchant = "MERCHANT_FEE"
	TypeFeeVoucher  = "VOUCHER_FEE"

	ProviderXendit = "xendit"

	ChannelCodeOVO       = "ID_OVO"
	ChannelCodeGopay     = "ID_GOPAY"
	ChannelCodeLinkAja   = "ID_LINKAJA"
	ChannelCodeAstraPay  = "ID_ASTRAPAY"
	ChannelCodeDana      = "ID_DANA"
	ChannelCodeShopeePay = "ID_SHOPEEPAY"
	ChannelCodeJenius    = "ID_JENIUSPAY"
)

const (
	TypeTxnIncome                   = 100
	TypeTxnSubsidy                  = 101
	TypeTxnFeatureFee               = 102
	TypeTxnCompleted                = 103
	TypeTxnComission                = 104
	TypeTxnPlatformFee              = 105
	TypeTxnThirdPartyFee            = 106
	TypeTxnDisburseMerhant          = 200
	TypeTxnDisburseFee              = 201
	TypeTxnDisburseFail             = 204
	TypeTxnDisburseFeeFail          = 205
	TypeTxnBuyCoin                  = 400
	TypeTxnDisburseCoin             = 401
	TypeTxnDisburseCoinCompleted    = 402
	TypeTxnRoundBook                = 500
	TypeTxnRoundCancelByPlayer      = 501
	TypeTxnRoundCancelByStreamer    = 502
	TypeTxnRoundComplete            = 503
	TypeTxnRoundCancelBySystem      = 504
	TypeTxnTournamentBook           = 505
	TypeTxnTournamentCancelBySystem = 506
	TypeTxnTournamentCancelByPlayer = 507
	TypeTxnLiveGift                 = 600
	TypeTxnCancelBySystem           = 999
)

const (
	TxnCompleted             = "completed"
	TxnIncome                = "income"
	TxnFeatureFee            = "feature_fee"
	TxnComission             = "comission"
	TxnSubsidy               = "subsidy"
	TxnThirdPartyFee         = "third_party_fee"
	TxnPlatformFee           = "platform_fee"
	TxnBuyCoin               = "buy_coin"
	TxnDisburseCoin          = "disburse_coin"
	TxnDisburseCoinCompleted = "disburse_coin_completed"
	TxnRoundBook             = "round_book"
	TxnRoundCancelByPlayer   = "round_cancel_by_player"
	TxnRoundCancelByStreamer = "round_cancel_by_streamer"
	TxnRoundComplete         = "round_complete"
	TxnRoundCancelBySystem   = "round_cancel_by_system"
	TxnDisburseMerhant       = "disburse_merchant"
	TxnDisburseFee           = "disburse_fee"
	TxnDisburseFail          = "disburse_fail"
	TxnDisburseFeeFail       = "disburse_fee_fail"
	TxnLiveGift              = "live_gift"
	TxnCancelBySystem        = "cancel_by_system"
)

var txnMapping = map[string]int{
	TxnIncome:                TypeTxnIncome,
	TxnSubsidy:               TypeTxnSubsidy,
	TxnComission:             TypeTxnComission,
	TxnFeatureFee:            TypeTxnFeatureFee,
	TxnCompleted:             TypeTxnCompleted,
	TxnPlatformFee:           TypeTxnPlatformFee,
	TxnThirdPartyFee:         TypeTxnThirdPartyFee,
	TxnBuyCoin:               TypeTxnBuyCoin,
	TxnDisburseCoin:          TypeTxnDisburseCoin,
	TxnDisburseCoinCompleted: TypeTxnDisburseCoinCompleted,
	TxnRoundBook:             TypeTxnRoundBook,
	TxnRoundCancelByPlayer:   TypeTxnRoundCancelByPlayer,
	TxnRoundCancelByStreamer: TypeTxnRoundCancelByStreamer,
	TxnRoundCancelBySystem:   TypeTxnRoundCancelBySystem,
	TxnRoundComplete:         TypeTxnRoundComplete,
	TxnLiveGift:              TypeTxnLiveGift,
	TxnDisburseMerhant:       TypeTxnDisburseMerhant,
	TxnDisburseFee:           TypeTxnDisburseFee,
	TxnDisburseFail:          TypeTxnDisburseFail,
	TxnDisburseFeeFail:       TypeTxnDisburseFeeFail,
	TxnCancelBySystem:        TypeTxnCancelBySystem,
}

func ConstantTxn(key string) int {
	if v, ok := txnMapping[key]; ok {
		return v
	}

	return -1
}

// ReverseConstant function to get the status name based on the constant value
func ReverseTxnConstant(value int) string {
	// Populate reverseStatusMapping by iterating over statusMapping
	reverseStatusMapping := make(map[int]string)
	for key, val := range txnMapping {
		reverseStatusMapping[val] = key
	}

	if key, ok := reverseStatusMapping[value]; ok {
		return key
	}
	return "Not Found"
}

const (
	TypeObjectTxn      = "transaction"
	TypeObjectDisb     = "disbursement"
	TypeObjectDisbCoin = "disbursement_coin"
)
