package tasks

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/manaslu/internal/repositories"
	"github.com/continue-team/manaslu/internal/services"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/util"
	r "github.com/continue-team/riot/rabbitmq"
	"go.uber.org/zap"
)

type Task struct {
	util                        riot.Util
	merchantSvc                 *services.MerchantService
	merchantPfSvc               *services.MerchantPlatformFeeService
	merchantVoucherSvc          *services.MerchantVoucherService
	merchantRepo                *repositories.MerchantRepository
	transactionRepo             *repositories.TransactionRepository
	paymentRepo                 *repositories.PaymentRepository
	providerRequestLogRepo      *repositories.ProviderRequestLogRepository
	providerPublishEventLogRepo *repositories.ProviderPublishEventLogRepository
	transferRepo                *repositories.TransferRepository
	subsidyRepo                 *repositories.SubsidyRepository
	disburseRepo                *repositories.DisbursementRepository
	disburseLogRepo             *repositories.DisbursementLogRepository
	disburseConfigRepo          *repositories.DisbursementConfigurationRepository
	merchantBankRepo            *repositories.MerchantBankRepository
	configRepo                  *repositories.ConfigurationRepository
	paymentMethodRepo           *repositories.PaymentMethodRepository
	merchantPlatformRepo        *repositories.MerchantPlatformRepository
	featureFeeRepo              *repositories.FeatureFeeRepository
	txnBalanceRepo              *repositories.BalanceTransactionRepository
	disburseSvc                 *services.DisbursementService
	txnBalanceSvc               *services.BalanceTransactionService
	coinSvc                     *services.CoinService
	txnCoinSvc                  *services.CoinTransactionService
	taskPublisher               *rabbitmq.Publisher
}

func NewTask(
	u riot.Util,
	merchantSvc *services.MerchantService,
	merchantPfSvc *services.MerchantPlatformFeeService,
	merchantVoucherSvc *services.MerchantVoucherService,
	merchantRepo *repositories.MerchantRepository,
	transactionRepo *repositories.TransactionRepository,
	paymentRepo *repositories.PaymentRepository,
	providerRequestLogRepo *repositories.ProviderRequestLogRepository,
	providerPublishEventLogRepo *repositories.ProviderPublishEventLogRepository,
	transferRepo *repositories.TransferRepository,
	subsidyRepo *repositories.SubsidyRepository,
	disburseRepo *repositories.DisbursementRepository,
	disburseLogRepo *repositories.DisbursementLogRepository,
	disburseConfigRepo *repositories.DisbursementConfigurationRepository,
	merchantBankRepo *repositories.MerchantBankRepository,
	configRepo *repositories.ConfigurationRepository,
	paymentMethodRepo *repositories.PaymentMethodRepository,
	merchantPlatformRepo *repositories.MerchantPlatformRepository,
	featureFeeRepo *repositories.FeatureFeeRepository,
	txnBalanceRepo *repositories.BalanceTransactionRepository,
	disburseSvc *services.DisbursementService,
	txnBalanceSvc *services.BalanceTransactionService,
	coinSvc *services.CoinService,
	txnCoinSvc *services.CoinTransactionService,
	taskPublisher *rabbitmq.Publisher,
) *Task {

	return &Task{
		util:                        u,
		merchantSvc:                 merchantSvc,
		merchantPfSvc:               merchantPfSvc,
		merchantVoucherSvc:          merchantVoucherSvc,
		merchantRepo:                merchantRepo,
		transactionRepo:             transactionRepo,
		paymentRepo:                 paymentRepo,
		providerRequestLogRepo:      providerRequestLogRepo,
		providerPublishEventLogRepo: providerPublishEventLogRepo,
		transferRepo:                transferRepo,
		subsidyRepo:                 subsidyRepo,
		disburseRepo:                disburseRepo,
		disburseLogRepo:             disburseLogRepo,
		disburseConfigRepo:          disburseConfigRepo,
		merchantBankRepo:            merchantBankRepo,
		configRepo:                  configRepo,
		paymentMethodRepo:           paymentMethodRepo,
		merchantPlatformRepo:        merchantPlatformRepo,
		featureFeeRepo:              featureFeeRepo,
		txnBalanceRepo:              txnBalanceRepo,
		disburseSvc:                 disburseSvc,
		txnBalanceSvc:               txnBalanceSvc,
		coinSvc:                     coinSvc,
		txnCoinSvc:                  txnCoinSvc,
		taskPublisher:               taskPublisher,
	}
}

func (c *Task) TaskEvtCreateMerchant(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadCreateMerchant

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtCreateMerchant", payload))

	ctx := context.Background()

	uplineMerchant := internal.Merchant{}

	nexusData := riot.Nexus{
		Server: &riot.Server{
			Webservice: &riot.NexusWebservice{
				ID: payload.CompanyID,
			},
			Profile: &riot.NexusProfile{
				ID:       payload.ProfileID,
				ParentID: payload.CompanyID,
				LoginID:  payload.Username,
			},
		},
	}

	paramsMerchant := internal.CreateMerchant{
		CompanyID:   payload.CompanyID,
		ProfileID:   payload.ProfileID,
		Name:        &payload.Username,
		LoginID:     &payload.LoginID,
		ReferrerID:  payload.ReferrerID,
		MarketingID: payload.MarketingID,
		VoucherID:   payload.VoucherID,
	}

	if payload.ReferrerID != "" {

		filterUplineMerchant := map[string]interface{}{
			"profile_id": payload.ReferrerID,
			"company_id": payload.CompanyID,
		}

		uplineMerchant, err = c.merchantRepo.GetByFilter(ctx, filterUplineMerchant)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCreateMerchant.merchantRepo.GetByFilter", zap.Error(err))
		}

		paramsMerchant.ReferrerID = uplineMerchant.ID
		paramsMerchant.MarketingID = uplineMerchant.MarketingID

	}

	merchant, err := c.merchantSvc.Create(ctx, paramsMerchant, nexusData)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCreateMerchant.merchantSvc.Create", zap.Error(err))
	}

	//create merchant platform fee
	paramsMerchantPf := internal.CreateMerchantPlatformFee{
		CompanyID:   payload.CompanyID,
		ProfileID:   payload.ProfileID,
		MerchantID:  merchant.ID,
		ReferrerID:  payload.ReferrerID,
		MarketingID: payload.MarketingID,
	}

	identifier := internal.Identifier{
		MerchantID: merchant.ID,
	}

	_, err = c.merchantPfSvc.Create(ctx, identifier, paramsMerchantPf, nexusData)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCreateMerchant.merchantPfSvc.Create", zap.Error(err))
	}

	if payload.VoucherID != "" {
		//create merchant voucher
		paramsMerchantVoucher := internal.CreateMerchantVoucher{
			CompanyID: payload.CompanyID,
			ProfileID: payload.ProfileID,
			VoucherID: payload.VoucherID,
		}

		identifier := internal.Identifier{
			MerchantID: merchant.ID,
		}

		_, err = c.merchantVoucherSvc.Create(ctx, identifier, paramsMerchantVoucher, nexusData)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCreateMerchant.merchantVoucherSvc.Create", zap.Error(err))
		}

	}

	//create user coins
	paramsUserCoins := internal.CreateCoin{
		CompanyID: payload.CompanyID,
		ProfileID: payload.ProfileID,
		LoginID:   &payload.LoginID,
		Metadata: &map[string]string{
			"merchant_id": merchant.ID,
			"merchant":    merchant.Name,
		},
	}

	_, err = c.coinSvc.Create(ctx, paramsUserCoins, nexusData)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCreateMerchant.coinSvc.Create", zap.Error(err))
	}

	return nil
}

func (c *Task) TaskEvtProviderRequestLog(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadProviderRequestLog

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtProviderRequestLog", payload))

	ctx := context.Background()

	//create provider request log
	paramsProviderRequestLog := internal.CreateProviderRequestLog{
		CompanyID:     payload.CompanyID,
		Provider:      payload.Provider,
		URL:           &payload.URL,
		TransactionID: &payload.TransactionID,
		ExternalID:    &payload.ExternalID,
		SubAccountID:  &payload.SubAccountID,
		RequestBody:   &payload.RequestBody,
		ResponseBody:  &payload.ResponseBody,
		StatusCode:    &payload.StatusCode,
		Status:        constant.StatusActive,
	}

	_, err = c.providerRequestLogRepo.Create(ctx, paramsProviderRequestLog)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtProviderRequestLog.providerRequestLogRepo.Create", zap.Error(err))
	}

	return nil
}

func (c *Task) TaskEvtQrPayment(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadWebhookQRPayment

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtProviderRequestLog", payload))

	ctx := context.Background()

	filterPayment := map[string]interface{}{
		"external_id": payload.Data.ReferenceID,
		"provider":    payload.Provider,
	}

	payment, err := c.paymentRepo.GetByFilter(ctx, filterPayment)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPayment.paymentRepo.GetAllWithSoftDelete", zap.Error(err))
	}

	if err := payment.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPayment.payment.ValidateNotExist", zap.Error(err))
	}

	filterTransaction := map[string]interface{}{
		"id":     payment.TransactionID,
		"status": constant.TypeTransactionUnpaid,
	}

	transaction, err := c.transactionRepo.GetByFilter(ctx, filterTransaction)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPayment.transactionRepo.GetAllWithSoftDelete", zap.Error(err))
	}

	c.util.Logger.Info("Task.TaskEvtQrPayment.transaction", zap.Any("Info", transaction))

	if err := transaction.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPayment.transaction.ValidateNotExist", zap.Error(err))
	}

	dataJson, err := json.Marshal(payload.Data)
	if err != nil {
		// Handle the error
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPayment.payment.json.Marshal", zap.Error(err))
	}

	dataJsonString := string(dataJson)

	eventParams := internal.CreateProviderPublishEventLog{
		Event:         &payload.Event,
		CompanyID:     payment.CompanyID,
		Provider:      payload.Provider,
		TransactionID: &payment.TransactionID,
		ExternalID:    &payment.ExternalID,
		Data:          &dataJsonString,
		Status:        constant.StatusActive,
	}

	_, err = c.providerPublishEventLogRepo.Create(ctx, eventParams)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPayment.providerPublishEventLogRepo.Create", zap.Error(err))
	}

	switch transaction.ProductType {

	case constant.TypeFeatureTip:
		return c.TaskEvtQrPaymentTip(ctx, payment, transaction, payload)

	case constant.TypeFeatureVoucherGame, constant.TypeFeatureCoin, constant.TypeFeatureSubcription:

		payloadChargePayment := internal.PayloadChargePayment{
			Status:      payload.Data.Status,
			ReferenceID: payload.Data.ReferenceID,
			WebhookAt:   &payload.Data.Created,
		}

		return c.TaskChargePayment(ctx, payment, transaction, payloadChargePayment)
	}

	return nil
}

func (c *Task) TaskEvtEWalletPayment(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadWebhookEWalletPayment

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtProviderRequestLog", payload))

	ctx := context.Background()

	filterPayment := map[string]interface{}{
		"external_id": payload.Data.ReferenceID,
		"provider":    payload.Provider,
		"status":      constant.TypeTransactionUnpaid,
	}

	payment, err := c.paymentRepo.GetByFilter(ctx, filterPayment)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtEWalletPayment.paymentRepo.GetAllWithSoftDelete", zap.Error(err))
	}

	if err := payment.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtEWalletPayment.payment.ValidateNotExist", zap.Error(err))
	}

	c.util.Logger.Info("Task.TaskEvtEWalletPayment.payment", zap.Any("Info", payment))

	filterTransaction := map[string]interface{}{
		"id":     payment.TransactionID,
		"status": constant.TypeTransactionUnpaid,
	}

	transaction, err := c.transactionRepo.GetByFilter(ctx, filterTransaction)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtEWalletPayment.transactionRepo.GetAllWithSoftDelete", zap.Error(err))
	}

	c.util.Logger.Info("Task.TaskEvtEWalletPayment.transaction", zap.Any("Info", transaction))

	if err := transaction.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtEWalletPayment.transaction.ValidateNotExist", zap.Error(err))
	}

	dataJson, err := json.Marshal(payload.Data)
	if err != nil {
		// Handle the error
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtEWalletPayment.payment.json.Marshal", zap.Error(err))
	}

	dataJsonString := string(dataJson)

	eventParams := internal.CreateProviderPublishEventLog{
		Event:         &payload.Event,
		CompanyID:     payment.CompanyID,
		Provider:      payload.Provider,
		TransactionID: &payment.TransactionID,
		ExternalID:    &payment.ExternalID,
		Data:          &dataJsonString,
		Status:        constant.StatusActive,
	}

	_, err = c.providerPublishEventLogRepo.Create(ctx, eventParams)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtEWalletPayment.providerPublishEventLogRepo.Create", zap.Error(err))
	}

	payloadChargePayment := internal.PayloadChargePayment{
		Status:      payload.Data.Status,
		ReferenceID: payload.Data.ReferenceID,
		WebhookAt:   &payload.Data.Created,
	}

	err = c.TaskChargePayment(ctx, payment, transaction, payloadChargePayment)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskChargePayment", zap.Error(err))
	}

	return nil
}

func (c *Task) TaskEvtCardPayment(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadWebhookCardPayment

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtCardPayment.XXXXXXXX", payload))

	ctx := context.Background()

	filterPayment := map[string]interface{}{
		"external_id": payload.Data.ExternalID,
		"provider":    payload.Provider,
		"status":      constant.TypeTransactionUnpaid,
	}

	payment, err := c.paymentRepo.GetByFilter(ctx, filterPayment)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCardPayment.paymentRepo.GetAllWithSoftDelete", zap.Error(err))
	}

	if err := payment.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCardPayment.payment.ValidateNotExist", zap.Error(err))
	}

	c.util.Logger.Info("Task.TaskEvtCardPayment.payment", zap.Any("Info", payment))

	filterTransaction := map[string]interface{}{
		"id":     payment.TransactionID,
		"status": constant.TypeTransactionUnpaid,
	}

	transaction, err := c.transactionRepo.GetByFilter(ctx, filterTransaction)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCardPayment.transactionRepo.GetAllWithSoftDelete", zap.Error(err))
	}

	c.util.Logger.Info("Task.TaskEvtCardPayment.transaction", zap.Any("Info", transaction))

	if err := transaction.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCardPayment.transaction.ValidateNotExist", zap.Error(err))
	}

	dataJson, err := json.Marshal(payload.Data)
	if err != nil {
		// Handle the error
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCardPayment.payment.json.Marshal", zap.Error(err))
	}

	dataJsonString := string(dataJson)

	eventParams := internal.CreateProviderPublishEventLog{
		Event:         &payload.Event,
		CompanyID:     payment.CompanyID,
		Provider:      payload.Provider,
		TransactionID: &payment.TransactionID,
		ExternalID:    &payment.ExternalID,
		Data:          &dataJsonString,
		Status:        constant.StatusActive,
	}

	_, err = c.providerPublishEventLogRepo.Create(ctx, eventParams)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCardPayment.providerPublishEventLogRepo.Create", zap.Error(err))
	}

	payloadChargePayment := internal.PayloadChargePayment{
		Status:      payload.Data.Status,
		ReferenceID: payload.Data.ExternalID,
		WebhookAt:   payload.Created,
	}

	err = c.TaskChargePayment(ctx, payment, transaction, payloadChargePayment)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskChargePayment", zap.Error(err))
	}

	return nil
}

func (c *Task) TaskChargePayment(ctx context.Context, payment internal.Payment, transaction internal.Transaction, payload internal.PayloadChargePayment) error {

	status := constant.StatusTransactionFailed

	switch payload.Status {

	case "SUCCEEDED":

		status = constant.StatusTransactionPaid
		filterMerchantPlatform := map[string]interface{}{
			"company_id": payment.CompanyID,
			"status":     constant.TypeActive,
		}

		mapMerchantPlatform := map[string]internal.MerchantPlatform{}
		merchantPlatforms, err := c.merchantPlatformRepo.GetAll(ctx, filterMerchantPlatform)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.merchantPlatformRepo.GetByFilter", zap.Error(err))
		}

		for _, merchantPlatform := range merchantPlatforms {
			mapMerchantPlatform[merchantPlatform.Name] = merchantPlatform
		}

		updateTransaction := internal.CreateTransaction{
			Status:         constant.StatusTransactionCompleted,
			PaymentMethod:  &payment.PaymentMethod,
			TotalPayment:   &payment.ChargeAmount,
			PGFee:          &payment.PGFee,
			PGRealFee:      &payment.PGRealFee,
			PGPlatformFee:  &payment.PGPlatformFee,
			PaymentType:    &payment.PaymentType,
			PaymentChannel: &payment.PaymentChannel,
		}

		c.util.Logger.Info("updateTransaction", zap.Any("updateTransaction", updateTransaction))

		transaction, err := c.transactionRepo.Update(ctx, transaction.ID, updateTransaction)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskChargePayment.transactionRepo.Update", zap.Error(err))
		}

		updatePayment := internal.CreatePayment{
			Status: constant.StatusTransactionPaid,
			PaidAt: payload.WebhookAt,
		}

		c.util.Logger.Info("updatePayment", zap.Any("updatePayment", updatePayment))

		payment, err = c.paymentRepo.Update(ctx, payment.ID, updatePayment)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskChargePayment.paymentRepo.Update", zap.Error(err))
		}

		if transaction.NetAmount > 0 && transaction.ProductType == constant.TypeFeatureSubcription {
			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  transaction.CompanyID,
				ProfileID:  transaction.ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnIncome,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnIncome),
				MerchantID:  transaction.MerchantID,
				SourceID:    transaction.ID,
				ProductType: transaction.ProductType,
				Amount:      transaction.NetAmount,
				Metadata:    transaction.Metadata,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
			}
		}

		//transfer
		if transaction.ThirdPartyFee > 0 && transaction.ProductType == constant.TypeFeatureVoucherGame {

			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  payment.CompanyID,
				ProfileID:  mapMerchantPlatform["UNIPLAY"].ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnThirdPartyFee,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnThirdPartyFee),
				MerchantID:  mapMerchantPlatform["UNIPLAY"].MerchantID,
				ProductType: transaction.ProductType,
				Amount:      transaction.ThirdPartyFee,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishTxnBalance", zap.Error(err))
			}
		}

		if transaction.ThirdPartyFee > 0 && transaction.ProductType == constant.TypeFeatureCoin {
			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  payment.CompanyID,
				ProfileID:  mapMerchantPlatform["MERCENARY"].ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnThirdPartyFee,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnThirdPartyFee),
				MerchantID:  mapMerchantPlatform["MERCENARY"].MerchantID,
				ProductType: transaction.ProductType,
				Amount:      transaction.ThirdPartyFee,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishTxnBalance", zap.Error(err))
			}
		}

		if transaction.TotalPlatformFee > 0 {

			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  payment.CompanyID,
				ProfileID:  mapMerchantPlatform["DIGITAL"].ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnPlatformFee,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnPlatformFee),
				MerchantID:  mapMerchantPlatform["DIGITAL"].MerchantID,
				ProductType: transaction.ProductType,
				Amount:      transaction.TotalPlatformFee,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishTxnBalance", zap.Error(err))
			}

		}

		if transaction.PGRealFee > 0 {

			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  payment.CompanyID,
				ProfileID:  mapMerchantPlatform["XENDIT"].ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnThirdPartyFee,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnThirdPartyFee),
				MerchantID:  mapMerchantPlatform["XENDIT"].MerchantID,
				ProductType: transaction.ProductType,
				Amount:      transaction.PGRealFee,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishTxnBalance", zap.Error(err))
			}
		}

		if transaction.PGPlatformFee > 0 {
			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  payment.CompanyID,
				ProfileID:  mapMerchantPlatform["PAYMENT"].ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnPlatformFee,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnPlatformFee),
				MerchantID:  mapMerchantPlatform["PAYMENT"].MerchantID,
				ProductType: transaction.ProductType,
				Amount:      transaction.PGPlatformFee,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishTxnBalance", zap.Error(err))
			}
		}

		if transaction.TotalReferrerFee > 0 {

			filterMerchant := map[string]interface{}{
				"profile_id": transaction.ReferrerID,
				"status":     constant.TypeActive,
			}

			c.util.Logger.Info("Task.TaskChargePayment.filterMerchant", zap.Any("filterMerchant", filterMerchant))

			merchant, err := c.merchantRepo.GetByFilter(ctx, filterMerchant)
			if err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.merchantRepo.GetByFilter", zap.Error(err))
			}

			c.util.Logger.Info("Task.TaskChargePayment.TaskChargePayment.Merchant", zap.Any("Info", merchant))

			if merchant.IsExist() {

				payloadTxnBalance := internal.PayloadTxnBalance{
					CompanyID:  payment.CompanyID,
					ProfileID:  merchant.ProfileID,
					TypeID:     transaction.ID,
					TypeObject: constant.TypeObjectTxn,
					Type:       constant.TypeTxnComission,
					SourceID:   transaction.MerchantID,
					Description: constant.ReverseTxnConstant(
						constant.TypeTxnComission),
					MerchantID:  merchant.ID,
					ProductType: transaction.ProductType,
					Amount:      transaction.TotalReferrerFee,
				}

				if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
					return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishTxnBalance", zap.Error(err))
				}

				//publish vg notification
				payloadVgCompleted := internal.PayloadDigitalPayCompleted{
					CompanyID:      payment.CompanyID,
					TransactionID:  transaction.ID,
					MerchantID:     merchant.ID,
					ProfileID:      merchant.ProfileID,
					ReferenceNo:    transaction.ReferenceNo,
					BuyerID:        transaction.BuyerID,
					BuyerName:      transaction.BuyerName,
					BuyerEmail:     transaction.BuyerEmail,
					Amount:         transaction.TotalReferrerFee,
					NetAmount:      transaction.TotalReferrerFee,
					PaymentMethod:  payment.PaymentMethod,
					PaymentChannel: payment.PaymentChannel,
					PaidAt:         payload.WebhookAt,
					Metadata:       transaction.Metadata,
				}

				if err := c.taskPublisher.PublishDigitalPayCompleted(ctx, payloadVgCompleted); err != nil {
					return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishDigitalPayCompleted", zap.Error(err))
				}

				//feature fee tts
				// find feature fee
				filterFeatureFee := map[string]interface{}{
					"company_id": payment.CompanyID,
					"name":       "tts",
					"status":     constant.TypeActive,
				}

				featureFee, err := c.featureFeeRepo.GetByFilter(ctx, filterFeatureFee)
				if err != nil {
					return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.featureFeeRepo.GetByFilter", zap.Error(err))
				}

				if err := featureFee.ValidateNotExist(); err != nil {
					return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.featureFee.ValidateNotExist", zap.Error(err))
				}

				payloadTxnBalance = internal.PayloadTxnBalance{
					CompanyID:  payment.CompanyID,
					ProfileID:  mapMerchantPlatform["DIGITAL"].ProfileID,
					TypeID:     transaction.ID,
					TypeObject: constant.TypeObjectTxn,
					Type:       constant.TypeTxnSubsidy,
					SourceID:   transaction.MerchantID,
					Description: constant.ReverseTxnConstant(
						constant.TypeTxnSubsidy),
					MerchantID:  mapMerchantPlatform["DIGITAL"].MerchantID,
					ProductType: transaction.ProductType,
					Amount:      transaction.TotalReferrerFee,
				}

				if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
					return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishTxnBalance", zap.Error(err))
				}

				payloadTxnBalance = internal.PayloadTxnBalance{
					CompanyID:  payment.CompanyID,
					ProfileID:  mapMerchantPlatform["TTS"].ProfileID,
					TypeID:     transaction.ID,
					TypeObject: constant.TypeObjectTxn,
					Type:       constant.TypeTxnFeatureFee,
					SourceID:   transaction.MerchantID,
					Description: constant.ReverseTxnConstant(
						constant.TypeTxnFeatureFee),
					MerchantID:  mapMerchantPlatform["TTS"].MerchantID,
					ProductType: transaction.ProductType,
					Amount:      featureFee.FeeValue,
				}

				if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
					return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskChargePayment.taskPublisher.PublishTxnBalance", zap.Error(err))
				}
			}

		}
	default:

		var err error

		status = constant.StatusTransactionFailed

		updatePayment := internal.CreatePayment{
			FailedAt: payload.WebhookAt,
			Status:   constant.StatusTransactionFailed,
		}

		payment, err = c.paymentRepo.Update(ctx, payment.ID, updatePayment)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskChargePayment.paymentRepo.Update", zap.Error(err))
		}
	}

	//publish to mercenary
	payloadPaymentState := internal.PayloadPaymentState{
		CompanyID:      transaction.CompanyID,
		ProfileID:      transaction.ProfileID,
		MerchantID:     transaction.MerchantID,
		TransactionID:  transaction.ID,
		PaymentID:      payment.ID,
		ReferenceNo:    transaction.ReferenceNo,
		BuyerID:        transaction.BuyerID,
		BuyerEmail:     transaction.BuyerEmail,
		Amount:         payment.Amount,
		ChargeAmount:   payment.ChargeAmount,
		PaymentMethod:  payment.PaymentMethod,
		PaymentChannel: payment.PaymentChannel,
		ChannelCode:    payment.ChannelCode,
		Provider:       payment.Provider,
		ProductType:    transaction.ProductType,
		State:          "webhook",
		PaidAt:         payment.PaidAt,
		FailedAt:       payment.FailedAt,
		Status:         status,
	}

	switch transaction.ProductType {
	case constant.TypeFeatureVoucherGame, constant.TypeFeatureCoin:
		if err := c.taskPublisher.PublishPaymentMercenaryState(ctx, payloadPaymentState); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.taskPublisher.PublishPaymentState", zap.Error(err))
		}
	case constant.TypeFeatureSubcription:
		if err := c.taskPublisher.PublishPaymentFameState(ctx, payloadPaymentState); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.taskPublisher.PublishPaymentState", zap.Error(err))
		}
	}

	return nil
}

func (c *Task) TaskEvtQrPaymentTip(ctx context.Context, payment internal.Payment, transaction internal.Transaction, payload internal.PayloadWebhookQRPayment) error {

	c.util.Logger.Info("Task.TaskEvtQrPayment.transaction", zap.Any("Info", transaction))

	if err := transaction.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPaymentTip.transaction.ValidateNotExist", zap.Error(err))
	}

	filterPaymentMethod := map[string]interface{}{
		"company_id": payment.CompanyID,
		"name":       payment.PaymentMethod,
	}

	paymentMethod, err := c.paymentMethodRepo.GetByFilter(ctx, filterPaymentMethod)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPaymentTip.paymentMethodRepo.GetByFilter", zap.Error(err))
	}

	if err := paymentMethod.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPaymentTip.paymentMethod.ValidateNotExist", zap.Error(err))
	}

	filterPayment := map[string]interface{}{
		"id":       payment.ID,
		"provider": payload.Provider,
	}

	if payload.Data.Status == "SUCCEEDED" {

		metadata := map[string]interface{}{
			"receipt_id": payload.Data.PaymentDetail.ReceiptID,
			"source":     payload.Data.PaymentDetail.Source,
		}

		updatePayment := internal.CreatePayment{
			Metadata: &metadata,
			Status:   constant.StatusTransactionPaid,
			PaidAt:   &payload.Data.Created,
		}

		_, err = c.paymentRepo.UpdateByFilter(ctx, filterPayment, updatePayment)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPaymentTip.paymentRepo.UpdateByFilter", zap.Error(err))
		}

		//calculate platform fee
		platformFeeType := ""
		platformFeeValue := 0.0
		totalPlatformFee := 0.0
		totalSubsidies := 0.0
		totalReferrerFee := 0.0
		totalMarketingFee := 0.0
		totalPaymentFee := 0.0
		totalFee := 0.0
		pgFee := 0.0

		amount := transaction.Amount
		netAmount := transaction.Amount
		enableSubsidy := false
		subsidyDescription := ""

		platformFee := transaction.Fee

		if platformFee.FeeValue == 0.0 {
			enableSubsidy = true
		}

		c.util.Logger.Info("Task.TaskEvtQrPayment", zap.Any("platformFees", platformFee))

		if enableSubsidy {
			//check subsidy
			if platformFee.PaymentMethod == payment.PaymentMethod {
				totalSubsidies = pkg.CalculatePercentage(amount, paymentMethod.PGFeeValue)
				subsidyDescription = "Subsidy " + payment.PaymentMethod
			}

			if transaction.TotalFeatureFee > 0 {
				totalSubsidies = totalSubsidies + transaction.TotalFeatureFee
				subsidyDescription = subsidyDescription + " + Feature Fee"
			}

		} else {

			if platformFee.PaymentMethod == payment.PaymentMethod {

				if platformFee.FeeType == "percentage" {
					totalFee = pkg.CalculatePercentage(amount, platformFee.FeeValue)
				} else if platformFee.FeeType == "fixed" {
					totalFee = platformFee.FeeValue
				}

				if platformFee.PlatformFeeType == "percentage" {
					totalPlatformFee = pkg.CalculatePercentage(amount, platformFee.PlatformFeeValue)
				} else if platformFeeType == "fixed" {
					totalPlatformFee = platformFee.PlatformFeeValue
				}

				netAmount = netAmount - totalFee

				if platformFee.ReferralFeeType == "percentage" {
					totalReferrerFee = pkg.CalculatePercentage(amount, platformFee.ReferralFeeValue)
				} else if platformFee.ReferralFeeType == "fixed" {
					totalReferrerFee = platformFee.ReferralFeeValue
				}

				if platformFee.MarkeringFeeType == "percentage" {
					totalMarketingFee = pkg.CalculatePercentage(amount, platformFee.MarkeringFeeValue)
				} else if platformFee.MarkeringFeeType == "fixed" {
					totalMarketingFee = platformFee.MarkeringFeeValue
				}

				if paymentMethod.PGFeeType == "percentage" {
					pgFee = pkg.CalculatePercentage(amount, paymentMethod.PGFeeValue)
				} else if paymentMethod.PGFeeType == "fixed" {
					pgFee = paymentMethod.PGFeeValue
				}
			}

			if transaction.TotalFeatureFee > 0 {
				netAmount = netAmount - transaction.TotalFeatureFee
			}

		}

		//update status transaction
		filterTransaction := map[string]interface{}{
			"id": payment.TransactionID,
		}

		updateTransaction := internal.CreateTransaction{
			Status:             constant.StatusTransactionCompleted,
			PaymentMethod:      &payment.PaymentMethod,
			PlatformFeeType:    &platformFeeType,
			PlatformFeeValue:   &platformFeeValue,
			TotalPlatformFee:   &totalPlatformFee,
			TotalSubsidyFee:    &totalSubsidies,
			TotalReferrerFee:   &totalReferrerFee,
			TotalMarketingFee:  &totalMarketingFee,
			TotalPaymentFee:    &totalPaymentFee,
			TotalFee:           &totalFee,
			PGFee:              &pgFee,
			DescriptionSubsidy: &subsidyDescription,
			NetAmount:          &netAmount,
		}

		c.util.Logger.Info("updateTransaction", zap.Any("updateTransaction", updateTransaction))

		transaction, err = c.transactionRepo.UpdateByFilter(ctx, filterTransaction, updateTransaction)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPaymentTip.transactionRepo.UpdateByFilter", zap.Error(err))
		}

		//publish event platform fee
		payloadPlatformFee := internal.PayloadPlatformFee{
			CompanyID:     payment.CompanyID,
			TransactionID: payment.TransactionID,
			MerchantID:    payment.MerchantID,
		}

		err = c.taskPublisher.PublishPlatformFee(ctx, payloadPlatformFee)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPaymentTip.taskPublisher.PublishPlatformFee", zap.Error(err))
		}

		//publish tip completed
		payloadTipCompleted := internal.PayloadTipCompleted{
			CompanyID:        transaction.CompanyID,
			ProfileID:        transaction.ProfileID,
			TransactionID:    transaction.ID,
			MerchantID:       transaction.MerchantID,
			ReferenceNo:      transaction.ReferenceNo,
			Amount:           transaction.Amount,
			NetAmount:        transaction.NetAmount,
			TotalPlatformFee: transaction.TotalFee,
			TotalFeatureFee:  transaction.TotalFeatureFee,
			PaymentMethod:    payment.PaymentMethod,
			PaymentChannel:   payment.PaymentChannel,
			PaidAt:           &payload.Data.Created,
		}

		err = c.taskPublisher.PublishTipCompleted(ctx, payloadTipCompleted)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPaymentTip.taskPublisher.PublishTipCompleted", zap.Error(err))
		}

	} else {

		updatePayment := internal.CreatePayment{
			FailedAt: &payload.Data.Created,
			Status:   constant.StatusTransactionFailed,
		}

		_, err = c.paymentRepo.UpdateByFilter(ctx, filterPayment, updatePayment)

		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtQrPaymentTip.paymentRepo.UpdateByFilter", zap.Error(err))
		}
	}

	return nil
}

func (c *Task) TaskEvtPlatformFee(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadPlatformFee

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtPlatformFee", payload))

	ctx := context.Background()

	filterMerchantPlatform := map[string]interface{}{
		"company_id": payload.CompanyID,
		"status":     constant.TypeActive,
	}

	mapMerchantPlatform := map[string]internal.MerchantPlatform{}
	merchantPlatforms, err := c.merchantPlatformRepo.GetAll(ctx, filterMerchantPlatform)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.merchantPlatformRepo.GetByFilter", zap.Error(err))
	}

	for _, merchantPlatform := range merchantPlatforms {
		mapMerchantPlatform[merchantPlatform.Name] = merchantPlatform
	}

	//find transaction
	filterTransaction := map[string]interface{}{
		"id":         payload.TransactionID,
		"company_id": payload.CompanyID,
		"status":     constant.TypeTransactionCompleted,
	}

	transaction, err := c.transactionRepo.GetByFilter(ctx, filterTransaction)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.transactionRepo.GetByFilter", zap.Error(err))
	}

	if err := transaction.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.transaction.ValidateNotExist", zap.Error(err))
	}

	//skip transfer if use voucher
	if transaction.UseVoucher {
		c.util.Logger.Info("skip transfer if use voucher")

		//check subsidy
		if transaction.TotalSubsidyFee > 0 {

			//publish event subsidy
			// payloadSubsidy := internal.PayloadSubsidy{
			// 	CompanyID:     transaction.CompanyID,
			// 	ProfileID:     transaction.ProfileID,
			// 	TransactionID: transaction.ID,
			// 	MerchantID:    transaction.MerchantID,
			// 	VoucherID:     transaction.VoucherID,
			// 	PGFee:         transaction.TotalPaymentFee,
			// 	FeatureFee:    transaction.TotalFeatureFee,
			// 	Amount:        transaction.TotalSubsidyFee,
			// 	Description:   transaction.DescriptionSubsidy,
			// }

			// err = c.taskPublisher.PublishSubsidy(ctx, payloadSubsidy)
			// if err != nil {
			// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishSubsidy", zap.Error(err))
			// }

			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  payload.CompanyID,
				ProfileID:  mapMerchantPlatform["TIPS"].ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnSubsidy,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnSubsidy),
				MerchantID:  mapMerchantPlatform["TIPS"].MerchantID,
				ProductType: constant.TypeFeatureTip,
				Amount:      transaction.TotalSubsidyFee,
				Metadata: map[string]interface{}{
					"total_feature_fee": transaction.TotalFeatureFee,
					"description":       transaction.DescriptionSubsidy,
					"voucher_id":        transaction.VoucherID,
					"voucher_code":      transaction.VoucherCode,
				},
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
			}

			return nil
		}

		fee := transaction.Fee

		// Publish transfer event
		amount := transaction.NetAmount
		// payloadTransfer := internal.PayloadTransfer{
		// 	CompanyID:     payload.CompanyID,
		// 	TransactionID: transaction.ID,
		// 	MerchantID:    transaction.MerchantID,
		// 	ProfileID:     transaction.ProfileID,
		// 	Type:          "income",
		// 	Description:   "income",
		// 	ProductType:   constant.TypeFeatureTip,
		// 	Amount:        amount,
		// 	Operation:     "ADD",
		// }
		// if err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer); err != nil {
		// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
		// }

		payloadTxnBalance := internal.PayloadTxnBalance{
			CompanyID:  payload.CompanyID,
			ProfileID:  transaction.ProfileID,
			TypeID:     transaction.ID,
			TypeObject: constant.TypeObjectTxn,
			Type:       constant.TypeTxnIncome,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnIncome),
			MerchantID:  transaction.MerchantID,
			ProductType: constant.TypeFeatureTip,
			SourceID:    transaction.ID,
			Amount:      amount,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
		}

		amountPlatform := transaction.Amount
		if fee.PlatformFeeType == "percentage" {
			amountPlatform = pkg.CalculatePercentage(transaction.Amount, fee.PlatformFeeValue)
		}

		// payloadTransfer = internal.PayloadTransfer{
		// 	CompanyID:     payload.CompanyID,
		// 	TransactionID: transaction.ID,
		// 	MerchantID:    mapMerchantPlatform["TIPS"].MerchantID,
		// 	ProfileID:     mapMerchantPlatform["TIPS"].ProfileID,
		// 	DownlineID:    transaction.MerchantID,
		// 	Type:          "platform_fee",
		// 	Description:   "platform fee",
		// 	ProductType:   constant.TypeFeatureTip,
		// 	Amount:        amountPlatform,
		// 	Operation:     "ADD",
		// }

		// if err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer); err != nil {
		// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
		// }

		payloadTxnBalance = internal.PayloadTxnBalance{
			CompanyID:  payload.CompanyID,
			ProfileID:  mapMerchantPlatform["TIPS"].ProfileID,
			TypeID:     transaction.ID,
			TypeObject: constant.TypeObjectTxn,
			Type:       constant.TypeTxnPlatformFee,
			SourceID:   transaction.MerchantID,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnPlatformFee),
			MerchantID:  mapMerchantPlatform["TIPS"].MerchantID,
			ProductType: constant.TypeFeatureTip,
			Amount:      amountPlatform,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
		}

		if transaction.TotalFeatureFee > 0 {
			//publish transfer event
			amount := transaction.TotalFeatureFee
			// payloadTransfer := internal.PayloadTransfer{
			// 	CompanyID:     payload.CompanyID,
			// 	TransactionID: transaction.ID,
			// 	MerchantID:    mapMerchantPlatform["TTS"].MerchantID,
			// 	ProfileID:     mapMerchantPlatform["TTS"].ProfileID,
			// 	DownlineID:    transaction.MerchantID,
			// 	Amount:        amount,
			// 	Description:   "feature fee",
			// 	Type:          "feature_fee",
			// 	ProductType:   constant.TypeFeatureTip,
			// 	Operation:     "ADD",
			// }

			// err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer)
			// if err != nil {
			// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
			// }

			payloadTxnBalance = internal.PayloadTxnBalance{
				CompanyID:  payload.CompanyID,
				ProfileID:  mapMerchantPlatform["TTS"].ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnFeatureFee,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnFeatureFee),
				MerchantID:  mapMerchantPlatform["TTS"].MerchantID,
				ProductType: constant.TypeFeatureTip,
				Amount:      amount,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
			}
		}

		if transaction.PGFee > 0 {
			//publish transfer event
			amount := transaction.PGFee
			// payloadTransfer := internal.PayloadTransfer{
			// 	CompanyID:     payload.CompanyID,
			// 	TransactionID: transaction.ID,
			// 	MerchantID:    mapMerchantPlatform["XENDIT"].MerchantID,
			// 	ProfileID:     mapMerchantPlatform["XENDIT"].ProfileID,
			// 	DownlineID:    transaction.MerchantID,
			// 	Amount:        amount,
			// 	Description:   "payment gateway fee",
			// 	Type:          "pg_fee",
			// 	ProductType:   constant.TypeFeatureTip,
			// 	Operation:     "ADD",
			// }

			// err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer)
			// if err != nil {
			// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
			// }

			payloadTxnBalance = internal.PayloadTxnBalance{
				CompanyID:  payload.CompanyID,
				ProfileID:  mapMerchantPlatform["XENDIT"].ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnThirdPartyFee,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnThirdPartyFee),
				MerchantID:  mapMerchantPlatform["XENDIT"].MerchantID,
				ProductType: constant.TypeFeatureTip,
				Amount:      amount,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
			}

		}

		return nil
	}

	if transaction.NetAmount > 0 {
		// payloadTransfer := internal.PayloadTransfer{
		// 	CompanyID:     payload.CompanyID,
		// 	TransactionID: transaction.ID,
		// 	MerchantID:    transaction.MerchantID,
		// 	ProfileID:     transaction.ProfileID,
		// 	Type:          "income",
		// 	Description:   "income",
		// 	ProductType:   constant.TypeFeatureTip,
		// 	Amount:        transaction.NetAmount,
		// 	Operation:     "ADD",
		// }

		// if err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer); err != nil {
		// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
		// }

		payloadTxnBalance := internal.PayloadTxnBalance{
			CompanyID:  payload.CompanyID,
			ProfileID:  transaction.ProfileID,
			TypeID:     transaction.ID,
			TypeObject: constant.TypeObjectTxn,
			Type:       constant.TypeTxnIncome,
			SourceID:   transaction.ID,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnIncome),
			MerchantID:  transaction.MerchantID,
			ProductType: constant.TypeFeatureTip,
			Amount:      transaction.NetAmount,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
		}
	}

	if transaction.TotalPlatformFee > 0 {
		//publish transfer event
		amount := transaction.TotalPlatformFee
		// payloadTransfer := internal.PayloadTransfer{
		// 	CompanyID:     payload.CompanyID,
		// 	TransactionID: transaction.ID,
		// 	MerchantID:    mapMerchantPlatform["TIPS"].MerchantID,
		// 	ProfileID:     mapMerchantPlatform["TIPS"].ProfileID,
		// 	DownlineID:    transaction.MerchantID,
		// 	Type:          "platform_fee",
		// 	Description:   "platform fee",
		// 	ProductType:   constant.TypeFeatureTip,
		// 	Amount:        amount,
		// 	Operation:     "ADD",
		// }

		// if err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer); err != nil {
		// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
		// }

		payloadTxnBalance := internal.PayloadTxnBalance{
			CompanyID:  payload.CompanyID,
			ProfileID:  mapMerchantPlatform["TIPS"].ProfileID,
			TypeID:     transaction.ID,
			TypeObject: constant.TypeObjectTxn,
			Type:       constant.TypeTxnPlatformFee,
			SourceID:   transaction.MerchantID,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnPlatformFee),
			MerchantID:  mapMerchantPlatform["TIPS"].MerchantID,
			ProductType: constant.TypeFeatureTip,
			Amount:      amount,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
		}
	}

	if transaction.TotalReferrerFee > 0 {

		filterMerchant := map[string]interface{}{
			"profile_id": transaction.ReferrerID,
			"status":     constant.TypeActive,
		}

		merchant, err := c.merchantRepo.GetByFilter(ctx, filterMerchant)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.merchantRepo.GetByFilter", zap.Error(err))
		}

		c.util.Logger.Info("Task.TaskEvtPlatformFee.TotalReferrerFee.Merchant", zap.Any("Info", merchant))

		if merchant.IsExist() {
			//publish transfer event
			amount := transaction.TotalReferrerFee
			// payloadTransfer := internal.PayloadTransfer{
			// 	CompanyID:     payload.CompanyID,
			// 	TransactionID: transaction.ID,
			// 	MerchantID:    merchant.ID,
			// 	DownlineID:    transaction.MerchantID,
			// 	Type:          "referral_fee",
			// 	Description:   "commission referral fee",
			// 	ProductType:   constant.TypeFeatureTip,
			// 	Amount:        amount,
			// 	Operation:     "ADD",
			// }

			// if err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer); err != nil {
			// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
			// }

			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  payload.CompanyID,
				ProfileID:  merchant.ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnComission,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnComission),
				MerchantID:  merchant.ID,
				ProductType: constant.TypeFeatureTip,
				Amount:      amount,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
			}
		}

	}

	if transaction.TotalMarketingFee > 0 {

		filterMerchant := map[string]interface{}{
			"profile_id": transaction.MarketingID,
			"status":     constant.TypeActive,
		}

		merchant, err := c.merchantRepo.GetByFilter(ctx, filterMerchant)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.merchantRepo.GetByFilter", zap.Error(err))
		}

		c.util.Logger.Info("Task.TaskEvtPlatformFee.TotalMarketingFee.Merchant", zap.Any("Info", merchant))

		if merchant.IsExist() {

			amount := transaction.TotalMarketingFee
			// payloadTransfer := internal.PayloadTransfer{
			// 	CompanyID:     payload.CompanyID,
			// 	TransactionID: transaction.ID,
			// 	MerchantID:    merchant.ID,
			// 	DownlineID:    transaction.MerchantID,
			// 	Amount:        amount,
			// 	Description:   "marketing fee",
			// 	Type:          "marketing_fee",
			// 	ProductType:   constant.TypeFeatureTip,
			// 	Operation:     "ADD",
			// }
			// if err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer); err != nil {
			// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
			// }

			payloadTxnBalance := internal.PayloadTxnBalance{
				CompanyID:  payload.CompanyID,
				ProfileID:  merchant.ProfileID,
				TypeID:     transaction.ID,
				TypeObject: constant.TypeObjectTxn,
				Type:       constant.TypeTxnComission,
				SourceID:   transaction.MerchantID,
				Description: constant.ReverseTxnConstant(
					constant.TypeTxnComission),
				MerchantID:  merchant.ID,
				ProductType: constant.TypeFeatureTip,
				Amount:      amount,
			}

			if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
				return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
			}
		}
	}

	if transaction.TotalFeatureFee > 0 {
		//publish transfer event
		amount := transaction.TotalFeatureFee
		// payloadTransfer := internal.PayloadTransfer{
		// 	CompanyID:     payload.CompanyID,
		// 	TransactionID: transaction.ID,
		// 	MerchantID:    mapMerchantPlatform["TTS"].MerchantID,
		// 	ProfileID:     mapMerchantPlatform["TTS"].ProfileID,
		// 	DownlineID:    transaction.MerchantID,
		// 	Amount:        amount,
		// 	Description:   "feature fee",
		// 	Type:          "feature_fee",
		// 	ProductType:   constant.TypeFeatureTip,
		// 	Operation:     "ADD",
		// }

		// err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer)
		// if err != nil {
		// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
		// }

		payloadTxnBalance := internal.PayloadTxnBalance{
			CompanyID:  payload.CompanyID,
			ProfileID:  mapMerchantPlatform["TTS"].ProfileID,
			TypeID:     transaction.ID,
			TypeObject: constant.TypeObjectTxn,
			Type:       constant.TypeTxnFeatureFee,
			SourceID:   transaction.MerchantID,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnFeatureFee),
			MerchantID:  mapMerchantPlatform["TTS"].MerchantID,
			ProductType: constant.TypeFeatureTip,
			Amount:      amount,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
		}
	}

	if transaction.PGFee > 0 {
		//publish transfer event
		amount := transaction.PGFee
		// payloadTransfer := internal.PayloadTransfer{
		// 	CompanyID:     payload.CompanyID,
		// 	TransactionID: transaction.ID,
		// 	MerchantID:    mapMerchantPlatform["XENDIT"].MerchantID,
		// 	ProfileID:     mapMerchantPlatform["XENDIT"].ProfileID,
		// 	DownlineID:    transaction.MerchantID,
		// 	Amount:        amount,
		// 	Description:   "payment gateway fee",
		// 	Type:          "pg_fee",
		// 	ProductType:   constant.TypeFeatureTip,
		// 	Operation:     "ADD",
		// }

		// err := c.taskPublisher.PublishTransfer(ctx, payloadTransfer)
		// if err != nil {
		// 	return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTransfer", zap.Error(err))
		// }

		payloadTxnBalance := internal.PayloadTxnBalance{
			CompanyID:  payload.CompanyID,
			ProfileID:  mapMerchantPlatform["XENDIT"].ProfileID,
			TypeID:     transaction.ID,
			TypeObject: constant.TypeObjectTxn,
			Type:       constant.TypeTxnThirdPartyFee,
			SourceID:   transaction.MerchantID,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnThirdPartyFee),
			MerchantID:  mapMerchantPlatform["XENDIT"].MerchantID,
			ProductType: constant.TypeFeatureTip,
			Amount:      amount,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.taskPublisher.PublishTxnBalance", zap.Error(err))
		}

	}
	return nil
}

func (c *Task) TaskEvtTransfer(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadTransfer

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtTransfer", payload))

	ctx := context.Background()

	if payload.TransactionID != "" {
		//check if transaction exist
		filterTransfer := map[string]interface{}{
			"merchant_id":    payload.MerchantID,
			"transaction_id": payload.TransactionID,
			"type":           payload.Type,
		}

		c.util.Logger.Info("TaskEvtTransfer.filterTransfer", zap.Any("filterTransfer", filterTransfer))

		transfer, err := c.transferRepo.GetByFilter(ctx, filterTransfer)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtTransfer.transferRepo.GetByFilter", zap.Error(err))
		}

		c.util.Logger.Info("TaskEvtTransfer.transfer", zap.Any("transfer", transfer))

		if err := transfer.ValidateExist(); err != nil {
			return nil
		}
	}

	transferParams := internal.CreateTransfer{
		CompanyID:      payload.CompanyID,
		ProfileID:      payload.ProfileID,
		TransactionID:  payload.TransactionID,
		DisbursementID: payload.DisbursementID,
		DownlineID:     payload.DownlineID,
		MerchantID:     payload.MerchantID,
		Type:           &payload.Type,
		Description:    &payload.Description,
		Amount:         &payload.Amount,
		ProductType:    &payload.ProductType,
		Operation:      &payload.Operation,
	}

	_, err = c.transferRepo.Create(ctx, transferParams)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtTransfer.transferRepo.Create", zap.Error(err))
	}

	//update balance
	filterMerchant := map[string]interface{}{
		"id": payload.MerchantID,
	}

	c.util.Logger.Info("Task.TaskEvtTransfer.filterMerchant", zap.Any("filterMerchant", filterMerchant))

	merchant, err := c.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtTransfer.merchantRepo.GetByFilter", zap.Error(err))
	}

	c.util.Logger.Info("Task.TaskEvtTransfer.merchant", zap.Any("Info", merchant))

	newBalance := merchant.Balance
	newSettledBalance := merchant.SettledBalance

	switch payload.Operation {

	case "ADD":

		newBalance = merchant.Balance + payload.Amount
		newSettledBalance = merchant.SettledBalance + payload.Amount

	case "SUB":

		newBalance = merchant.Balance - payload.Amount
		newSettledBalance = merchant.SettledBalance - payload.Amount

	}

	updateParams := internal.CreateMerchant{
		Balance:        &newBalance,
		SettledBalance: &newSettledBalance,
	}

	_, err = c.merchantRepo.Update(ctx, merchant.ID, updateParams)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtTransfer.merchantRepo.Update", zap.Error(err))
	}

	return nil
}

func (c *Task) TaskEvtTxnBalance(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadTxnBalance

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtTransfer", payload))

	ctx := context.Background()

	var params = []internal.CreateBalanceTransaction{

		{
			MerchantID:  payload.MerchantID,
			Type:        &payload.Type,
			TypeObject:  &payload.TypeObject,
			TypeID:      &payload.TypeID,
			SourceID:    &payload.SourceID,
			Description: &payload.Description,
			Amount:      &payload.Amount,
			ProductType: &payload.ProductType,
			Metadata:    &payload.Metadata,
		},
	}

	nexusData := riot.Nexus{
		Server: &riot.Server{
			Webservice: &riot.NexusWebservice{
				ID: payload.CompanyID,
			},
			Profile: &riot.NexusProfile{
				ID:       payload.ProfileID,
				ParentID: payload.CompanyID,
			},
		},
	}

	c.util.Logger.Info("Task.TaskEvtTxnBalance.Data", zap.Any("Data", params), zap.Any("Nexus", nexusData))

	if _, err := c.txnBalanceSvc.Create(ctx, params, nexusData); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtTxnBalance.txnBalanceSvc.Create", zap.Error(err))
	}

	return nil
}

func (c *Task) TaskEvtCoinBalance(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadCoinBalance

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtTransfer", payload))

	ctx := context.Background()

	var params = []internal.CreateCoinTransaction{

		{
			CoinID:      payload.CoinID,
			UserID:      payload.UserID,
			Type:        &payload.Type,
			TypeID:      &payload.TypeID,
			SourceID:    &payload.SourceID,
			Description: &payload.Description,
			Amount:      &payload.Amount,
			Metadata:    &payload.Metadata,
		},
	}

	nexusData := riot.Nexus{
		Server: &riot.Server{
			Webservice: &riot.NexusWebservice{
				ID: payload.CompanyID,
			},
			Profile: &riot.NexusProfile{
				ID:       payload.ProfileID,
				ParentID: payload.CompanyID,
				Role:     riot.RoleCustomer,
			},
		},
	}

	if _, err := c.txnCoinSvc.Create(ctx, params, nexusData); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtTxnBalance.txnBalanceSvc.Create", zap.Error(err))
	}

	return nil
}

func (c *Task) TaskEvtSubsidy(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadSubsidy

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtSubsidy", payload))

	ctx := context.Background()

	filterMerchant := map[string]interface{}{
		"id": c.util.Conf.Get("MERCHANT_MAIN"),
	}

	merchant, err := c.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtTransfer.merchantRepo.GetByFilter", zap.Error(err))
	}

	subsidyParams := internal.CreateSubsidy{
		CompanyID:     merchant.CompanyID,
		ProfileID:     merchant.ProfileID,
		TransactionID: payload.TransactionID,
		MerchantID:    merchant.ID,
		VoucherID:     &payload.VoucherID,
		Description:   &payload.Description,
		Amount:        &payload.Amount,
		PGFee:         &payload.PGFee,
		FeatureFee:    &payload.FeatureFee,
	}

	subsidy, err := c.subsidyRepo.Create(ctx, subsidyParams)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtSubsidy.transferRepo.Create", zap.Error(err))
	}

	//update balance

	newBalance := merchant.Balance - subsidy.Amount
	settledBalance := merchant.SettledBalance - subsidy.Amount

	updateParams := internal.CreateMerchant{
		Balance:        &newBalance,
		SettledBalance: &settledBalance,
	}

	_, err = c.merchantRepo.Update(ctx, merchant.ID, updateParams)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtSubsidy.merchantRepo.Update", zap.Error(err))
	}

	return nil
}

func (c *Task) TaskEvtWebhookDisbursement(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadWebhookDisbursement

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	ctx := context.Background()

	filterDisburse := map[string]interface{}{
		"disbursement_no": payload.Data.ExternalID,
		"status":          constant.TypeTransactionInProgress,
	}

	disburse, err := c.disburseRepo.GetByFilter(ctx, filterDisburse)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.disburseRepo.GetByFilter", zap.Error(err))
	}

	if err := disburse.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.disburseRepo.disburse.ValidateNotExist", zap.Error(err))
	}

	//filter merchant
	filterMerchant := map[string]interface{}{
		"id":         disburse.MerchantID,
		"company_id": disburse.CompanyID,
	}

	merchant, err := c.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.merchantRepo.GetByFilter", zap.Error(err))
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.merchantRepo.merchant.ValidateNotExist", zap.Error(err))
	}

	filterMerchantPlatform := map[string]interface{}{
		"company_id": disburse.CompanyID,
		"status":     constant.TypeActive,
	}

	mapMerchantPlatform := map[string]internal.MerchantPlatform{}
	merchantPlatforms, err := c.merchantPlatformRepo.GetAll(ctx, filterMerchantPlatform)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "TaskEvtPlatformFee.merchantPlatformRepo.GetByFilter", zap.Error(err))
	}

	for _, merchantPlatform := range merchantPlatforms {
		mapMerchantPlatform[merchantPlatform.Name] = merchantPlatform
	}

	dataJson, err := json.Marshal(payload.Data)
	if err != nil {
		// Handle the error
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.payment.json.Marshal", zap.Error(err))
	}

	dataJsonString := string(dataJson)

	eventParams := internal.CreateProviderPublishEventLog{
		Event:         &payload.Event,
		CompanyID:     disburse.CompanyID,
		Provider:      payload.Provider,
		TransactionID: nil,
		ExternalID:    &disburse.ExternalID,
		Data:          &dataJsonString,
		Status:        constant.StatusActive,
	}

	_, err = c.providerPublishEventLogRepo.Create(ctx, eventParams)
	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.providerPublishEventLogRepo.Create", zap.Error(err))
	}

	switch payload.Data.Status {
	case "FAILED":
		//update error disburse
		updateDisburseParams := internal.CreateDisbursement{
			FailedAt:       &payload.Data.Created,
			ErrorMessage:   &payload.Data.FailureCode,
			IsInstant:      &payload.Data.IsInstant,
			Status:         constant.StatusTransactionFailed,
			ProviderStatus: &payload.Data.Status,
		}

		resDisburse, err := c.disburseRepo.Update(ctx, disburse.ID, updateDisburseParams)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.disburseRepo.Update", zap.Error(err))
		}

		//rollback merchant balance
		payloadTxnBalance := internal.PayloadTxnBalance{
			CompanyID:  merchant.CompanyID,
			ProfileID:  merchant.ProfileID,
			TypeID:     resDisburse.ID,
			TypeObject: constant.TypeObjectDisb,
			Type:       constant.TypeTxnDisburseFail,
			SourceID:   resDisburse.MerchantID,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnDisburseFail),
			MerchantID:  merchant.ID,
			ProductType: constant.TypeFeatureDisb,
			Amount:      disburse.Amount,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.taskPublisher.PublishTxnBalance", zap.Error(err))
		}

		payloadTxnBalance = internal.PayloadTxnBalance{
			CompanyID:  merchant.CompanyID,
			ProfileID:  mapMerchantPlatform["DISBURSEMENT"].ProfileID,
			TypeID:     resDisburse.ID,
			TypeObject: constant.TypeObjectDisb,
			Type:       constant.TypeTxnDisburseFeeFail,
			SourceID:   resDisburse.MerchantID,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnDisburseFeeFail),
			MerchantID:  mapMerchantPlatform["DISBURSEMENT"].MerchantID,
			ProductType: constant.TypeFeatureDisb,
			Amount:      disburse.PlatformFee,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.taskPublisher.PublishTxnBalance", zap.Error(err))
		}

		payloadTxnBalance = internal.PayloadTxnBalance{
			CompanyID:  merchant.CompanyID,
			ProfileID:  mapMerchantPlatform["XENDIT"].ProfileID,
			TypeID:     resDisburse.ID,
			TypeObject: constant.TypeObjectDisb,
			Type:       constant.TypeTxnDisburseFeeFail,
			SourceID:   resDisburse.MerchantID,
			Description: constant.ReverseTxnConstant(
				constant.TypeTxnDisburseFeeFail),
			MerchantID:  mapMerchantPlatform["XENDIT"].MerchantID,
			ProductType: constant.TypeFeatureDisb,
			Amount:      disburse.NetAmount + disburse.PGFee,
		}

		if err := c.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.taskPublisher.PublishTxnBalance", zap.Error(err))
		}

		//record disbursement log
		disbursementLogParams := internal.CreateDisbursementLog{
			CompanyID:              resDisburse.CompanyID,
			ProfileID:              resDisburse.ProfileID,
			MerchantID:             resDisburse.MerchantID,
			MerchantBankID:         resDisburse.MerchantBankID,
			DisbursementID:         resDisburse.ID,
			DisbursementNo:         &resDisburse.DisbursementNo,
			ProviderDisbursementID: &resDisburse.ProviderDisbursementID,
			Provider:               &resDisburse.Provider,
			ExternalID:             &resDisburse.ExternalID,
			Status:                 resDisburse.Status,
			ErrorMessage:           &resDisburse.ErrorMessage,
			ProviderStatus:         &resDisburse.ProviderStatus,
		}

		_, err = c.disburseLogRepo.Create(ctx, disbursementLogParams)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.disburseLogRepo.Create", zap.Error(err))
		}

	case "COMPLETED":

		//update success disburse
		updateDisburseParams := internal.CreateDisbursement{
			IsInstant:      &payload.Data.IsInstant,
			Status:         constant.StatusTransactionCompleted,
			DisbursedAt:    &payload.Data.Created,
			ProviderStatus: &payload.Data.Status,
		}

		resDisburse, err := c.disburseRepo.Update(ctx, disburse.ID, updateDisburseParams)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.disburseRepo.Update", zap.Error(err))
		}

		//record disbursement log
		disbursementLogParams := internal.CreateDisbursementLog{
			CompanyID:              resDisburse.CompanyID,
			ProfileID:              resDisburse.ProfileID,
			MerchantID:             resDisburse.MerchantID,
			MerchantBankID:         resDisburse.MerchantBankID,
			DisbursementID:         resDisburse.ID,
			DisbursementNo:         &resDisburse.DisbursementNo,
			ProviderDisbursementID: &resDisburse.ProviderDisbursementID,
			Provider:               &resDisburse.Provider,
			ExternalID:             &resDisburse.ExternalID,
			Status:                 resDisburse.Status,
			ErrorMessage:           &resDisburse.ErrorMessage,
			ProviderStatus:         &resDisburse.ProviderStatus,
		}

		_, err = c.disburseLogRepo.Create(ctx, disbursementLogParams)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.disburseLogRepo.Create", zap.Error(err))
		}

		//update total balance
		totalDisburse := merchant.TotalDisburse + disburse.Amount
		updateMerchantParams := internal.CreateMerchant{
			TotalDisburse: &totalDisburse,
		}

		_, err = c.merchantRepo.Update(ctx, merchant.ID, updateMerchantParams)
		if err != nil {
			return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtDisbursement.merchantRepo.Update", zap.Error(err))
		}
	}

	return nil
}

func (c *Task) TaskEvtCronPopulateDisbursement(route string, evt r.PayloadEvent) error {

	c.util.Logger.Info("Task.TaskEvtCronPopulateDisbursement", zap.Any("Info", evt))

	page := 1
	limit := 10
	ctx := context.Background()

	// Function to handle pagination and publishing
	publishDisbursementConfiguration := func(page, limit int) (*internal.DisbursementConfigurationPagin, error) {

		paginOpt := mongorm.FilterOpt{
			Page:     page,
			PageSize: limit,
			Filter: map[string]interface{}{
				"status":        constant.TypeActive,
				"auto_disburse": true,
			},
		}

		disburseConfigs, err := c.disburseConfigRepo.GetAllWithPagination(ctx, paginOpt)
		if err != nil {
			return nil, riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCronInsightSocials.repo.GetAllWithPagination")
		}

		for _, disburseConfig := range disburseConfigs.Records {

			c.util.Logger.Info("Task.TaskEvtCronPopulateDisbursement", zap.Any("disburseConfig", disburseConfig))

			filterMerchant := map[string]interface{}{
				"id": disburseConfig.MerchantID,
			}

			merchant, err := c.merchantRepo.GetByFilter(ctx, filterMerchant)
			if err != nil {
				return nil, riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCronInsightSocials.repo.GetOne")
			}

			if err := merchant.ValidateNotExist(); err != nil {
				c.util.Logger.Error("Task.TaskEvtCronPopulateDisbursement.merchant.ValidateNotExist", zap.Error(err))
				continue
			}

			if disburseConfig.MerchantBankID == "" {
				c.util.Logger.Error("Task.TaskEvtCronPopulateDisbursement.merchantBankID is empty", zap.Error(err))
				continue
			}

			if disburseConfig.TotalAmount < merchant.Balance {
				c.util.Logger.Error("Task.TaskEvtCronPopulateDisbursement.totalAmount is less than merchantBalance", zap.Error(err))
				continue
			}

			filterMerchantBank := map[string]interface{}{
				"id": disburseConfig.MerchantBankID,
			}

			merchantBank, err := c.merchantBankRepo.GetByFilter(ctx, filterMerchantBank)
			if err != nil {
				return nil, riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCronPopulateDisbursement.merchantBankRepo.GetByFilter")
			}

			if err := merchantBank.ValidateNotExist(); err != nil {
				c.util.Logger.Error("Task.TaskEvtCronPopulateDisbursement.merchantBank.ValidateNotExist)", zap.Error(err))
				continue
			}

			payload := internal.PayloadAutoDisburse{
				ProfileID:      disburseConfig.ProfileID,
				CompanyID:      disburseConfig.CompanyID,
				MerchantID:     disburseConfig.MerchantID,
				MerchantBankID: disburseConfig.MerchantBankID,
				AccountNumber:  merchantBank.BankAccountNumber,
				Amount:         disburseConfig.TotalAmount,
				Provider:       "xendit",
				Description:    "Auto Disbursement to " + merchant.Name,
			}

			if err := c.taskPublisher.PublishAutoDisburse(ctx, payload); err != nil {
				return nil, riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtCronPopulateDisbursement.taskPublisher.PublishAutoDisburse")
			}

		}

		return &disburseConfigs, nil
	}

	// Loop through all pages
	for {

		c.util.Logger.Info("Fetching page", zap.Int("page", page))

		disburseConfigs, err := publishDisbursementConfiguration(page, limit)
		if err != nil {
			return err
		}

		// Stop if no more records are available
		if len(disburseConfigs.Records) == 0 {
			c.util.Logger.Info("No more integrations to process. Exiting.")
			break
		}

		// Proceed to the next page
		page++
	}

	return nil
}

func (c *Task) TaskEvtAutoDisbursement(route string, evt r.PayloadEvent) error {

	var payload internal.PayloadAutoDisburse

	err := util.ConvertToStruct(evt.Data, &payload)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	c.util.Logger.Info(fmt.Sprintf("%s.%s", route, "success"), zap.Any("TaskEvtAutoDisbursement", payload))

	ctx := context.Background()
	isAuto := true
	paramsDisburse := internal.CreateDisbursement{
		MerchantID:     payload.MerchantID,
		MerchantBankID: payload.MerchantBankID,
		AccountNumber:  &payload.AccountNumber,
		Provider:       &payload.Provider,
		Description:    &payload.Description,
		Amount:         &payload.Amount,
		IsAuto:         &isAuto,
	}

	nexusData := riot.Nexus{
		Server: &riot.Server{
			Profile: &riot.NexusProfile{
				ID:       payload.ProfileID,
				ParentID: payload.CompanyID,
			},
		},
	}

	_, err = c.disburseSvc.Create(ctx, paramsDisburse, nexusData)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtAutoDisbursement.disburseSvc.Create", zap.Error(err))
	}

	return nil
}
