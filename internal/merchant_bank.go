package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type MerchantBank struct {
	ID                    string            `json:"_id"`
	CompanyID             string            `json:"company_id"`
	ProfileID             string            `json:"profile_id"`
	MerchantID            string            `json:"merchant_id"`
	BankID                string            `json:"bank_id"`
	BankName              string            `json:"bank_name"`
	BankBranch            string            `json:"bank_branch"`
	BankAccountNumber     string            `json:"bank_account_number"`
	BankAccountHolderName string            `json:"bank_account_holder_name"`
	BankDetail            Bank              `json:"bank_detail"`
	Metadata              map[string]string `json:"metadata"`
	IsDefault             bool              `json:"is_default"`
	Status                string            `json:"status"`
	CreatedAt             *time.Time        `json:"created_at"`
	UpdatedAt             *time.Time        `json:"updated_at"`
}

type MerchantBankPagin struct {
	Limit        int            `json:"limit"`
	Page         int            `json:"page"`
	Sort         string         `json:"sort"`
	TotalRecords int            `json:"total_records"`
	TotalPages   int            `json:"total_pages"`
	Records      []MerchantBank `json:"records"`
}

func (b MerchantBank) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b MerchantBank) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b MerchantBank) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *MerchantBank) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateMerchantBank struct {
	CompanyID             string             `json:"company_id,omitempty"`
	ProfileID             string             `json:"profile_id,omitempty"`
	MerchantID            string             `json:"merchant_id,omitempty"`
	BankID                string             `json:"bank_id,omitempty"`
	BankCode              string             `json:"bank_code,omitempty"`
	BankName              *string            `json:"bank_name,omitempty"`
	BankBranch            *string            `json:"bank_branch,omitempty"`
	BankAccountNumber     *string            `json:"bank_account_number,omitempty"`
	BankAccountHolderName *string            `json:"bank_account_holder_name,omitempty"`
	BankDetail            *Bank              `json:"bank_detail,omitempty"`
	Metadata              *map[string]string `json:"metadata,omitempty"`
	IsDefault             *bool              `json:"is_default,omitempty"`
	Status                string             `json:"status,omitempty"`
}

func (ct CreateMerchantBank) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.BankAccountNumber, validation.Required),
		validation.Field(&ct.BankAccountHolderName, validation.Required),
		validation.Field(&ct.BankCode, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
