package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Coin struct {
	ID          string            `json:"_id"`
	CompanyID   string            `json:"company_id"`
	ProfileID   string            `json:"profile_id"`
	LoginID     string            `json:"login_id"`
	Balance     float64           `json:"balance"`
	Description string            `json:"description"`
	Metadata    map[string]string `json:"metadata"`
	Status      string            `json:"status"`
	CreatedAt   *time.Time        `json:"created_at"`
	UpdatedAt   *time.Time        `json:"updated_at"`
}

type CoinProtected struct {
	ID          string            `json:"_id"`
	CompanyID   string            `json:"company_id"`
	ProfileID   string            `json:"profile_id"`
	LoginID     string            `json:"login_id"`
	Balance     float64           `json:"balance"`
	Description string            `json:"description"`
	Metadata    map[string]string `json:"metadata"`
	Status      string            `json:"status"`
	CreatedAt   *time.Time        `json:"created_at"`
	UpdatedAt   *time.Time        `json:"updated_at"`
}

type CoinPagin struct {
	Limit        int    `json:"limit"`
	Page         int    `json:"page"`
	Sort         string `json:"sort"`
	TotalRecords int    `json:"total_records"`
	TotalPages   int    `json:"total_pages"`
	Records      []Coin `json:"records"`
}

func (b Coin) IsExist() bool {
	return b.ID != ""
}

func (b Coin) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Coin) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ID)),
		}
	}
	return nil
}

func (b Coin) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b Coin) ValidateUserNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			"User": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ProfileID)),
		}
	}
	return nil
}

func (b Coin) ValidateBalance(newBalance float64) error {
	if newBalance < 0 {
		return validation.Errors{
			"Balance": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("coin %.2f is not sufficient", b.Balance)),
		}
	}
	return nil
}

func (b Coin) ValidateSourceNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			"Source": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ProfileID)),
		}
	}
	return nil
}

func (b *Coin) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateCoin struct {
	CompanyID   string             `json:"company_id,omitempty"`
	ProfileID   string             `json:"profile_id,omitempty"`
	LoginID     *string            `json:"login_id,omitempty"`
	Balance     *float64           `json:"balance,omitempty"`
	Description *string            `json:"description,omitempty"`
	Metadata    *map[string]string `json:"metadata,omitempty"`
	Status      string             `json:"status,omitempty"`
}

func (ct CreateCoin) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.Balance, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type CreateCoinDisburse struct {
	CompanyID   string                 `json:"company_id,omitempty"`
	ProfileID   string                 `json:"profile_id,omitempty"`
	Amount      float64                `json:"amount,omitempty"`
	Description string                 `json:"description,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Status      string                 `json:"status,omitempty"`
}

func (ct CreateCoinDisburse) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.Amount, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

// validate coin balance
func (c Coin) ValidateDisburseBalance(amount float64) error {
	if c.Balance < amount {
		return validation.Errors{
			util.GetStructName(c): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%f balance is not enough.", c.Balance)),
		}
	}
	return nil
}

type CoinDisburse struct {
	CompanyID      string             `json:"company_id"`
	ProfileID      string             `json:"profile_id"`
	CurrentBalance *float64           `json:"current_balance"`
	Metadata       *map[string]string `json:"metadata"`
	Status         string             `json:"status"`
}
