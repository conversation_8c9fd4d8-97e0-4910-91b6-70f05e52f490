package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type BalanceTransaction struct {
	ID            string                 `json:"_id"`
	CompanyID     string                 `json:"company_id"`
	ProfileID     string                 `json:"profile_id"`
	MerchantID    string                 `json:"merchant_id"`
	Type          int                    `json:"type"`
	TypeObject    string                 `json:"type_object"`
	TypeID        string                 `json:"type_id"`
	SourceID      string                 `json:"source_id"`
	Balance       float64                `json:"balance"`
	BalanceBefore float64                `json:"balance_before"`
	Description   string                 `json:"description"`
	Metadata      map[string]interface{} `json:"metadata"`
	ProductType   string                 `json:"product_type"`
	Status        string                 `json:"status"`
	CreatedAt     *time.Time             `json:"created_at"`
	UpdatedAt     *time.Time             `json:"updated_at"`
}

type BalanceTransactionProtected struct {
	ID            string                  `json:"_id"`
	CompanyID     string                  `json:"company_id"`
	ProfileID     string                  `json:"profile_id"`
	MerchantID    string                  `json:"merchant_id"`
	Type          *int                    `json:"type"`
	TypeObject    *string                 `json:"type_object"`
	TypeID        *string                 `json:"type_id"`
	SourceID      *string                 `json:"source_id"`
	Balance       *float64                `json:"balance"`
	BalanceBefore *float64                `json:"balance_before"`
	Description   *string                 `json:"description"`
	Metadata      *map[string]interface{} `json:"metadata"`
	Status        string                  `json:"status"`
	CreatedAt     *time.Time              `json:"created_at"`
	UpdatedAt     *time.Time              `json:"updated_at"`
}

type BalanceTransactionPagin struct {
	Limit        int                  `json:"limit"`
	Page         int                  `json:"page"`
	Sort         string               `json:"sort"`
	TotalRecords int                  `json:"total_records"`
	TotalPages   int                  `json:"total_pages"`
	Records      []BalanceTransaction `json:"records"`
}

func (b BalanceTransaction) IsExist() bool {
	return b.ID != ""
}

func (b BalanceTransaction) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b BalanceTransaction) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ID)),
		}
	}
	return nil
}

func (b BalanceTransaction) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *BalanceTransaction) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateBalanceTransaction struct {
	MerchantID    string                  `json:"merchant_id"`
	CompanyID     string                  `json:"company_id"`
	ProfileID     string                  `json:"profile_id"`
	Type          *int                    `json:"type,omitempty"`
	TypeObject    *string                 `json:"type_object,omitempty"`
	TypeID        *string                 `json:"type_id,omitempty"`
	SourceID      *string                 `json:"source_id,omitempty"`
	Balance       *float64                `json:"balance,omitempty"`
	BalanceBefore *float64                `json:"balance_before,omitempty"`
	Amount        *float64                `json:"amount,omitempty"`
	Description   *string                 `json:"description,omitempty"`
	Metadata      *map[string]interface{} `json:"metadata,omitempty"`
	ProductType   *string                 `json:"product_type,omitempty"`
	Status        string                  `json:"status,omitempty"`
}

func (ct CreateBalanceTransaction) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.MerchantID, validation.Required),
		validation.Field(&ct.TypeID, validation.Required),
		validation.Field(&ct.Type, validation.Required, validation.In(
			constant.TypeTxnIncome,
			constant.TypeTxnSubsidy,
			constant.TypeTxnComission,
			constant.TypeTxnFeatureFee,
			constant.TypeTxnPlatformFee,
			constant.TypeTxnThirdPartyFee,
			constant.TypeTxnCompleted,
			constant.TypeTxnComission,
			constant.TypeTxnDisburseCoin,
			constant.TypeTxnBuyCoin,
			constant.TypeTxnRoundBook,
			constant.TypeTxnDisburseCoinCompleted,
		)),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (ct CreateBalanceTransaction) ValidateRoundCompleted() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.MerchantID, validation.Required),
		validation.Field(&ct.SourceID, validation.Required),
		validation.Field(&ct.TypeID, validation.Required),
		validation.Field(&ct.Type, validation.Required, validation.In(constant.TypeTxnSubsidy, constant.TypeTxnFeatureFee, constant.TypeTxnCompleted, constant.TypeTxnComission)),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
