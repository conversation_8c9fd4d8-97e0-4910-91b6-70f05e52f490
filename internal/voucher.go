package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Voucher struct {
	ID               string      `json:"_id"`
	CompanyID        string      `json:"company_id"`
	Code             string      `json:"code"`
	Fees             []Fee       `json:"fees"`
	DiscountType     string      `json:"discount_type"`
	DiscountValue    float64     `json:"discount_value"`
	MinPurchase      float64     `json:"min_purchase"`
	MaxDiscount      float64     `json:"max_discount"`
	UsageLimit       int         `json:"usage_limit"`
	EnableLimit      bool        `json:"enable_limit"`
	RemainingUsage   int         `json:"remaining_usage"`
	Description      string      `json:"description"`
	ExpiresAt        *time.Time  `json:"expires_at"`
	ExpireType       string      `json:"expire_type"`
	ValidFrom        *time.Time  `json:"valid_from"`
	ValidTo          *time.Time  `json:"valid_to"`
	Period           int         `json:"period"`
	PeriodType       string      `json:"period_type"`
	LevelPriority    int         `json:"level_priority"`
	Type             string      `json:"type"`
	Combined         bool        `json:"combined"`
	CombinationTypes []string    `json:"combination_types"`
	Metadata         interface{} `json:"metadata"`
	ProductType      string      `json:"product_type"`
	Status           string      `json:"status"`
	CreatedAt        *time.Time  `json:"created_at"`
	UpdatedAt        *time.Time  `json:"updated_at"`
}

type VoucherPagin struct {
	Limit        int       `json:"limit"`
	Page         int       `json:"page"`
	Sort         string    `json:"sort"`
	TotalRecords int       `json:"total_records"`
	TotalPages   int       `json:"total_pages"`
	Records      []Voucher `json:"records"`
}

func (b Voucher) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Voucher) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.Code)),
		}
	}
	return nil
}

func (b Voucher) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b Voucher) ValidateExpire() error {
	if b.ExpiresAt != nil && b.ExpiresAt.Before(time.Now()) {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is expired.", b.Code)),
		}
	}
	return nil
}

func (b Voucher) ValidateUsageRemaining() error {
	if b.RemainingUsage <= 0 {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is out of stock.", b.Code)),
		}
	}
	return nil
}

func (b *Voucher) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateVoucher struct {
	CompanyID        string             `json:"company_id,omitempty"`
	Code             *string            `json:"code,omitempty"`
	Fees             []CreateFee        `json:"fees,omitempty"`
	DiscountType     *string            `json:"discount_type,omitempty"`
	DiscountValue    *float64           `json:"discount_value,omitempty"`
	MinPurchase      *float64           `json:"min_purchase,omitempty"`
	MaxDiscount      *float64           `json:"max_discount,omitempty"`
	UsageLimit       *int               `json:"usage_limit,omitempty"`
	Description      *string            `json:"description,omitempty"`
	EnableLimit      *bool              `json:"enable_limit,omitempty"`
	RemainingUsage   *int               `json:"remaining_usage,omitempty"`
	ExpiresAt        string             `json:"expires_at,omitempty"`
	ExpireType       string             `json:"expire_type,omitempty"`
	ValidFrom        string             `json:"valid_from,omitempty"`
	ValidTo          string             `json:"valid_to,omitempty"`
	Period           *int               `json:"period,omitempty"`
	PeriodType       *string            `json:"period_type,omitempty"`
	LevelPriority    *int               `json:"level_priority,omitempty"`
	Type             *string            `json:"type,omitempty"`
	Combined         *bool              `json:"combined,omitempty"`
	CombinationTypes *[]string          `json:"combination_types,omitempty"`
	Metadata         *map[string]string `json:"metadata,omitempty"`
	ProductType      string             `json:"product_type,omitempty"`
	Status           string             `json:"status,omitempty"`
}

func (ct CreateVoucher) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.Code, validation.Required),
		validation.Field(&ct.DiscountType, validation.Required, validation.In("percentage", "fixed", "custom")),
		validation.Field(&ct.ExpireType, validation.In("fixed_range", "dynamic_period", "rolling_window")),
		validation.Field(&ct.Type, validation.In("event", "registration", "promo_campaign", "subscription_bonus")),
		validation.Field(&ct.ProductType, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (ct CreateVoucher) ValidateExpireType() error {

	switch ct.ExpireType {
	case "fixed_range":
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.ValidFrom, validation.Required),
			validation.Field(&ct.ValidTo, validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}

	case "dynamic_period":
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.Period, validation.Required),
			validation.Field(&ct.PeriodType, validation.In("day", "week", "month", "year"), validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	case "rolling_window":
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.ExpiresAt, validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	}

	return nil
}

func (ct CreateVoucher) ValidateUpdate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.DiscountType, validation.In("percentage", "fixed", "custom")),
		validation.Field(&ct.ExpireType, validation.In("fixed_range", "dynamic_period", "rolling_window")),
		validation.Field(&ct.Type, validation.In("event", "registration", "promo_campaign", "subscription_bonus")),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
