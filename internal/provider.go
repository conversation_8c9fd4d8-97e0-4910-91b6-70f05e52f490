package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Provider struct {
	ID          string            `json:"_id"`
	Slug        string            `json:"slug"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Secret      map[string]string `json:"secret"`
	Status      string            `json:"status"`
	CreatedAt   *time.Time        `json:"created_at"`
	UpdatedAt   *time.Time        `json:"updated_at"`
}

type ProviderProtected struct {
	ID          string     `json:"_id"`
	Slug        string     `json:"slug"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Status      string     `json:"status"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
}

type ProviderPagin struct {
	Limit        int                 `json:"limit"`
	Page         int                 `json:"page"`
	Sort         string              `json:"sort"`
	TotalRecords int                 `json:"total_records"`
	TotalPages   int                 `json:"total_pages"`
	Records      []ProviderProtected `json:"records"`
}

func (b Provider) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Provider) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ID)),
		}
	}
	return nil
}

func (b Provider) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Provider) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateProvider struct {
	Slug        *string            `json:"slug,omitempty"`
	Name        *string            `json:"name,omitempty"`
	Description *string            `json:"description,omitempty"`
	Secret      *map[string]string `json:"secret,omitempty"`
	Status      string             `json:"status,omitempty"`
}

func (ct CreateProvider) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.Name, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
