package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type DisbursementConfiguration struct {
	ID             string        `json:"_id"`
	CompanyID      string        `json:"company_id"`
	ProfileID      string        `json:"profile_id"`
	MerchantID     string        `json:"merchant_id"`
	MerchantBankID string        `json:"merchant_bank_id,omitempty"`
	AutoDisburse   bool          `json:"auto_disburse"`
	TotalAmount    float64       `json:"total_amount"`
	Merchant       *Merchant     `json:"merchant,omitempty"`
	MerchantBank   *MerchantBank `json:"merchant_bank,omitempty"`
	Status         string        `json:"status"`
	CreatedAt      *time.Time    `json:"created_at"`
	UpdatedAt      *time.Time    `json:"updated_at"`
}

type DisbursementConfigurationPagin struct {
	Limit        int                         `json:"limit"`
	Page         int                         `json:"page"`
	Sort         string                      `json:"sort"`
	TotalRecords int                         `json:"total_records"`
	TotalPages   int                         `json:"total_pages"`
	Records      []DisbursementConfiguration `json:"records"`
}

func (b DisbursementConfiguration) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b DisbursementConfiguration) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b DisbursementConfiguration) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *DisbursementConfiguration) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateDisbursementConfiguration struct {
	CompanyID      string   `json:"company_id,omitempty"`
	ProfileID      string   `json:"profile_id,omitempty"`
	MerchantID     string   `json:"merchant_id,omitempty"`
	MerchantBankID *string  `json:"merchant_bank_id,omitempty"`
	AutoDisburse   *bool    `json:"auto_disburse,omitempty"`
	TotalAmount    *float64 `json:"total_amount,omitempty"`
	Status         string   `json:"status,omitempty"`
}

func (ct CreateDisbursementConfiguration) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.MerchantID, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
