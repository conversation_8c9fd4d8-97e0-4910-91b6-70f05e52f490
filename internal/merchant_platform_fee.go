package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/go-ozzo/ozzo-validation/v4/is"
)

type MerchantPlatformFee struct {
	ID                     string            `json:"_id"`
	CompanyID              string            `json:"company_id"`
	ProfileID              string            `json:"profile_id"`
	MerchantID             string            `json:"merchant_id"`
	ProviderID             string            `json:"provider_id"`
	ReferrerID             *string           `json:"referrer_id,omitempty"`
	MarketingID            *string           `json:"marketing_id,omitempty"`
	Fees                   []Fee             `json:"fees"`
	Metadata               map[string]string `json:"metadata"`
	Provider               string            `json:"provider"`
	HasMarketingFee        bool              `json:"has_marketing_fee,omitempty"`
	HasReferralFee         bool              `json:"has_referral_fee,omitempty"`
	HasCashbackProviderFee bool              `json:"has_cashback_provider_fee,omitempty"`
	ProductType            string            `json:"product_type"`
	Status                 string            `json:"status"`
	CreatedAt              *time.Time        `json:"created_at"`
	UpdatedAt              *time.Time        `json:"updated_at"`
}

type MerchantPlatformFeePagin struct {
	Limit        int                   `json:"limit"`
	Page         int                   `json:"page"`
	Sort         string                `json:"sort"`
	TotalRecords int                   `json:"total_records"`
	TotalPages   int                   `json:"total_pages"`
	Records      []MerchantPlatformFee `json:"records"`
}

func (b MerchantPlatformFee) IsExist() bool {
	return b.ID != ""
}

func (b MerchantPlatformFee) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b MerchantPlatformFee) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *MerchantPlatformFee) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateMerchantPlatformFee struct {
	CompanyID              string             `json:"company_id,omitempty"`
	ProfileID              string             `json:"profile_id,omitempty"`
	MerchantID             string             `json:"merchant_id,omitempty"`
	ReferrerID             string             `json:"referrer_id,omitempty"`
	MarketingID            string             `json:"marketing_id,omitempty"`
	Type                   string             `json:"type,omitempty"`
	Fees                   []CreateFee        `json:"fees,omitempty"`
	Metadata               *map[string]string `json:"medata,omitempty"`
	Provider               *string            `json:"provider,omitempty"`
	HasMarketingFee        *bool              `json:"has_marketing_fee,omitempty"`
	HasReferralFee         *bool              `json:"has_referral_fee,omitempty"`
	HasCashbackProviderFee *bool              `json:"has_cashback_provider_fee,omitempty"`
	ProductType            string             `json:"product_type,omitempty"`
	Status                 string             `json:"status,omitempty"`
}

func (ct CreateMerchantPlatformFee) Validate(validateType string) error {

	switch validateType {
	case "custom":
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.CompanyID, validation.Required),
			validation.Field(&ct.MerchantID, validation.Required, is.MongoID),
			validation.Field(&ct.Provider, validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	case "default":
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.CompanyID, validation.Required),
			validation.Field(&ct.MerchantID, validation.Required, is.MongoID),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	default:
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.CompanyID, validation.Required),
			validation.Field(&ct.MerchantID, validation.Required, is.MongoID),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	}

	return nil
}

type UpdateMerchantPlatformFee struct {
	CompanyID              string             `json:"company_id,omitempty"`
	ProfileID              string             `json:"profile_id,omitempty"`
	MerchantID             string             `json:"merchant_id,omitempty"`
	ReferrerID             string             `json:"referrer_id,omitempty"`
	MarketingID            string             `json:"marketing_id,omitempty"`
	Type                   string             `json:"type,omitempty"`
	Fees                   []CreateFee        `json:"fees,omitempty"`
	Metadata               *map[string]string `json:"medata,omitempty"`
	Provider               *string            `json:"provider,omitempty"`
	HasMarketingFee        *bool              `json:"has_marketing_fee,omitempty"`
	HasReferralFee         *bool              `json:"has_referral_fee,omitempty"`
	HasCashbackProviderFee *bool              `json:"has_cashback_provider_fee,omitempty"`
	ProductType            string             `json:"product_type,omitempty"`
	Status                 string             `json:"status,omitempty"`
}

func (ct UpdateMerchantPlatformFee) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.ProductType, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (ct CreateFee) ValidateUpdateFee() error {

	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.FeeValue, validation.Required),
		validation.Field(&ct.FeeType, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
