package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Bank struct {
	ID          string            `json:"_id"`
	Slug        string            `json:"slug"`
	Name        string            `json:"name"`
	BankCode    string            `json:"bank_code"`
	Description string            `json:"description"`
	Metadata    map[string]string `json:"metadata"`
	CanDisburse bool              `json:"can_disburse"`
	Status      string            `json:"status"`
	CreatedAt   *time.Time        `json:"created_at"`
	UpdatedAt   *time.Time        `json:"updated_at"`
}

type BankPagin struct {
	Limit        int    `json:"limit"`
	Page         int    `json:"page"`
	Sort         string `json:"sort"`
	TotalRecords int    `json:"total_records"`
	TotalPages   int    `json:"total_pages"`
	Records      []Bank `json:"records"`
}

func (b Bank) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.Name)),
		}
	}
	return nil
}

func (b Bank) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.Name)),
		}
	}
	return nil
}

func (b Bank) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Bank) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateBank struct {
	Slug        *string            `json:"slug,omitempty"`
	Name        *string            `json:"name,omitempty"`
	BankCode    *string            `json:"bank_code,omitempty"`
	Description *string            `json:"description,omitempty"`
	Metadata    *map[string]string `json:"medata,omitempty"`
	CanDisburse *bool              `json:"can_disburse,omitempty"`
	Status      string             `json:"status,omitempty"`
}

func (ct CreateBank) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.Name, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
