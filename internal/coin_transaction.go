package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type CoinTransaction struct {
	ID            string            `json:"_id"`
	CompanyID     string            `json:"company_id"`
	ProfileID     string            `json:"profile_id"`
	CoinID        string            `json:"coin_id"`
	Type          int               `json:"type"`
	TypeID        string            `json:"type_id"`
	SourceID      string            `json:"source_id"`
	Balance       float64           `json:"balance"`
	BalanceBefore float64           `json:"balance_before"`
	Description   string            `json:"description"`
	Metadata      map[string]string `json:"metadata"`
	Status        string            `json:"status"`
	CreatedAt     *time.Time        `json:"created_at"`
	UpdatedAt     *time.Time        `json:"updated_at"`
}

type CoinTransactionProtected struct {
	ID            string             `json:"_id"`
	CompanyID     string             `json:"company_id"`
	ProfileID     string             `json:"profile_id"`
	Type          *int               `json:"type"`
	TypeID        *string            `json:"type_id"`
	SourceID      *string            `json:"source_id"`
	Balance       *float64           `json:"balance"`
	BalanceBefore *float64           `json:"balance_before"`
	Description   *string            `json:"description"`
	Metadata      *map[string]string `json:"metadata"`
	Status        string             `json:"status"`
	CreatedAt     *time.Time         `json:"created_at"`
	UpdatedAt     *time.Time         `json:"updated_at"`
}

type CoinTransactionPagin struct {
	Limit        int               `json:"limit"`
	Page         int               `json:"page"`
	Sort         string            `json:"sort"`
	TotalRecords int               `json:"total_records"`
	TotalPages   int               `json:"total_pages"`
	Records      []CoinTransaction `json:"records"`
}

func (b CoinTransaction) IsExist() bool {
	return b.ID != ""
}

func (b CoinTransaction) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b CoinTransaction) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ID)),
		}
	}
	return nil
}

func (b CoinTransaction) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *CoinTransaction) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateCoinTransaction struct {
	CoinID        string             `json:"coin_id"`
	CompanyID     string             `json:"company_id"`
	ProfileID     string             `json:"profile_id"`
	UserID        string             `json:"user_id"`
	Type          *int               `json:"type,omitempty"`
	TypeID        *string            `json:"type_id,omitempty"`
	SourceID      *string            `json:"source_id,omitempty"`
	Balance       *float64           `json:"balance,omitempty"`
	BalanceBefore *float64           `json:"balance_before,omitempty"`
	Amount        *float64           `json:"amount,omitempty"`
	Description   *string            `json:"description,omitempty"`
	Metadata      *map[string]string `json:"metadata,omitempty"`
	Status        string             `json:"status,omitempty"`
}

func (ct CreateCoinTransaction) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.UserID, validation.Required),
		validation.Field(&ct.TypeID, validation.Required),
		validation.Field(&ct.Type, validation.Required, validation.In(
			constant.TypeTxnRoundBook,
			constant.TypeTxnRoundCancelByPlayer,
			constant.TypeTxnRoundCancelByStreamer,
			constant.TypeTxnRoundComplete,
			constant.TypeTxnRoundCancelBySystem,
			constant.TypeTxnBuyCoin,
			constant.TypeTxnDisburseCoin,
		)),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (ct CreateCoinTransaction) ValidateRoundCompleted() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.UserID, validation.Required),
		validation.Field(&ct.SourceID, validation.Required),
		validation.Field(&ct.TypeID, validation.Required),
		validation.Field(&ct.Type, validation.Required, validation.In(
			constant.TypeTxnRoundBook,
			constant.TypeTxnRoundCancelByPlayer,
			constant.TypeTxnRoundCancelByStreamer,
			constant.TypeTxnRoundComplete,
			constant.TypeTxnRoundCancelBySystem,
			constant.TypeTxnBuyCoin,
			constant.TypeTxnDisburseCoin,
		)),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type PayloadCoinBalance struct {
	CoinID        string            `json:"coin_id"`
	CompanyID     string            `json:"company_id"`
	ProfileID     string            `json:"profile_id"`
	UserID        string            `json:"user_id"`
	Type          int               `json:"type,omitempty"`
	TypeID        string            `json:"type_id,omitempty"`
	SourceID      string            `json:"source_id,omitempty"`
	Balance       float64           `json:"balance,omitempty"`
	BalanceBefore float64           `json:"balance_before,omitempty"`
	Amount        float64           `json:"amount,omitempty"`
	Description   string            `json:"description,omitempty"`
	Metadata      map[string]string `json:"metadata,omitempty"`
	Status        string            `json:"status,omitempty"`
}
