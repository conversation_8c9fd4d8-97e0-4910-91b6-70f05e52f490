package rest

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"github.com/continue-team/riot"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type WebhookSvc interface {
	HandleWebhook(ctx context.Context, params []byte, provider, webhookType string) (bool, error)
}

type WebhookHandler struct {
	svc  WebhookSvc
	util riot.Util
}

func NewWebhookHandler(utl riot.Util, svc WebhookSvc) *WebhookHandler {
	return &WebhookHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *WebhookHandler) Register(r *mux.Router) {

	auth := r.PathPrefix("/webhook").Subrouter()

	auth.HandleFunc(fmt.Sprintf("/{provider:%s}/{type:%s}", "[a-zA-Z0-9-]+", "[a-zA-Z0-9-]+"), h.<PERSON>).Methods(http.MethodPost)
}

func (h *WebhookHandler) HandleWebhook(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("WebhookHandler", "AuthSocial"))

	provider := mux.Vars(r)["provider"]
	webhookType := mux.Vars(r)["type"]
	h.util.Logger.Info("WebhookHandler.Webhook", zap.String("Provider", provider))

	callbackToken := r.Header.Get("X-Callback-Token")

	expectedToken := h.util.Conf.Get("XENDIT_WEBHOOK_TOKEN")

	if callbackToken == "" {
		h.util.Logger.Error("Missing X-CALLBACK-TOKEN header")
		http.Error(w, "Missing X-CALLBACK-TOKEN header", http.StatusUnauthorized)
		return
	}

	// Verify the token
	if callbackToken != expectedToken {
		h.util.Logger.Error("Invalid X-CALLBACK-TOKEN")
		http.Error(w, "Invalid X-CALLBACK-TOKEN", http.StatusUnauthorized)
		return
	}

	// Read the request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		h.util.Logger.Error("Failed to read request body", zap.Error(err))
		http.Error(w, "Failed to read request body", http.StatusInternalServerError)
		return
	}

	// Log the request body
	h.util.Logger.Info("Request Body", zap.String("Body", string(body)))

	data, err := h.svc.HandleWebhook(r.Context(), body, provider, webhookType)

	if err != nil {
		h.util.Logger.Error("Failed to create webhook", zap.Error(err))
		http.Error(w, "Failed to create webhook", http.StatusInternalServerError)
		return
	}
	// Respond with OK
	riot.ResponseOK(w, data)
}
