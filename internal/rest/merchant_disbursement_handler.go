package rest

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type MerchantDisbursementmerchantDisbSvc interface {
	Create(ctx context.Context, identifier internal.Identifier, params internal.CreateDisbursementConfiguration, nexusData riot.Nexus) (_ internal.DisbursementConfiguration, err error)
	Delete(ctx context.Context, identifier internal.Identifier, nexusData riot.Nexus) (err error)
	Update(ctx context.Context, identifier internal.Identifier, params internal.CreateDisbursementConfiguration, nexusData riot.Nexus) (_ internal.DisbursementConfiguration, err error)
	GetByMerchantID(ctx context.Context, identifier internal.Identifier, id string, includes []string, nexusData riot.Nexus) (_ internal.DisbursementConfiguration, err error)
}

type MerchantDisbursementkHandler struct {
	svc  MerchantDisbursementmerchantDisbSvc
	util riot.Util
}

func NewMerchantDisbursementkHandler(utl riot.Util, svc MerchantDisbursementmerchantDisbSvc) *MerchantDisbursementkHandler {
	return &MerchantDisbursementkHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *MerchantDisbursementkHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/disbursements/configuration", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Create),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPost)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/disbursements/configuration", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetByMerchantID),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/disbursements/configuration", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Update),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPut)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/disbursements/configuration", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Delete),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodDelete)

}

func (h *MerchantDisbursementkHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantDisbursementkHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreateDisbursementConfiguration

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	merchantID := mux.Vars(r)["merchantId"] //nolint: gosimple

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	merchantDisb, err := h.svc.Create(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, merchantDisb)
}

func (h *MerchantDisbursementkHandler) GetByMerchantID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantDisbursementkHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"]                 //nolint: gosimple
	merchantID := mux.Vars(r)["merchantId"] //nolint: gosimple

	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	merchantDisb, err := h.svc.GetByMerchantID(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, merchantDisb)
}

func (h *MerchantDisbursementkHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantDisbursementkHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	merchantID := mux.Vars(r)["merchantId"] //nolint: gosimple

	err := h.svc.Delete(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, nil, "Data successfully deleted.")
}

func (h *MerchantDisbursementkHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantDisbursementkHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	merchantID := mux.Vars(r)["merchantId"]

	var body internal.CreateDisbursementConfiguration
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	merchantDisb, err := h.svc.Update(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, merchantDisb)
}
