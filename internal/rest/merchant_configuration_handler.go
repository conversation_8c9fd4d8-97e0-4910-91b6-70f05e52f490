package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type MerchantConfigurationSvc interface {
	GetAllWithPagination(ctx context.Context, identifier internal.Identifier, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantConfigurationPagin, err error)
}

type MerchantConfigurationHandler struct {
	svc  MerchantConfigurationSvc
	util riot.Util
}

func NewMerchantConfigurationHandler(utl riot.Util, svc MerchantConfigurationSvc) *MerchantConfigurationHandler {
	return &MerchantConfigurationHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *MerchantConfigurationHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/configurations", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAll),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
}

func (h *MerchantConfigurationHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantConfigurationHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	merchantID := mux.Vars(r)["merchantId"]

	urlValues := r.URL.Query()

	merchantConfiguration, err := h.svc.GetAllWithPagination(r.Context(), internal.Identifier{MerchantID: merchantID}, urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, merchantConfiguration)
}
