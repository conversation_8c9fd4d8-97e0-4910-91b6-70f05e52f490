package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type MerchantVoucherSvc interface {
	Create(ctx context.Context, identifier internal.Identifier, params internal.CreateMerchantVoucher, nexusData riot.Nexus) (_ internal.MerchantVoucher, err error)
	Delete(ctx context.Context, identifier internal.Identifier, id string, nexusData riot.Nexus) (err error)
	Update(ctx context.Context, identifier internal.Identifier, id string, params internal.CreateMerchantVoucher, nexusData riot.Nexus) (_ internal.MerchantVoucher, err error)
	GetByID(ctx context.Context, identifier internal.Identifier, id string, includes []string, nexusData riot.Nexus) (_ internal.MerchantVoucher, err error)
	GetAllWithPagination(ctx context.Context, identifier internal.Identifier, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantVoucherPagin, err error)
}

type MerchantVoucherHandler struct {
	svc  MerchantVoucherSvc
	util riot.Util
}

func NewMerchantVoucherHandler(utl riot.Util, svc MerchantVoucherSvc) *MerchantVoucherHandler {
	return &MerchantVoucherHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *MerchantVoucherHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/vouchers", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Create),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPost)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/vouchers/{id:%s}", common.ObjectIDRegex, common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetByID),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/vouchers/{id:%s}", common.ObjectIDRegex, common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Update),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPut)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/vouchers/{id:%s}", common.ObjectIDRegex, common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Delete),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodDelete)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/vouchers", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAll),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
}

func (h *MerchantVoucherHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantVoucherHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreateMerchantVoucher

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}
	mechantId := mux.Vars(r)["merchantId"]

	platformFee, err := h.svc.Create(r.Context(), internal.Identifier{MerchantID: mechantId}, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, platformFee)
}

func (h *MerchantVoucherHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantVoucherHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	merchantID := mux.Vars(r)["merchantId"]
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	merchantVoucher, err := h.svc.GetByID(r.Context(), internal.Identifier{MerchantID: merchantID}, id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, merchantVoucher)
}

func (h *MerchantVoucherHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantVoucherHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	merchantID := mux.Vars(r)["merchantId"]

	urlValues := r.URL.Query()

	merchantVoucher, err := h.svc.GetAllWithPagination(r.Context(), internal.Identifier{MerchantID: merchantID}, urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, merchantVoucher)
}

func (h *MerchantVoucherHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantVoucherHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple
	merchantID := mux.Vars(r)["merchantId"]

	err := h.svc.Delete(r.Context(), internal.Identifier{MerchantID: merchantID}, id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, nil, "Data successfully deleted.")
}

func (h *MerchantVoucherHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantVoucherHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple
	merchantID := mux.Vars(r)["merchantId"]

	var body internal.CreateMerchantVoucher
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	merchantVoucher, err := h.svc.Update(r.Context(), internal.Identifier{MerchantID: merchantID}, id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, merchantVoucher)
}
