package rest

import (
	"context"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type BalanceTransactionSvc interface {
	Create(ctx context.Context, params []internal.CreateBalanceTransaction, nexusData riot.Nexus) (_ []internal.BalanceTransaction, err error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.BalanceTransactionPagin, err error)
	GetStatistics(ctx context.Context, filter internal.BalanceTransactionStatsFilter, nexusData riot.Nexus) (_ internal.BalanceTransactionStatistics, err error)
	GetAllTimeStatistics(ctx context.Context, filter internal.BalanceTransactionAllTimeFilter, nexusData riot.Nexus) (_ internal.BalanceTransactionAllTimeStats, err error)
}

type BalanceTransactionHandler struct {
	svc  BalanceTransactionSvc
	util riot.Util
}

func NewBalanceTransactionHandler(utl riot.Util, svc BalanceTransactionSvc) *BalanceTransactionHandler {
	return &BalanceTransactionHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *BalanceTransactionHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.Handle("/balance/transactions",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Create),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, false, riot.RoleCompany))).
		Methods(http.MethodPost)
	v1.Handle("/balance/transactions",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAll),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
	v1.Handle("/balance/transactions/statistics",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetStatistics),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
	v1.Handle("/balance/transactions/statistics/alltime",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAllTimeStatistics),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
}

func (h *BalanceTransactionHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BalanceTransactionHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	type Data struct {
		Data []internal.CreateBalanceTransaction `json:"data"`
	}

	var body Data

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	balance, err := h.svc.Create(r.Context(), body.Data, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, balance)
}

func (h *BalanceTransactionHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BalanceTransactionHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()

	balance, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, balance)
}

func (h *BalanceTransactionHandler) GetStatistics(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BalanceTransactionHandler", "GetStatistics"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// Parse query parameters
	urlValues := r.URL.Query()

	filter := internal.BalanceTransactionStatsFilter{
		Period: urlValues.Get("period"),
	}

	// Set default period if not provided
	if filter.Period == "" {
		filter.Period = "daily"
	}

	// Parse date range
	if startDateStr := urlValues.Get("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			filter.StartDate = &startDate
		}
	}
	if endDateStr := urlValues.Get("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			filter.EndDate = &endDate
		}
	}

	// Parse merchant ID
	if merchantID := urlValues.Get("merchant_id"); merchantID != "" {
		filter.MerchantID = &merchantID
	}

	// Parse profile ID
	if profileID := urlValues.Get("profile_id"); profileID != "" {
		filter.ProfileID = &profileID
	}

	// Parse include flags
	filter.IncludeTypes = urlValues.Get("include_types") == "true"
	filter.IncludeMerchants = urlValues.Get("include_merchants") == "true"
	filter.IncludeDaily = urlValues.Get("include_daily") == "true"
	filter.IncludeMonthly = urlValues.Get("include_monthly") == "true"

	// Parse transaction types
	if typesStr := urlValues.Get("types"); typesStr != "" {
		typeStrs := strings.Split(typesStr, ",")
		for _, typeStr := range typeStrs {
			if typeInt, err := strconv.Atoi(strings.TrimSpace(typeStr)); err == nil {
				filter.Types = append(filter.Types, typeInt)
			}
		}
	}

	statistics, err := h.svc.GetStatistics(r.Context(), filter, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, statistics)
}

func (h *BalanceTransactionHandler) GetAllTimeStatistics(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BalanceTransactionHandler", "GetAllTimeStatistics"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// Parse query parameters
	urlValues := r.URL.Query()

	filter := internal.BalanceTransactionAllTimeFilter{}

	// Parse merchant ID
	if merchantID := urlValues.Get("merchant_id"); merchantID != "" {
		filter.MerchantID = &merchantID
	}

	// Parse profile ID
	if profileID := urlValues.Get("profile_id"); profileID != "" {
		filter.ProfileID = &profileID
	}

	// Parse include flags
	filter.IncludeTypes = urlValues.Get("include_types") == "true"
	filter.IncludeMerchants = urlValues.Get("include_merchants") == "true"
	filter.IncludeTrends = urlValues.Get("include_trends") == "true"

	// Parse top limit
	if topLimitStr := urlValues.Get("top_limit"); topLimitStr != "" {
		if topLimit, err := strconv.Atoi(topLimitStr); err == nil && topLimit > 0 {
			filter.TopLimit = topLimit
		}
	}

	// Parse transaction types
	if typesStr := urlValues.Get("types"); typesStr != "" {
		typeStrs := strings.Split(typesStr, ",")
		for _, typeStr := range typeStrs {
			if typeInt, err := strconv.Atoi(strings.TrimSpace(typeStr)); err == nil {
				filter.Types = append(filter.Types, typeInt)
			}
		}
	}

	statistics, err := h.svc.GetAllTimeStatistics(r.Context(), filter, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, statistics)
}
