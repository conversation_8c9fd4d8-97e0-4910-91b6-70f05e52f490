package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type MerchantBankSvc interface {
	Create(ctx context.Context, identifier internal.Identifier, params internal.CreateMerchantBank, nexusData riot.Nexus) (_ internal.MerchantBank, err error)
	Delete(ctx context.Context, identifier internal.Identifier, id string, nexusData riot.Nexus) (err error)
	Update(ctx context.Context, identifier internal.Identifier, id string, params internal.CreateMerchantBank, nexusData riot.Nexus) (_ internal.MerchantBank, err error)
	GetByID(ctx context.Context, identifier internal.Identifier, id string, includes []string, nexusData riot.Nexus) (_ internal.MerchantBank, err error)
	GetAllWithPagination(ctx context.Context, identifier internal.Identifier, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantBankPagin, err error)
	CreateBulk(ctx context.Context, identifier internal.Identifier, params []internal.CreateMerchantBank, nexusData riot.Nexus) ([]internal.MerchantBank, error)
}

type MerchantBankHandler struct {
	svc  MerchantBankSvc
	util riot.Util
}

func NewMerchantBankHandler(utl riot.Util, svc MerchantBankSvc) *MerchantBankHandler {
	return &MerchantBankHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *MerchantBankHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/banks", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Create),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPost)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/banks/{id:%s}", common.ObjectIDRegex, common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetByID),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/banks/{id:%s}", common.ObjectIDRegex, common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Update),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPut)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/banks/{id:%s}", common.ObjectIDRegex, common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Delete),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodDelete)
	v1.Handle(fmt.Sprintf("/merchants/{merchantId:%s}/banks", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAll),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
}

func (h *MerchantBankHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantBankHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreateMerchantBank

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	merchantID := mux.Vars(r)["merchantId"] //nolint: gosimple

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	bank, err := h.svc.Create(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, bank)
}

func (h *MerchantBankHandler) CreateBulk(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantBankHandler", "CreateBulk"))

	requestID := r.Header.Get("X-Request-ID")

	type Data struct {
		Data []internal.CreateMerchantBank `json:"data"`
	}

	var body Data

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	merchantID := mux.Vars(r)["merchantId"] //nolint: gosimple

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	bank, err := h.svc.CreateBulk(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, body.Data, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, bank)
}

func (h *MerchantBankHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantBankHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"]                 //nolint: gosimple
	merchantID := mux.Vars(r)["merchantId"] //nolint: gosimple

	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	bank, err := h.svc.GetByID(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, bank)
}

func (h *MerchantBankHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantBankHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()
	merchantID := mux.Vars(r)["merchantId"] //nolint: gosimple

	bank, err := h.svc.GetAllWithPagination(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, bank)
}

func (h *MerchantBankHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantBankHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"]                 //nolint: gosimple
	merchantID := mux.Vars(r)["merchantId"] //nolint: gosimple

	err := h.svc.Delete(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, nil, "Data successfully deleted.")
}

func (h *MerchantBankHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("MerchantBankHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple
	merchantID := mux.Vars(r)["merchantId"]

	var body internal.CreateMerchantBank
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	bank, err := h.svc.Update(r.Context(), internal.Identifier{
		MerchantID: merchantID,
	}, id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, bank)
}
