package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type PlatformFeeSvc interface {
	Create(ctx context.Context, params internal.CreatePlatformFee, nexusData riot.Nexus) (_ internal.PlatformFee, err error)
	Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error)
	Update(ctx context.Context, id string, params internal.CreatePlatformFee, nexusData riot.Nexus) (_ internal.PlatformFee, err error)
	GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.PlatformFee, err error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.PlatformFeePagin, err error)
}

type PlatformFeeHandler struct {
	svc  PlatformFeeSvc
	util riot.Util
}

func NewPlatformFeeHandler(utl riot.Util, svc PlatformFeeSvc) *PlatformFeeHandler {
	return &PlatformFeeHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *PlatformFeeHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.Handle("/platform-fees",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Create),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPost)
	v1.Handle(fmt.Sprintf("/platform-fees/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetByID),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
	v1.Handle(fmt.Sprintf("/platform-fees/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Update),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPut)
	v1.Handle(fmt.Sprintf("/platform-fees/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Delete),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodDelete)
	v1.Handle("/platform-fees",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAll),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
}

func (h *PlatformFeeHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("PlatformFeeHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreatePlatformFee

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	platformFee, err := h.svc.Create(r.Context(), body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, platformFee)
}

func (h *PlatformFeeHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("PlatformFeeHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	platformFeeFee, err := h.svc.GetByID(r.Context(), id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, platformFeeFee)
}

func (h *PlatformFeeHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("PlatformFeeHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()

	platformFeeFee, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, platformFeeFee)
}

func (h *PlatformFeeHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("PlatformFeeHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	err := h.svc.Delete(r.Context(), id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, nil, "Data successfully deleted.")
}

func (h *PlatformFeeHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("PlatformFeeHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	var body internal.CreatePlatformFee
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	platformFeeFee, err := h.svc.Update(r.Context(), id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, platformFeeFee)
}
