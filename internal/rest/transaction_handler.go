package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type TransactionSvc interface {
	Create(ctx context.Context, params internal.CreateTransaction, nexusData riot.Nexus) (_ internal.Transaction, err error)
	Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error)
	Update(ctx context.Context, id string, params internal.CreateTransaction, nexusData riot.Nexus) (_ internal.Transaction, err error)
	GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Transaction, err error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.TransactionPagin, err error)
}

type TransactionHandler struct {
	svc  TransactionSvc
	util riot.Util
}

func NewTransactionHandler(utl riot.Util, svc TransactionSvc) *TransactionHandler {
	return &TransactionHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *TransactionHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.Handle("/transactions",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Create),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, false, riot.RoleCompany, riot.RoleCustomer))).
		Methods(http.MethodPost)
	v1.Handle(fmt.Sprintf("/transactions/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetByID),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
	v1.Handle(fmt.Sprintf("/transactions/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Update),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPut)
	v1.Handle(fmt.Sprintf("/transactions/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Delete),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodDelete)
	v1.Handle("/transactions",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAll),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
}

func (h *TransactionHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("TransactionHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreateTransaction

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	transaction, err := h.svc.Create(r.Context(), body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, transaction)
}

func (h *TransactionHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("TransactionHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	transactionFee, err := h.svc.GetByID(r.Context(), id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, transactionFee)
}

func (h *TransactionHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("TransactionHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")
	authorization := r.Header.Get("Authorization")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()
	urlValues.Set("Authorization", authorization)

	transactionFee, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, transactionFee)
}

func (h *TransactionHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("TransactionHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	err := h.svc.Delete(r.Context(), id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, nil, "Data successfully deleted.")
}

func (h *TransactionHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("TransactionHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	var body internal.CreateTransaction
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	transactionFee, err := h.svc.Update(r.Context(), id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, transactionFee)
}
