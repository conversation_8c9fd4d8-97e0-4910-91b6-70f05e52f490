package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type BankSvc interface {
	Create(ctx context.Context, params internal.CreateBank, nexusData riot.Nexus) (_ internal.Bank, err error)
	Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error)
	Update(ctx context.Context, id string, params internal.CreateBank, nexusData riot.Nexus) (_ internal.Bank, err error)
	GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Bank, err error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.BankPagin, err error)
	CreateBulk(ctx context.Context, params []internal.CreateBank, nexusData riot.Nexus) ([]internal.Bank, error)
}

type BankHandler struct {
	svc  BankSvc
	util riot.Util
}

func NewBankHandler(utl riot.Util, svc BankSvc) *BankHandler {
	return &BankHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *BankHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.Handle("/banks",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Create),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPost)
	v1.Handle("/banks/_bulk",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.CreateBulk),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPost)
	v1.Handle(fmt.Sprintf("/banks/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetByID),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
	v1.Handle(fmt.Sprintf("/banks/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Update),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodPut)
	v1.Handle(fmt.Sprintf("/banks/{id:%s}", common.ObjectIDRegex),
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Delete),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodDelete)
	v1.Handle("/banks",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAll),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
}

func (h *BankHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BankHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreateBank

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	bank, err := h.svc.Create(r.Context(), body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, bank)
}

func (h *BankHandler) CreateBulk(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BankHandler", "CreateBulk"))

	requestID := r.Header.Get("X-Request-ID")

	type Data struct {
		Data []internal.CreateBank `json:"data"`
	}

	var body Data

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	bank, err := h.svc.CreateBulk(r.Context(), body.Data, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, bank)
}

func (h *BankHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BankHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	bank, err := h.svc.GetByID(r.Context(), id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, bank)
}

func (h *BankHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BankHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()

	bank, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, bank)
}

func (h *BankHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BankHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	err := h.svc.Delete(r.Context(), id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, nil, "Data successfully deleted.")
}

func (h *BankHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("BankHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	var body internal.CreateBank
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	bank, err := h.svc.Update(r.Context(), id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, bank)
}
