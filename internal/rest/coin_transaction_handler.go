package rest

import (
	"context"
	"net/http"
	"net/url"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type CoinTransactionSvc interface {
	Create(ctx context.Context, params []internal.CreateCoinTransaction, nexusData riot.Nexus) (_ []internal.CoinTransaction, err error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.CoinTransactionPagin, err error)
}

type CoinTransactionHandler struct {
	svc  CoinTransactionSvc
	util riot.Util
}

func NewCoinTransactionHandler(utl riot.Util, svc CoinTransactionSvc) *CoinTransactionHandler {
	return &CoinTransactionHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *CoinTransactionHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.Handle("/coins/transactions",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.Create),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, false, riot.RoleCompany))).
		Methods(http.MethodPost)
	v1.Handle("/coins/transactions",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.GetAll),
			middlewares.ValidateRole(h.util.Logger, riot.ClientFrontend, true, riot.RoleCustomer))).
		Methods(http.MethodGet)
}

func (h *CoinTransactionHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("CoinTransactionHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	type Data struct {
		Data []internal.CreateCoinTransaction `json:"data"`
	}

	var body Data

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	coin, err := h.svc.Create(r.Context(), body.Data, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, coin)
}

func (h *CoinTransactionHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("CoinTransactionHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()

	coin, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, coin)
}
