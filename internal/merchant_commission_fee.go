package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/go-ozzo/ozzo-validation/v4/is"
)

type MerchantCommissionFee struct {
	ID         string            `json:"_id"`
	CompanyID  string            `json:"company_id"`
	ProfileID  string            `json:"profile_id"`
	DownlineID string            `json:"downline_id"`
	ReffID     string            `json:"reff_id,omitempty"`
	MerchantID string            `json:"merchant_id"`
	ProviderID string            `json:"provider_id"`
	Fees       []FeeDetail       `json:"fees"`
	Metadata   map[string]string `json:"metadata"`
	Provider   string            `json:"provider"`
	Status     string            `json:"status"`
	CreatedAt  *time.Time        `json:"created_at"`
	UpdatedAt  *time.Time        `json:"updated_at"`
}

type MerchantCommissionFeePagin struct {
	Limit        int                     `json:"limit"`
	Page         int                     `json:"page"`
	Sort         string                  `json:"sort"`
	TotalRecords int                     `json:"total_records"`
	TotalPages   int                     `json:"total_pages"`
	Records      []MerchantCommissionFee `json:"records"`
}

func (b MerchantCommissionFee) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b MerchantCommissionFee) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *MerchantCommissionFee) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateMerchantCommissionFee struct {
	CompanyID  string             `json:"company_id,omitempty"`
	ProfileID  string             `json:"profile_id,omitempty"`
	MerchantID string             `json:"merchant_id,omitempty"`
	DownlineID string             `json:"downline_id,omitempty"`
	ReffID     *string            `json:"reff_id,omitempty"`
	Type       string             `json:"type,omitempty"`
	Fees       []FeeDetail        `json:"fees,omitempty"`
	Metadata   *map[string]string `json:"medata,omitempty"`
	Provider   *string            `json:"provider,omitempty"`
	Status     string             `json:"status,omitempty"`
}

func (ct CreateMerchantCommissionFee) Validate(validateType string) error {

	switch validateType {
	case "custom":
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.CompanyID, validation.Required),
			validation.Field(&ct.MerchantID, validation.Required, is.MongoID),
			validation.Field(&ct.Type, validation.Required),
			validation.Field(&ct.Provider, validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	case "default":
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.CompanyID, validation.Required),
			validation.Field(&ct.MerchantID, validation.Required, is.MongoID),
			validation.Field(&ct.Type, validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	default:
		if err := validation.ValidateStruct(&ct,
			validation.Field(&ct.CompanyID, validation.Required),
			validation.Field(&ct.MerchantID, validation.Required, is.MongoID),
			validation.Field(&ct.Type, validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	}

	return nil
}
