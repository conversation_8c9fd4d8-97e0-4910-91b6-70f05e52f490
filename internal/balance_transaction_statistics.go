package internal

import (
	"time"
)

// BalanceTransactionStatistics represents aggregated statistics for balance transactions
type BalanceTransactionStatistics struct {
	Period           string                           `json:"period"`           // daily, monthly, yearly
	StartDate        time.Time                        `json:"start_date"`
	EndDate          time.Time                        `json:"end_date"`
	TotalAmount      float64                          `json:"total_amount"`
	TotalCount       int64                            `json:"total_count"`
	ByType           []BalanceTransactionTypeStats    `json:"by_type"`
	ByMerchant       []BalanceTransactionMerchantStats `json:"by_merchant,omitempty"`
	DailyBreakdown   []BalanceTransactionDailyStats   `json:"daily_breakdown,omitempty"`
	MonthlyBreakdown []BalanceTransactionMonthlyStats `json:"monthly_breakdown,omitempty"`
}

// BalanceTransactionTypeStats represents statistics grouped by transaction type
type BalanceTransactionTypeStats struct {
	Type        int     `json:"type"`
	TypeName    string  `json:"type_name"`
	TotalAmount float64 `json:"total_amount"`
	TotalCount  int64   `json:"total_count"`
	Percentage  float64 `json:"percentage"`
}

// BalanceTransactionMerchantStats represents statistics grouped by merchant
type BalanceTransactionMerchantStats struct {
	MerchantID   string  `json:"merchant_id"`
	MerchantName string  `json:"merchant_name,omitempty"`
	TotalAmount  float64 `json:"total_amount"`
	TotalCount   int64   `json:"total_count"`
	Percentage   float64 `json:"percentage"`
}

// BalanceTransactionDailyStats represents daily statistics
type BalanceTransactionDailyStats struct {
	Date        time.Time `json:"date"`
	TotalAmount float64   `json:"total_amount"`
	TotalCount  int64     `json:"total_count"`
}

// BalanceTransactionMonthlyStats represents monthly statistics
type BalanceTransactionMonthlyStats struct {
	Year        int     `json:"year"`
	Month       int     `json:"month"`
	TotalAmount float64 `json:"total_amount"`
	TotalCount  int64   `json:"total_count"`
}

// BalanceTransactionStatsFilter represents filters for statistics queries
type BalanceTransactionStatsFilter struct {
	StartDate        *time.Time `json:"start_date,omitempty"`
	EndDate          *time.Time `json:"end_date,omitempty"`
	MerchantID       *string    `json:"merchant_id,omitempty"`
	CompanyID        *string    `json:"company_id,omitempty"`
	ProfileID        *string    `json:"profile_id,omitempty"`
	Types            []int      `json:"types,omitempty"`        // Filter by specific transaction types
	Period           string     `json:"period"`                 // daily, monthly, yearly
	IncludeTypes     bool       `json:"include_types"`          // Include breakdown by types
	IncludeMerchants bool       `json:"include_merchants"`      // Include breakdown by merchants
	IncludeDaily     bool       `json:"include_daily"`          // Include daily breakdown
	IncludeMonthly   bool       `json:"include_monthly"`        // Include monthly breakdown
}

// BalanceTransactionSummary represents a summary of balance transactions
type BalanceTransactionSummary struct {
	TotalIncome      float64   `json:"total_income"`
	TotalExpenses    float64   `json:"total_expenses"`
	NetAmount        float64   `json:"net_amount"`
	TransactionCount int64     `json:"transaction_count"`
	LastUpdated      time.Time `json:"last_updated"`
}

// BalanceTransactionTrend represents trend data for balance transactions
type BalanceTransactionTrend struct {
	Period     string                        `json:"period"`
	TrendData  []BalanceTransactionTrendData `json:"trend_data"`
	GrowthRate float64                       `json:"growth_rate"` // Percentage growth
	Trend      string                        `json:"trend"`       // "increasing", "decreasing", "stable"
}

// BalanceTransactionTrendData represents individual trend data points
type BalanceTransactionTrendData struct {
	Date       time.Time `json:"date"`
	Amount     float64   `json:"amount"`
	Count      int64     `json:"count"`
	Cumulative float64   `json:"cumulative"`
}
