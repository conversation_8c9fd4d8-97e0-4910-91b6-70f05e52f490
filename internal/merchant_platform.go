package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type MerchantPlatform struct {
	ID          string            `json:"_id"`
	CompanyID   string            `json:"company_id"`
	ProfileID   string            `json:"profile_id"`
	MerchantID  string            `json:"merchant_id"`
	Slug        string            `json:"slug"`
	Name        string            `json:"name"`
	DisplayName string            `json:"display_name"`
	Description string            `json:"description"`
	Metadata    map[string]string `json:"metadata,omitempty"`
	Status      string            `json:"status"`
	CreatedAt   *time.Time        `json:"created_at"`
	UpdatedAt   *time.Time        `json:"updated_at"`
}

type MerchantPlatformProtected struct {
	ID          string     `json:"_id"`
	Slug        string     `json:"slug"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Status      string     `json:"status"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
}

type MerchantPlatformPagin struct {
	Limit        int                `json:"limit"`
	Page         int                `json:"page"`
	Sort         string             `json:"sort"`
	TotalRecords int                `json:"total_records"`
	TotalPages   int                `json:"total_pages"`
	Records      []MerchantPlatform `json:"records"`
}

func (b MerchantPlatform) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b MerchantPlatform) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ID)),
		}
	}
	return nil
}

func (b MerchantPlatform) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *MerchantPlatform) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateMerchantPlatform struct {
	CompanyID   string             `json:"company_id,omitempty"`
	ProfileID   string             `json:"profile_id,omitempty"`
	MerchantID  string             `json:"merchant_id,omitempty"`
	Slug        *string            `json:"slug,omitempty"`
	Name        *string            `json:"name,omitempty"`
	DisplayName *string            `json:"display_name,omitempty"`
	Description *string            `json:"description,omitempty"`
	Metadata    *map[string]string `json:"metadata,omitempty"`
	Status      string             `json:"status,omitempty"`
}

func (ct CreateMerchantPlatform) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.CompanyID, validation.Required),
		validation.Field(&ct.ProfileID, validation.Required),
		validation.Field(&ct.MerchantID, validation.Required),
		validation.Field(&ct.DisplayName, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
