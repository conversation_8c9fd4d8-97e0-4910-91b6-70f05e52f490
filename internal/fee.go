package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Fee struct {
	ID                string     `json:"_id"`
	CompanyID         string     `json:"company_id"`
	ProfileID         string     `json:"profile_id"`
	ObjectType        string     `json:"object_type"`
	ObjectID          string     `json:"object_id"`
	PaymentMethod     string     `json:"payment_method"`
	FeeType           string     `json:"fee_type"`
	FeeValue          float64    `json:"fee_value"`
	PGFeeType         string     `json:"pg_fee_type"`
	PGFeeValue        float64    `json:"pg_fee_value"`
	ProviderFeeType   string     `json:"provider_fee_type"`
	ProviderFeeValue  float64    `json:"provider_fee_value"`
	PlatformFeeType   string     `json:"platform_fee_type"`
	PlatformFeeValue  float64    `json:"platform_fee_value"`
	ReferralFeeType   string     `json:"referral_fee_type"`
	ReferralFeeValue  float64    `json:"referral_fee_value"`
	MarkeringFeeType  string     `json:"markering_fee_type"`
	MarkeringFeeValue float64    `json:"markering_fee_value"`
	IsDefault         bool       `json:"is_default"`
	Currency          string     `json:"currency"`
	Status            string     `json:"status"`
	CreatedAt         *time.Time `json:"created_at"`
	UpdatedAt         *time.Time `json:"updated_at"`
}

type FeePagin struct {
	Limit        int    `json:"limit"`
	Page         int    `json:"page"`
	Sort         string `json:"sort"`
	TotalRecords int    `json:"total_records"`
	TotalPages   int    `json:"total_pages"`
	Records      []Fee  `json:"records"`
}

func (b Fee) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Fee) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Fee) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateFee struct {
	CompanyID         string   `json:"company_id,omitempty"`
	ProfileID         string   `json:"profile_id,omitempty"`
	ObjectID          string   `json:"object_id,omitempty"`
	ObjectType        *string  `json:"object_type,omitempty"`
	PaymentMethod     *string  `json:"payment_method,omitempty"`
	FeeType           *string  `json:"fee_type,omitempty"`
	FeeValue          *float64 `json:"fee_value,omitempty"`
	PGFeeType         *string  `json:"pg_fee_type,omitempty"`
	PGFeeValue        *float64 `json:"pg_fee_value,omitempty"`
	ProviderFeeType   *string  `json:"provider_fee_type,omitempty"`
	ProviderFeeValue  *float64 `json:"provider_fee_value,omitempty"`
	PlatformFeeType   *string  `json:"platform_fee_type,omitempty"`
	PlatformFeeValue  *float64 `json:"platform_fee_value,omitempty"`
	ReferralFeeType   *string  `json:"referral_fee_type,omitempty"`
	ReferralFeeValue  *float64 `json:"referral_fee_value,omitempty"`
	MarkeringFeeType  *string  `json:"markering_fee_type,omitempty"`
	MarkeringFeeValue *float64 `json:"markering_fee_value,omitempty"`
	IsDefault         *bool    `json:"is_default,omitempty"`
	Currency          *string  `json:"currency"`
	Status            string   `json:"status,omitempty"`
}

func (ct CreateFee) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.CompanyID, validation.Required),
		validation.Field(&ct.PaymentMethod, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
