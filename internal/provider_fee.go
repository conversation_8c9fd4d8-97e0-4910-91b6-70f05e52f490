package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type FeeDetail struct {
	PaymentMethod            *string  `json:"payment_method,omitempty"`
	FeeType                  *string  `json:"fee_type,omitempty"`
	FeeValue                 *float64 `json:"fee_value,omitempty"`
	ProviderFeeType          *string  `json:"provider_fee_type,omitempty"`
	ProviderFeeValue         *float64 `json:"provider_fee_value,omitempty"`
	PlatformFeeType          *string  `json:"platform_fee_type,omitempty"`
	PlatformFeeValue         *float64 `json:"platform_fee_value,omitempty"`
	CashbackProviderFeeType  *string  `json:"cashback_provider_fee_type,omitempty"`
	CashbackProviderFeeValue *float64 `json:"cashback_provider_fee_value,omitempty"`
	ReferralFeeType          *string  `json:"referral_fee_type,omitempty"`
	ReferralFeeValue         *float64 `json:"referral_fee_value,omitempty"`
	MarketingFeeType         *string  `json:"marketing_fee_type,omitempty"`
	MarketingFeeValue        *float64 `json:"marketing_fee_value,omitempty"`
	Provider                 *string  `json:"provider,omitempty"`
}

func (f FeeDetail) Validate() error {
	return validation.ValidateStruct(&f,
		validation.Field(&f.PaymentMethod, validation.Required.Error("Payment method is required")),
		validation.Field(&f.FeeType,
			validation.Required.Error("Fee type is required"),
			validation.In("fixed", "percentage").Error("Fee type must be either 'fixed' or 'percentage'"),
		),
		validation.Field(&f.FeeValue, validation.Min(0.0).Error("Fee value cannot be negative")),
		validation.Field(&f.ProviderFeeType,
			validation.In("fixed", "percentage").Error("Provider fee type must be either 'fixed' or 'percentage'"),
		),
		validation.Field(&f.ProviderFeeValue, validation.Min(0.0).Error("Provider fee value cannot be negative")),
		validation.Field(&f.PlatformFeeType,
			validation.In("fixed", "percentage").Error("Platform fee type must be either 'fixed' or 'percentage'"),
		),
		validation.Field(&f.PlatformFeeValue, validation.Min(0.0).Error("Platform fee value cannot be negative")),
	)
}

type ProviderFee struct {
	ID         string            `json:"_id"`
	CompanyID  string            `json:"company_id"`
	ProfileID  string            `json:"profile_id"`
	ProviderID string            `json:"provider_id"`
	Fees       []FeeDetail       `json:"fees"`
	Metadata   map[string]string `json:"metadata"`
	Status     string            `json:"status"`
	CreatedAt  *time.Time        `json:"created_at"`
	UpdatedAt  *time.Time        `json:"updated_at"`
}

type ProviderFeePagin struct {
	Limit        int           `json:"limit"`
	Page         int           `json:"page"`
	Sort         string        `json:"sort"`
	TotalRecords int           `json:"total_records"`
	TotalPages   int           `json:"total_pages"`
	Records      []ProviderFee `json:"records"`
}

func (b ProviderFee) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b ProviderFee) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b ProviderFee) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *ProviderFee) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateProviderFee struct {
	CompanyID  string             `json:"company_id,omitempty"`
	ProfileID  string             `json:"profile_id,omitempty"`
	ProviderID string             `json:"provider_id,omitempty"`
	Fees       []FeeDetail        `json:"fees,omitempty"`
	Metadata   *map[string]string `json:"medata,omitempty"`
	Status     string             `json:"status,omitempty"`
}

func (ct CreateProviderFee) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.CompanyID, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
