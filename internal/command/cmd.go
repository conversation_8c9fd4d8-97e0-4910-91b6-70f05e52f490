package command

import (
	"context"
	"errors"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/repositories"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func MigrateCoinCommand(ctx context.Context, util riot.Util, merchantRepo *repositories.MerchantRepository, coinRepo *repositories.CoinRepository) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "migrate-coins",
		Short: "Parses a JSON input",
		RunE: func(cmd *cobra.Command, args []string) error {
			util.Logger.Info("✅ Command 'migrate-coins' is running...") // Debugging

			page := 1
			limit := 10

			companyID, err := cmd.Flags().GetString("company_id")
			if err != nil {
				return riot.NewErrorf(riot.ErrorCodeInvalidArgument, err.Error())
			}

			merchantID, err := cmd.Flags().GetString("merchant_id")
			if err != nil {
				return riot.NewErrorf(riot.ErrorCodeInvalidArgument, err.Error())
			}

			listMigrateCoins := func(merchantID, companyID string, page, limit int) (*internal.MerchantPagin, error) {

				paginOpt := mongorm.FilterOpt{
					Page:     page,
					PageSize: limit,
					Filter:   map[string]interface{}{},
				}

				filterMap, ok := paginOpt.Filter.(map[string]interface{})
				if !ok {
					// handle the case where Filter is not a map
					return nil, errors.New("filter is not a map")
				}

				filterMap["status"] = constant.TypeActive

				if merchantID != "" {
					objectID, err := primitive.ObjectIDFromHex(merchantID)
					if err != nil {
						return nil, err
					}
					filterMap["_id"] = objectID
				}

				if companyID != "" {
					objectID, err := primitive.ObjectIDFromHex(companyID)
					if err != nil {
						return nil, err
					}
					filterMap["company_id"] = objectID
				}

				util.Logger.Info("options", zap.Any("options", paginOpt))

				merchants, err := merchantRepo.GetAllWithPagination(ctx, paginOpt)

				if err != nil {
					return nil, err
				}

				for _, merchant := range merchants.Records {

					filterCoin := map[string]interface{}{
						"profile_id": merchant.ProfileID,
						"company_id": merchant.CompanyID,
						"status":     constant.TypeActive,
					}

					coin, err := coinRepo.GetByFilter(ctx, filterCoin)

					if err != nil {
						return nil, err
					}

					if coin.IsExist() {
						continue
					}

					balance := float64(0)

					_, err = coinRepo.Create(ctx, internal.CreateCoin{
						ProfileID: merchant.ProfileID,
						CompanyID: merchant.CompanyID,
						LoginID:   &merchant.LoginID,
						Balance:   &balance,
						Status:    constant.StatusActive,
						Metadata: &map[string]string{
							"merchant_id": merchant.ID,
							"merchant":    merchant.Name,
						},
					})

					if err != nil {
						return nil, err
					}

				}

				return &merchants, nil
			}

			// Loop through all pages
			for {
				migrateCoins, err := listMigrateCoins(merchantID, companyID, page, limit)

				if err != nil {
					return err
				}

				// Stop if no more records are available
				if len(migrateCoins.Records) == 0 {
					util.Logger.Info("No more integrations to process. Exiting.")
					break
				}

				// Proceed to the next page
				page++
			}

			util.Logger.Info("✅ command 'migrate-coins' is completed...")
			return nil
		},
	}

	cmd.Flags().String("company_id", "", "Filter by company ID")
	cmd.MarkFlagRequired("company_id")

	cmd.Flags().String("merchant_id", "", "Filter by merchant ID")

	return cmd
}
