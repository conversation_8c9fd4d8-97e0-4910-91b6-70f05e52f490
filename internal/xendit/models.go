package xendit

import "time"

// QR Code Request & Response
type QRCodeRequest struct {
	ReferenceID string  `json:"reference_id"`
	Type        string  `json:"type"`
	Currency    string  `json:"currency"`
	ChannelCode string  `json:"channel_code"`
	Amount      float64 `json:"amount"`
	ExpiresAt   string  `json:"expires_at,omitempty"`
}

type QRCodeResponse struct {
	ID          string  `json:"id"`
	ReferenceID string  `json:"reference_id"`
	Type        string  `json:"type"`
	Currency    string  `json:"currency"`
	ChannelCode string  `json:"channel_code"`
	Amount      float64 `json:"amount"`
	ExpiresAt   string  `json:"expires_at"`
	QRString    string  `json:"qr_string"`
	BusinessID  string  `json:"business_id"`
	Status      string  `json:"status"`
	CreatedAt   string  `json:"created"`
}

// Virtual Account Request & Response
type VirtualAccountRequest struct {
	ExternalID string  `json:"external_id"`
	BankCode   string  `json:"bank_code"` // Example: "BCA", "BNI", "MANDIRI"
	Name       string  `json:"name"`
	Amount     float64 `json:"expected_amount,omitempty"`
}

type VirtualAccountResponse struct {
	ID         string `json:"id"`
	ExternalID string `json:"external_id"`
	BankCode   string `json:"bank_code"`
	Name       string `json:"name"`
	AccountNo  string `json:"account_number"`
}

// Bank Account Validation Request & Response
type BankAccountRequest struct {
	BankCode      string `json:"bank_code"`
	AccountNumber string `json:"bank_account_number"`
}

type BankAccountResponse struct {
	BankCode      string `json:"bank_code"`
	AccountNumber string `json:"account_number"`
	AccountHolder string `json:"account_holder_name"`
	IsValid       bool   `json:"is_valid"`
}

// Disbursement Request & Response
type DisbursementRequest struct {
	ExternalID        string  `json:"external_id"`
	BankCode          string  `json:"bank_code"`
	AccountNumber     string  `json:"account_number"`
	AccountHolderName string  `json:"account_holder_name"`
	Amount            float64 `json:"amount"`
	Description       string  `json:"description"`
}

type DisbursementResponse struct {
	ID            string  `json:"id"`
	ExternalID    string  `json:"external_id"`
	BankCode      string  `json:"bank_code"`
	AccountNumber string  `json:"account_number"`
	AccountHolder string  `json:"account_holder_name"`
	Amount        float64 `json:"amount"`
	Status        string  `json:"status"`
	FailureReason string  `json:"failure_reason,omitempty"`
}

// ChannelProperties represents the nested channel properties
type EwalletChannelProperties struct {
	MobileNumber       string `json:"mobile_number,omitempty"`
	CashTag            string `json:"cashtag,omitempty"`
	SuccessRedirectURL string `json:"success_redirect_url,omitempty"`
	FailureRedirectURL string `json:"failure_redirect_url,omitempty"`
}

// PaymentRequest represents the overall payment request structure
type EwalletRequest struct {
	ReferenceID    string                   `json:"reference_id"`
	Currency       string                   `json:"currency"`
	Amount         float64                  `json:"amount"`
	CheckoutMethod string                   `json:"checkout_method"`
	ChannelCode    string                   `json:"channel_code"`
	ChannelProps   EwalletChannelProperties `json:"channel_properties"`
	Metadata       map[string]interface{}   `json:"metadata"`
}

type EwalletActions struct {
	DesktopWebCheckoutURL  string `json:"desktop_web_checkout_url"`
	MobileWebCheckoutURL   string `json:"mobile_web_checkout_url"`
	MobileDeeplinkCheckout string `json:"mobile_deeplink_checkout_url"`
	QRCheckoutString       string `json:"qr_checkout_string"`
}

type EwalletDetail struct {
	FundSource *string `json:"fund_source"`
	Source     *string `json:"source"`
}

// EWalletResponse represents the eWallet response structure
type EWalletResponse struct {
	ID                 string                   `json:"id,omitempty"`
	BusinessID         string                   `json:"business_id,omitempty"`
	ReferenceID        string                   `json:"reference_id,omitempty"`
	Status             string                   `json:"status,omitempty"`
	Currency           string                   `json:"currency,omitempty"`
	ChargeAmount       int                      `json:"charge_amount,omitempty"`
	CaptureAmount      int                      `json:"capture_amount,omitempty"`
	PayerChargedCurr   *string                  `json:"payer_charged_currency,omitempty"`
	PayerChargedAmt    *int                     `json:"payer_charged_amount,omitempty"`
	RefundedAmount     *int                     `json:"refunded_amount,omitempty"`
	CheckoutMethod     string                   `json:"checkout_method,omitempty"`
	ChannelCode        string                   `json:"channel_code,omitempty"`
	ChannelProps       EwalletChannelProperties `json:"channel_properties,omitempty"`
	Actions            *EwalletActions          `json:"actions,omitempty"`
	IsRedirectRequired bool                     `json:"is_redirect_required,omitempty"`
	CallbackURL        string                   `json:"callback_url,omitempty"`
	Created            time.Time                `json:"created,omitempty"`
	Updated            time.Time                `json:"updated,omitempty"`
	VoidStatus         *string                  `json:"void_status,omitempty"`
	VoidedAt           *time.Time               `json:"voided_at,omitempty"`
	CaptureNow         bool                     `json:"capture_now,omitempty"`
	CustomerID         *string                  `json:"customer_id,omitempty"`
	Customer           *string                  `json:"customer,omitempty"`
	PaymentMethodID    *string                  `json:"payment_method_id,omitempty"`
	FailureCode        *string                  `json:"failure_code,omitempty"`
	Basket             *string                  `json:"basket,omitempty"`
	Metadata           map[string]string        `json:"metadata,omitempty"`
	ShippingInfo       *string                  `json:"shipping_information,omitempty"`
	PaymentDetail      EwalletDetail            `json:"payment_detail,omitempty"`
}

type CreditCardChargeRequest struct {
	TokenID    string  `json:"token_id,omitempty"`
	ExternalID string  `json:"external_id,omitempty"`
	Amount     float64 `json:"amount,omitempty"`
	CardCvn    string  `json:"card_cvn,omitempty"`
}

type CreditCardChargeResponse struct {
	ID                    string                 `json:"id"`
	Status                string                 `json:"status"`
	AuthorizedAmount      float64                `json:"authorized_amount"`
	CaptureAmount         float64                `json:"capture_amount"`
	Currency              string                 `json:"currency"`
	Metadata              map[string]interface{} `json:"metadata"`
	CreditCardTokenID     string                 `json:"credit_card_token_id"`
	BusinessID            string                 `json:"business_id"`
	MerchantID            string                 `json:"merchant_id"`
	MerchantReferenceCode string                 `json:"merchant_reference_code"`
	ExternalID            string                 `json:"external_id"`
	ECI                   string                 `json:"eci"`
	ChargeType            string                 `json:"charge_type"`
	MaskedCardNumber      string                 `json:"masked_card_number"`
	CardBrand             string                 `json:"card_brand"`
	CardType              string                 `json:"card_type"`
	Descriptor            string                 `json:"descriptor"`
	AuthorizationID       string                 `json:"authorization_id"`
	BankReconciliationID  string                 `json:"bank_reconciliation_id"`
	IssuingBankName       string                 `json:"issuing_bank_name"`
	CVNCode               string                 `json:"cvn_code"`
	ApprovalCode          string                 `json:"approval_code"`
	Created               string                 `json:"created"`
	CardFingerprint       string                 `json:"card_fingerprint"`
	NetworkResponse       NetworkResponse        `json:"network_response"`
}

type NetworkResponse struct {
	CardNetworkResponseCode  string `json:"card_network_response_code"`
	CardNetworkDescriptor    string `json:"card_network_descriptor"`
	MerchantAdviceCode       string `json:"merchant_advice_code"`
	MerchantAdviceDescriptor string `json:"merchant_advice_descriptor"`
	NetworkTransactionID     string `json:"network_transaction_id"`
}
