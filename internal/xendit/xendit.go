package xendit

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/riot"
	"go.uber.org/zap"
)

const (
	API_QR_CODE         = "https://api.xendit.co/qr_codes"
	API_QR_CODE_PAYMENT = "https://api.xendit.co/qris_payments"
)

// XenditClient - API client structure
type XenditClient struct {
	APIKey        string
	util          riot.Util
	companyID     string
	transactionID string
	externalID    string
	taskPublisher *rabbitmq.Publisher
}

// NewXendit - Initialize a new Xendit API client
func NewXendit(apiKey string, util riot.Util, companyID string, transactionID string, externalID string, taskPublisher *rabbitmq.Publisher) *XenditClient {
	return &XenditClient{APIKey: apiKey, util: util, companyID: companyID, transactionID: transactionID, externalID: externalID, taskPublisher: taskPublisher}
}

// sendRequest - Internal function to send API requests
// sendRequest - Internal function to send API requests
func (xc *XenditClient) sendRequest(url string, payload any, response interface{}) error {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	payloadPublisher := internal.PayloadProviderRequestLog{
		URL:         url,
		Provider:    "xendit",
		RequestBody: string(jsonPayload),
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return err
	}
	req.SetBasicAuth(xc.APIKey, "")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("api-version", xc.util.Conf.Get("XENDIT_API_VERSION"))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		return errors.New(fmt.Sprintf("Xendit API error: %s", body))
	}

	payloadPublisher.StatusCode = resp.StatusCode
	payloadPublisher.ResponseBody = string(body)

	if xc.companyID != "" {
		payloadPublisher.CompanyID = xc.companyID
	}

	if xc.transactionID != "" {
		payloadPublisher.TransactionID = xc.transactionID
	}

	if xc.externalID != "" {
		payloadPublisher.ExternalID = xc.externalID
	}

	err = xc.taskPublisher.PublishProviderRequestLog(context.Background(), payloadPublisher)

	if err != nil {
		return errors.New(fmt.Sprintf("Xendit PublishProviderRequestLog: %s", body))
	}

	// Parse response dynamically into provided struct
	return json.Unmarshal(body, response)
}

// CreateQRCode - Generate QRIS payment
func (xc *XenditClient) CreateQRCode(referenceID string, amount float64, expiresAt string) (*QRCodeResponse, error) {
	url := "https://api.xendit.co/qr_codes"

	payload := QRCodeRequest{
		ReferenceID: referenceID,
		Type:        "DYNAMIC",
		Currency:    "IDR",
		ChannelCode: "ID_XENDIT",
		Amount:      amount,
		ExpiresAt:   expiresAt,
	}

	xc.util.Logger.Info("XenditClient.CreateQRCode Request", zap.Any("payload", payload))

	// Prepare response struct
	var qrResponse QRCodeResponse

	// Send request
	err := xc.sendRequest(url, payload, &qrResponse)
	if err != nil {
		xc.util.Logger.Error("XenditClient.CreateQRCode API Error", zap.Error(err))
		return nil, err
	}

	xc.util.Logger.Info("XenditClient.CreateQRCode Success", zap.Any("response", qrResponse))
	return &qrResponse, nil
}

// RequestEWalletPayment - Request an E-Wallet payment (OVO, DANA, ShopeePay)
func (xc *XenditClient) CreateEWallet(params EwalletRequest) (*EWalletResponse, error) {
	url := "https://api.xendit.co/ewallets/charges"

	var ewalletResponse EWalletResponse

	// Send request
	err := xc.sendRequest(url, params, &ewalletResponse)
	if err != nil {
		xc.util.Logger.Error("XenditClient.CreateEWallet API Error", zap.Error(err))
		return nil, err
	}

	xc.util.Logger.Info("XenditClient.CreateEWallet Success", zap.Any("response", ewalletResponse))
	return &ewalletResponse, nil
}

// CreateVirtualAccount - Generate a Virtual Account (VA) for bank transfers
func (xc *XenditClient) CreateVirtualAccount(referenceID, bankCode string, amount float64) (*VirtualAccountResponse, error) {
	url := "https://api.xendit.co/callback_virtual_accounts"

	payload := VirtualAccountRequest{
		ExternalID: referenceID,
		BankCode:   bankCode, // Example: "BCA", "BNI", "MANDIRI"
		Name:       "Customer Name",
		Amount:     amount,
	}

	var vaResponse VirtualAccountResponse
	err := xc.sendRequest(url, payload, &vaResponse)
	if err != nil {
		return nil, err
	}

	return &vaResponse, nil
}

// CheckBankAccount - Validate bank account details
func (xc *XenditClient) CheckBankAccount(bankCode, accountNumber string) (*BankAccountResponse, error) {
	url := "https://api.xendit.co/bank_account_data_requests"

	payload := BankAccountRequest{
		BankCode:      bankCode,
		AccountNumber: accountNumber,
	}

	xc.util.Logger.Info("XenditClient.CheckBankAccount Request", zap.Any("payload", payload))

	var bankResponse BankAccountResponse
	err := xc.sendRequest(url, payload, &bankResponse)
	if err != nil {
		xc.util.Logger.Error("XenditClient.CheckBankAccount API Error", zap.Error(err))
		return nil, err
	}

	xc.util.Logger.Info("XenditClient.CheckBankAccount Success", zap.Any("response", bankResponse))
	return &bankResponse, nil
}

// CreateDisbursement - Transfer dana ke rekening tujuan
func (xc *XenditClient) CreateDisbursement(externalID, bankCode, accountNumber string, accountHolderName string, amount float64, description string) (*DisbursementResponse, error) {
	url := "https://api.xendit.co/disbursements"

	payload := DisbursementRequest{
		ExternalID:        externalID,
		BankCode:          bankCode,
		AccountNumber:     accountNumber,
		AccountHolderName: accountHolderName,
		Amount:            amount,
		Description:       description,
	}

	xc.util.Logger.Info("XenditClient.CreateDisbursement Request", zap.Any("payload", payload))

	var disbursementResponse DisbursementResponse
	err := xc.sendRequest(url, payload, &disbursementResponse)
	if err != nil {
		xc.util.Logger.Error("XenditClient.CreateDisbursement API Error", zap.Error(err))
		return nil, err
	}

	xc.util.Logger.Info("XenditClient.CreateDisbursement Success", zap.Any("response", disbursementResponse))
	return &disbursementResponse, nil
}

func (xc *XenditClient) CreateCreditCardCharge(params CreditCardChargeRequest) (*CreditCardChargeResponse, error) {
	url := "https://api.xendit.co/credit_card_charges"

	xc.util.Logger.Info("XenditClient.CreateCreditCardCharge Request", zap.Any("payload", params))

	var ccResponse CreditCardChargeResponse
	err := xc.sendRequest(url, params, &ccResponse)
	if err != nil {
		xc.util.Logger.Error("XenditClient.CreateCreditCardCharge API Error", zap.Error(err))
		return nil, err
	}

	xc.util.Logger.Info("XenditClient.CreateCreditCardCharge Success", zap.Any("response", ccResponse))
	return &ccResponse, nil
}
