package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type SubsidyRepository struct {
	util riot.Util
}

func NewSubsidyRepository(util riot.Util) *SubsidyRepository {
	return &SubsidyRepository{
		util: util,
	}
}

func (r *SubsidyRepository) Create(ctx context.Context, params internal.CreateSubsidy) (internal.Subsidy, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapSubsidyFromParams(params, "create")

	if err != nil {
		return internal.Subsidy{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.SubsidyCollection, &model)
	if err != nil {
		return internal.Subsidy{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SubsidyRepository.Create")
	}
	return mapSubsidyToInternalData(model), nil
}

func (r *SubsidyRepository) CreateBulk(ctx context.Context, params []internal.CreateSubsidy) ([]internal.Subsidy, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapSubsidyFromParams(param, "create")

		if err != nil {
			return []internal.Subsidy{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Subsidy{}).CreateBulk(ctx, r.util.DB, models.SubsidyCollection, listModels)

	if err != nil {
		return []internal.Subsidy{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SubsidyRepository.CreateBulk")
	}

	return []internal.Subsidy{}, nil
}

func (r *SubsidyRepository) Update(ctx context.Context, id string, params internal.CreateSubsidy) (internal.Subsidy, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository.Repo", "Update"))

	model, err := mapSubsidyFromParams(params, "update")

	if err != nil {
		return internal.Subsidy{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Subsidy{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.SubsidyCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *SubsidyRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateSubsidy) (internal.Subsidy, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapSubsidyFromParams(params, "update")

	if err != nil {
		return internal.Subsidy{}, err
	}

	model.Update(ctx, r.util.DB, models.SubsidyCollection, bsonM, &model)

	return mapSubsidyToInternalData(model), nil
}

func (r *SubsidyRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "SoftDelete"))
	var model models.Subsidy
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "SubsidyRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.SubsidyCollection, bson.M{"_id": idHex})
}

func (r *SubsidyRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "SoftDeleteByFilter"))

	var model models.Subsidy

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.SubsidyCollection, bsonM)
}

func (r *SubsidyRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Subsidy, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "GetByFilter"))

	var model models.Subsidy

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Subsidy{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.SubsidyCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Subsidy{}, nil
		}

		return internal.Subsidy{}, err
	}

	return mapSubsidyToInternalData(model), nil
}

func (r *SubsidyRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "SumByFilter"))

	var model models.Subsidy

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.SubsidyCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *SubsidyRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "CountByFilter"))

	var model models.Subsidy

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.SubsidyCollection, bsonM)
}

func (r *SubsidyRepository) GetByID(ctx context.Context, id string) (internal.Subsidy, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "GetByID"))

	var model models.Subsidy
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "SubsidyRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.SubsidyCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Subsidy{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Subsidy{}, err
	}
	return mapSubsidyToInternalData(model), nil
}

func (r *SubsidyRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Subsidy, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "GetAll"))
	var model models.Subsidy
	var listModels []models.Subsidy

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.SubsidyCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Subsidy{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SubsidyRepository.FindAll")
	}

	var internalModels []internal.Subsidy

	for _, externalModel := range listModels {
		internalModel := mapSubsidyToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *SubsidyRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Subsidy, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "GetAll"))
	var model models.Subsidy
	var listModels []models.Subsidy

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Subsidy{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.SubsidyCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Subsidy{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SubsidyRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Subsidy

	for _, externalModel := range listModels {
		internalModel := mapSubsidyToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *SubsidyRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.SubsidyPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SubsidyRepository", "GetAllWithPagination"))
	var listModels []models.Subsidy
	var model models.Subsidy

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.SubsidyCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.SubsidyPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.SubsidyPagin{}, err
	}

	var internalModels []internal.Subsidy

	for _, externalModel := range listModels {
		internalModel := mapSubsidyToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.SubsidyPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapSubsidyToInternalData(data models.Subsidy) internal.Subsidy {
	return internal.Subsidy{
		ID:            data.ID.Hex(),
		CompanyID:     data.CompanyID.Hex(),
		ProfileID:     data.ProfileID.Hex(),
		MerchantID:    data.MerchantID.Hex(),
		TransactionID: data.TransactionID.Hex(),
		VoucherID: func() string {
			if data.VoucherID != nil {
				return data.VoucherID.Hex()
			}
			return ""
		}(),
		PGFee:       pkg.GetFloat64Value(data.PGFee, 0.00),
		FeatureFee:  pkg.GetFloat64Value(data.FeatureFee, 0.00),
		Amount:      pkg.GetFloat64Value(data.Amount, 0.00),
		Description: pkg.GetStringValue(data.Description, ""),
		Metadata:    pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapSubsidyFromParams(params internal.CreateSubsidy, action string) (models.Subsidy, error) {
	model := models.Subsidy{
		PGFee:       pkg.SetFloat64Pointer(params.PGFee, action),
		FeatureFee:  pkg.SetFloat64Pointer(params.FeatureFee, action),
		Amount:      pkg.SetFloat64Pointer(params.Amount, action),
		Description: pkg.SetStringPointer(params.Description, action),
		Metadata:    pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Subsidy{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.Subsidy{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.Subsidy{}, err
		}
		model.MerchantID = objectID
	}

	if params.TransactionID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.TransactionID)
		if err != nil {
			return models.Subsidy{}, err
		}
		model.TransactionID = objectID
	}

	if params.VoucherID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.VoucherID)
		if err != nil {
			return models.Subsidy{}, err
		}
		model.VoucherID = &objectID
	}

	return model, nil
}
