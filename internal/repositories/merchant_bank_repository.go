package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type MerchantBankRepository struct {
	util riot.Util
}

func NewMerchantBankRepository(util riot.Util) *MerchantBankRepository {
	return &MerchantBankRepository{
		util: util,
	}
}

func (r *MerchantBankRepository) Create(ctx context.Context, params internal.CreateMerchantBank) (internal.MerchantBank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapMerchantBankFromParams(params, "create")

	if err != nil {
		return internal.MerchantBank{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.MerchantBankCollection, &model)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankRepository.Create")
	}
	return mapMerchantBankToInternalData(model), nil
}

func (r *MerchantBankRepository) CreateBulk(ctx context.Context, params []internal.CreateMerchantBank) ([]internal.MerchantBank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapMerchantBankFromParams(param, "create")

		if err != nil {
			return []internal.MerchantBank{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.MerchantBank{}).CreateBulk(ctx, r.util.DB, models.MerchantBankCollection, listModels)

	if err != nil {
		return []internal.MerchantBank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankRepository.CreateBulk")
	}

	return []internal.MerchantBank{}, nil
}

func (r *MerchantBankRepository) Update(ctx context.Context, id string, params internal.CreateMerchantBank) (internal.MerchantBank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository.Repo", "Update"))

	model, err := mapMerchantBankFromParams(params, "update")

	if err != nil {
		return internal.MerchantBank{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	err = model.Update(ctx, r.util.DB, models.MerchantBankCollection, bson.M{"_id": objectID}, &model)

	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankRepository.Update")
	}

	return r.GetByID(ctx, id)
}

func (r *MerchantBankRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantBank) (internal.MerchantBank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["profile_id"] = objectID
		case "bank_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["bank_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapMerchantBankFromParams(params, "update")

	if err != nil {
		return internal.MerchantBank{}, err
	}

	err = model.Update(ctx, r.util.DB, models.MerchantBankCollection, bsonM, &model)

	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankRepository.UpdateByFilter")
	}

	return r.GetByFilter(ctx, filter)
}

func (r *MerchantBankRepository) UpdateManyByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantBank) (internal.MerchantBank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["profile_id"] = objectID
		case "bank_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["bank_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapMerchantBankFromParams(params, "update")

	if err != nil {
		return internal.MerchantBank{}, err
	}

	err = model.UpdateMany(ctx, r.util.DB, models.MerchantBankCollection, bsonM, &model)

	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankRepository.UpdateByFilter")
	}

	return r.GetByFilter(ctx, filter)
}

func (r *MerchantBankRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "SoftDelete"))
	var model models.MerchantBank
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.MerchantBankCollection, bson.M{"_id": idHex})
}

func (r *MerchantBankRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "SoftDeleteByFilter"))

	var model models.MerchantBank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "bank_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["bank_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.MerchantBankCollection, bsonM)
}

func (r *MerchantBankRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantBank, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "GetByFilter"))

	var model models.MerchantBank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["profile_id"] = objectID
		case "bank_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["bank_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantBank{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.MerchantBankCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.MerchantBank{}, nil
		}

		return internal.MerchantBank{}, err
	}

	return mapMerchantBankToInternalData(model), nil
}

func (r *MerchantBankRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "SumByFilter"))

	var model models.MerchantBank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "bank_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["bank_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.MerchantBankCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *MerchantBankRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "CountByFilter"))

	var model models.MerchantBank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "bank_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["bank_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.MerchantBankCollection, bsonM)
}

func (r *MerchantBankRepository) GetByID(ctx context.Context, id string) (internal.MerchantBank, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "GetByID"))

	var model models.MerchantBank
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.MerchantBankCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantBank{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantBank{}, err
	}
	return mapMerchantBankToInternalData(model), nil
}

func (r *MerchantBankRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantBank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "GetAll"))
	var model models.MerchantBank
	var listModels []models.MerchantBank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantBank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantBank{}, err
			}
			bsonM["profile_id"] = objectID
		case "bank_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantBank{}, err
			}
			bsonM["bank_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantBank{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.MerchantBankCollection, bsonM, &listModels)
	if err != nil {
		return []internal.MerchantBank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankRepository.FindAll")
	}

	var internalModels []internal.MerchantBank

	for _, externalModel := range listModels {
		internalModel := mapMerchantBankToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantBankRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantBank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "GetAll"))
	var model models.MerchantBank
	var listModels []models.MerchantBank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantBank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantBank{}, err
			}
			bsonM["profile_id"] = objectID
		case "bank_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantBank{}, err
			}
			bsonM["bank_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantBank{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.MerchantBankCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.MerchantBank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.MerchantBank

	for _, externalModel := range listModels {
		internalModel := mapMerchantBankToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantBankRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantBankPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankRepository", "GetAllWithPagination"))
	var listModels []models.MerchantBank
	var model models.MerchantBank

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.MerchantBankCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantBankPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantBankPagin{}, err
	}

	var internalModels []internal.MerchantBank

	for _, externalModel := range listModels {
		internalModel := mapMerchantBankToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.MerchantBankPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapMerchantBankToInternalData(data models.MerchantBank) internal.MerchantBank {
	return internal.MerchantBank{
		ID:                    data.ID.Hex(),
		CompanyID:             data.CompanyID.Hex(),
		ProfileID:             data.ProfileID.Hex(),
		MerchantID:            data.MerchantID.Hex(),
		BankID:                data.BankID.Hex(),
		BankBranch:            pkg.GetStringValue(data.BankBranch, ""),
		BankName:              pkg.GetStringValue(data.BankName, ""),
		BankAccountNumber:     pkg.GetStringValue(data.BankAccountNumber, ""),
		BankAccountHolderName: pkg.GetStringValue(data.BankAccountHolderName, ""),
		BankDetail: func() internal.Bank {
			if data.BankDetail != nil {
				return internal.Bank{
					Slug:        pkg.GetStringValue(data.BankDetail.Slug, ""),
					Name:        pkg.GetStringValue(data.BankDetail.Name, ""),
					BankCode:    pkg.GetStringValue(data.BankDetail.BankCode, ""),
					Description: pkg.GetStringValue(data.BankDetail.Description, ""),
					CanDisburse: pkg.GetBoolValue(data.BankDetail.CanDisburse, false),
					Metadata:    pkg.GetMapstring(data.BankDetail.Metadata, map[string]string{}),
				}
			}
			return internal.Bank{}
		}(),
		Metadata:  pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:    constant.ReverseConstant(int(data.Status)),
		IsDefault: pkg.GetBoolValue(data.IsDefault, false),
		CreatedAt: data.CreatedAt,
		UpdatedAt: data.UpdatedAt,
	}
}

func mapMerchantBankFromParams(params internal.CreateMerchantBank, action string) (models.MerchantBank, error) {
	model := models.MerchantBank{
		BankName:              pkg.SetStringPointer(params.BankName, action),
		BankBranch:            pkg.SetStringPointer(params.BankBranch, action),
		BankAccountNumber:     pkg.SetStringPointer(params.BankAccountNumber, action),
		BankAccountHolderName: pkg.SetStringPointer(params.BankAccountHolderName, action),
		BankDetail: func() *models.Bank {
			if params.BankDetail != nil {
				return &models.Bank{
					Slug:        &params.BankDetail.Slug,
					Name:        &params.BankDetail.Name,
					BankCode:    &params.BankDetail.BankCode,
					Description: &params.BankDetail.Description,
					CanDisburse: &params.BankDetail.CanDisburse,
					Metadata:    &params.BankDetail.Metadata,
				}
			}
			return nil

		}(),
		IsDefault: pkg.SetBoolPointer(params.IsDefault, action),
		Metadata:  pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.MerchantBank{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.MerchantBank{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.MerchantBank{}, err
		}
		model.MerchantID = objectID
	}

	if params.BankID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.BankID)
		if err != nil {
			return models.MerchantBank{}, err
		}
		model.BankID = objectID
	}

	return model, nil
}
