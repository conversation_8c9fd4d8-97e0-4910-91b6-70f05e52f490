package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type TransferRepository struct {
	util riot.Util
}

func NewTransferRepository(util riot.Util) *TransferRepository {
	return &TransferRepository{
		util: util,
	}
}

func (r *TransferRepository) Create(ctx context.Context, params internal.CreateTransfer) (internal.Transfer, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapTransferFromParams(params, "create")

	if err != nil {
		return internal.Transfer{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.TransferCollection, &model)
	if err != nil {
		return internal.Transfer{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransferRepository.Create")
	}
	return mapTransferToInternalData(model), nil
}

func (r *TransferRepository) CreateBulk(ctx context.Context, params []internal.CreateTransfer) ([]internal.Transfer, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapTransferFromParams(param, "create")

		if err != nil {
			return []internal.Transfer{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Transfer{}).CreateBulk(ctx, r.util.DB, models.TransferCollection, listModels)

	if err != nil {
		return []internal.Transfer{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransferRepository.CreateBulk")
	}

	return []internal.Transfer{}, nil
}

func (r *TransferRepository) Update(ctx context.Context, id string, params internal.CreateTransfer) (internal.Transfer, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository.Repo", "Update"))

	model, err := mapTransferFromParams(params, "update")

	if err != nil {
		return internal.Transfer{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Transfer{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.TransferCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *TransferRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateTransfer) (internal.Transfer, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapTransferFromParams(params, "update")

	if err != nil {
		return internal.Transfer{}, err
	}

	model.Update(ctx, r.util.DB, models.TransferCollection, bsonM, &model)

	return mapTransferToInternalData(model), nil
}

func (r *TransferRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "SoftDelete"))
	var model models.Transfer
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransferRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.TransferCollection, bson.M{"_id": idHex})
}

func (r *TransferRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "SoftDeleteByFilter"))

	var model models.Transfer

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.TransferCollection, bsonM)
}

func (r *TransferRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Transfer, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "GetByFilter"))

	var model models.Transfer

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transfer{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.TransferCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Transfer{}, nil
		}

		return internal.Transfer{}, err
	}

	return mapTransferToInternalData(model), nil
}

func (r *TransferRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "SumByFilter"))

	var model models.Transfer

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.TransferCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *TransferRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "CountByFilter"))

	var model models.Transfer

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.TransferCollection, bsonM)
}

func (r *TransferRepository) GetByID(ctx context.Context, id string) (internal.Transfer, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "GetByID"))

	var model models.Transfer
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransferRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.TransferCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Transfer{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Transfer{}, err
	}
	return mapTransferToInternalData(model), nil
}

func (r *TransferRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Transfer, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "GetAll"))
	var model models.Transfer
	var listModels []models.Transfer

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.TransferCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Transfer{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransferRepository.FindAll")
	}

	var internalModels []internal.Transfer

	for _, externalModel := range listModels {
		internalModel := mapTransferToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *TransferRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Transfer, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "GetAll"))
	var model models.Transfer
	var listModels []models.Transfer

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transfer{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.TransferCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Transfer{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransferRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Transfer

	for _, externalModel := range listModels {
		internalModel := mapTransferToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *TransferRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.TransferPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransferRepository", "GetAllWithPagination"))
	var listModels []models.Transfer
	var model models.Transfer

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.TransferCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.TransferPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.TransferPagin{}, err
	}

	var internalModels []internal.Transfer

	for _, externalModel := range listModels {
		internalModel := mapTransferToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.TransferPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapTransferToInternalData(data models.Transfer) internal.Transfer {
	return internal.Transfer{
		ID:         data.ID.Hex(),
		CompanyID:  data.CompanyID.Hex(),
		ProfileID:  data.ProfileID.Hex(),
		MerchantID: data.MerchantID.Hex(),
		TransactionID: func() string {
			if data.TransactionID != nil {
				return data.TransactionID.Hex()
			}
			return ""
		}(),
		DownlineID: func() string {
			if data.DownlineID != nil {
				return data.DownlineID.Hex()
			}
			return ""
		}(),
		DisbursementID: func() string {
			if data.DisbursementID != nil {
				return data.DisbursementID.Hex()
			}
			return ""
		}(),
		Amount:      pkg.GetFloat64Value(data.Amount, 0.00),
		Type:        pkg.GetStringValue(data.Type, ""),
		ProductType: pkg.GetStringValue(data.ProductType, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		Metadata:    pkg.GetMapstring(data.Metadata, map[string]string{}),
		Operation:   pkg.GetStringValue(data.Operation, ""),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapTransferFromParams(params internal.CreateTransfer, action string) (models.Transfer, error) {
	model := models.Transfer{
		Amount:      pkg.SetFloat64Pointer(params.Amount, action),
		Description: pkg.SetStringPointer(params.Description, action),
		Metadata:    pkg.SetMapstringPointer(params.Metadata, action),
		Type:        pkg.SetStringPointer(params.Type, action),
		ProductType: pkg.SetStringPointer(params.ProductType, action),
		Operation:   pkg.SetStringPointer(params.Operation, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Transfer{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.CompanyID")
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.Transfer{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.ProfileID")
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.Transfer{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.MerchantID")
		}
		model.MerchantID = objectID
	}

	if params.TransactionID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.TransactionID)
		if err != nil {
			return models.Transfer{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.TransactionID")
		}
		model.TransactionID = &objectID
	}

	if params.DisbursementID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.DisbursementID)
		if err != nil {
			return models.Transfer{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.DisbursementID")
		}
		model.DisbursementID = &objectID
	}

	if params.DownlineID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.DownlineID)
		if err != nil {
			return models.Transfer{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.DownlineID")
		}
		model.DownlineID = &objectID
	}

	return model, nil
}
