package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type MerchantCommisionFeeRepository struct {
	util riot.Util
}

func NewMerchantCommisionFeeRepository(util riot.Util) *MerchantCommisionFeeRepository {
	return &MerchantCommisionFeeRepository{
		util: util,
	}
}

func (r *MerchantCommisionFeeRepository) Create(ctx context.Context, params internal.CreateMerchantCommissionFee) (internal.MerchantCommissionFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapMerchantCommisionFeeFromParams(params, "create")

	if err != nil {
		return internal.MerchantCommissionFee{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.MerchantCommissionCollection, &model)
	if err != nil {
		return internal.MerchantCommissionFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantCommisionFeeRepository.Create")
	}
	return mapMerchantCommisionToInternalData(model), nil
}

func (r *MerchantCommisionFeeRepository) CreateBulk(ctx context.Context, params []internal.CreateMerchantCommissionFee) ([]internal.MerchantCommissionFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapMerchantCommisionFeeFromParams(param, "create")

		if err != nil {
			return []internal.MerchantCommissionFee{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.MerchantCommissionFee{}).CreateBulk(ctx, r.util.DB, models.MerchantCommissionCollection, listModels)

	if err != nil {
		return []internal.MerchantCommissionFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantCommisionFeeRepository.CreateBulk")
	}

	return []internal.MerchantCommissionFee{}, nil
}

func (r *MerchantCommisionFeeRepository) Update(ctx context.Context, id string, params internal.CreateMerchantCommissionFee) (internal.MerchantCommissionFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository.Repo", "Update"))

	model, err := mapMerchantCommisionFeeFromParams(params, "update")

	if err != nil {
		return internal.MerchantCommissionFee{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.MerchantCommissionFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.MerchantCommissionCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *MerchantCommisionFeeRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantCommissionFee) (internal.MerchantCommissionFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantCommissionFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantCommissionFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantCommissionFee{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantCommissionFee{}, err
			}
			bsonM["merchant_id"] = objectID

		default:
			bsonM[key] = value
		}
	}

	model, err := mapMerchantCommisionFeeFromParams(params, "update")

	if err != nil {
		return internal.MerchantCommissionFee{}, err
	}

	model.Update(ctx, r.util.DB, models.MerchantCommissionCollection, bsonM, &model)

	return mapMerchantCommisionToInternalData(model), nil
}

func (r *MerchantCommisionFeeRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "SoftDelete"))
	var model models.MerchantCommissionFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantCommisionFeeRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.MerchantCommissionCollection, bson.M{"_id": idHex})
}

func (r *MerchantCommisionFeeRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "SoftDeleteByFilter"))

	var model models.MerchantCommissionFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.MerchantCommissionCollection, bsonM)
}

func (r *MerchantCommisionFeeRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantCommissionFee, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "GetByFilter"))

	var model models.MerchantCommissionFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantCommissionFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantCommissionFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantCommissionFee{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantCommissionFee{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.MerchantCommissionCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.MerchantCommissionFee{}, nil
		}

		return internal.MerchantCommissionFee{}, err
	}

	return mapMerchantCommisionToInternalData(model), nil
}

func (r *MerchantCommisionFeeRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "SumByFilter"))

	var model models.MerchantCommissionFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.MerchantCommissionCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *MerchantCommisionFeeRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "CountByFilter"))

	var model models.MerchantCommissionFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.MerchantCommissionCollection, bsonM)
}

func (r *MerchantCommisionFeeRepository) GetByID(ctx context.Context, id string) (internal.MerchantCommissionFee, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "GetByID"))

	var model models.MerchantCommissionFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantCommisionFeeRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.MerchantCommissionCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantCommissionFee{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantCommissionFee{}, err
	}
	return mapMerchantCommisionToInternalData(model), nil
}

func (r *MerchantCommisionFeeRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantCommissionFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "GetAll"))
	var model models.MerchantCommissionFee
	var listModels []models.MerchantCommissionFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantCommissionFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantCommissionFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantCommissionFee{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantCommissionFee{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.MerchantCommissionCollection, bsonM, &listModels)
	if err != nil {
		return []internal.MerchantCommissionFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantCommisionFeeRepository.FindAll")
	}

	var internalModels []internal.MerchantCommissionFee

	for _, externalModel := range listModels {
		internalModel := mapMerchantCommisionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantCommisionFeeRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantCommissionFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "GetAll"))
	var model models.MerchantCommissionFee
	var listModels []models.MerchantCommissionFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantCommissionFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantCommissionFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantCommissionFee{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantCommissionFee{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.MerchantCommissionCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.MerchantCommissionFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantCommisionFeeRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.MerchantCommissionFee

	for _, externalModel := range listModels {
		internalModel := mapMerchantCommisionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantCommisionFeeRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantCommissionFeePagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantCommisionFeeRepository", "GetAllWithPagination"))
	var listModels []models.MerchantCommissionFee
	var model models.MerchantCommissionFee

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.MerchantCommissionCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantCommissionFeePagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantCommissionFeePagin{}, err
	}

	var internalModels []internal.MerchantCommissionFee

	for _, externalModel := range listModels {
		internalModel := mapMerchantCommisionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.MerchantCommissionFeePagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapMerchantCommisionToInternalData(data models.MerchantCommissionFee) internal.MerchantCommissionFee {
	return internal.MerchantCommissionFee{
		ID:         data.ID.Hex(),
		CompanyID:  data.CompanyID.Hex(),
		ProfileID:  data.ProfileID.Hex(),
		DownlineID: data.DownlineID.Hex(),
		ReffID: func() string {
			if data.ReffID != nil {
				return data.ReffID.Hex()
			}
			return ""
		}(),
		MerchantID: data.MerchantID.Hex(),
		Fees: func() []internal.FeeDetail {
			if data.Fees != nil {
				feeDetails := make([]internal.FeeDetail, len(data.Fees))
				for i, feeDetail := range data.Fees {
					feeDetails[i] = internal.FeeDetail{
						PaymentMethod:            feeDetail.PaymentMethod,
						FeeType:                  feeDetail.FeeType,
						FeeValue:                 feeDetail.FeeValue,
						ProviderFeeType:          feeDetail.ProviderFeeType,
						ProviderFeeValue:         feeDetail.ProviderFeeValue,
						PlatformFeeType:          feeDetail.PlatformFeeType,
						PlatformFeeValue:         feeDetail.PlatformFeeValue,
						CashbackProviderFeeType:  feeDetail.CashbackProviderFeeType,
						CashbackProviderFeeValue: feeDetail.CashbackProviderFeeValue,
						ReferralFeeType:          feeDetail.ReferralFeeType,
						ReferralFeeValue:         feeDetail.ReferralFeeValue,
						MarketingFeeType:         feeDetail.MarketingFeeType,
						MarketingFeeValue:        feeDetail.MarketingFeeValue,
					}
				}
				return feeDetails
			}
			return []internal.FeeDetail{}
		}(),
		Metadata:  pkg.GetMapstring(data.Metadata, map[string]string{}),
		Provider:  pkg.GetStringValue(data.Provider, ""),
		Status:    constant.ReverseConstant(int(data.Status)),
		CreatedAt: data.CreatedAt,
		UpdatedAt: data.UpdatedAt,
	}
}

func mapMerchantCommisionFeeFromParams(params internal.CreateMerchantCommissionFee, action string) (models.MerchantCommissionFee, error) {
	model := models.MerchantCommissionFee{
		Fees: func() []models.FeeDetail {

			feeDetails := make([]models.FeeDetail, len(params.Fees))
			for i, feeDetail := range params.Fees {
				feeDetails[i] = models.FeeDetail{
					PaymentMethod:            pkg.SetStringUpperPointer(feeDetail.PaymentMethod, action),
					FeeType:                  pkg.SetStringPointer(feeDetail.FeeType, action),
					FeeValue:                 pkg.SetFloat64Pointer(feeDetail.FeeValue, action),
					ProviderFeeType:          pkg.SetStringPointer(feeDetail.ProviderFeeType, action),
					ProviderFeeValue:         pkg.SetFloat64Pointer(feeDetail.ProviderFeeValue, action),
					PlatformFeeType:          pkg.SetStringPointer(feeDetail.PlatformFeeType, action),
					PlatformFeeValue:         pkg.SetFloat64Pointer(feeDetail.PlatformFeeValue, action),
					CashbackProviderFeeType:  feeDetail.CashbackProviderFeeType,
					CashbackProviderFeeValue: feeDetail.CashbackProviderFeeValue,
					ReferralFeeType:          feeDetail.ReferralFeeType,
					ReferralFeeValue:         feeDetail.ReferralFeeValue,
					MarketingFeeType:         feeDetail.MarketingFeeType,
					MarketingFeeValue:        feeDetail.MarketingFeeValue,
				}
			}
			return feeDetails
		}(),
		Metadata: pkg.SetMapstringPointer(params.Metadata, action),
		Provider: pkg.SetStringPointer(params.Provider, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.MerchantCommissionFee{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.MerchantCommissionFee{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.MerchantCommissionFee{}, err
		}
		model.MerchantID = objectID
	}

	if params.DownlineID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.DownlineID)
		if err != nil {
			return models.MerchantCommissionFee{}, err
		}
		model.DownlineID = objectID
	}

	if params.ReffID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.ReffID)
		if err != nil {
			return models.MerchantCommissionFee{}, err
		}
		model.ReffID = &objectID
	}

	return model, nil
}
