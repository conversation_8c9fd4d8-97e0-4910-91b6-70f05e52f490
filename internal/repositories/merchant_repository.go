package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type MerchantRepository struct {
	util riot.Util
}

func NewMerchantRepository(util riot.Util) *MerchantRepository {
	return &MerchantRepository{
		util: util,
	}
}

func (r *MerchantRepository) Create(ctx context.Context, params internal.CreateMerchant) (internal.Merchant, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository.Repo", "Create"), zap.Any("params", params))

	//generate slug
	uniqSlug, err := (&models.Merchant{}).GenerateUniqueSlug(ctx, r.util.DB, models.MerchantCollection, *params.LoginID)

	if err != nil {
		return internal.Merchant{}, err
	}

	params.Slug = &uniqSlug

	model, err := mapMerchantFromParams(params, "create")

	if err != nil {
		return internal.Merchant{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.MerchantCollection, &model)
	if err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantRepository.Create")
	}
	return mapMerchantToInternalData(model), nil
}

func (r *MerchantRepository) CreateBulk(ctx context.Context, params []internal.CreateMerchant) ([]internal.Merchant, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapMerchantFromParams(param, "create")

		if err != nil {
			return []internal.Merchant{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Merchant{}).CreateBulk(ctx, r.util.DB, models.MerchantCollection, listModels)

	if err != nil {
		return []internal.Merchant{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantRepository.CreateBulk")
	}

	return []internal.Merchant{}, nil
}

func (r *MerchantRepository) Update(ctx context.Context, id string, params internal.CreateMerchant) (internal.Merchant, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository.Repo", "Update"))

	model, err := mapMerchantUpdateFromParams(params, "update")

	if err != nil {
		return internal.Merchant{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	if err = model.Update(ctx, r.util.DB, models.MerchantCollection, bson.M{"_id": objectID}, &model); err != nil {
		return internal.Merchant{}, err
	}

	return r.GetByID(ctx, id)
}

func (r *MerchantRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchant) (internal.Merchant, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "UpdateByFilter"))

	bsonM := make(bson.M)

	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Merchant{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Merchant{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Merchant{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapMerchantUpdateFromParams(params, "update")

	if err != nil {
		return internal.Merchant{}, err
	}

	err = (&models.Merchant{}).Update(ctx, r.util.DB, models.MerchantCollection, bsonM, &model)

	if err != nil {
		return internal.Merchant{}, err
	}

	return r.GetByFilter(ctx, filter)
}

func (r *MerchantRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "SoftDelete"))
	var model models.Merchant
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.MerchantCollection, bson.M{"_id": idHex})
}

func (r *MerchantRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "SoftDeleteByFilter"))

	var model models.Merchant

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.MerchantCollection, bsonM)
}

func (r *MerchantRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Merchant, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "GetByFilter"))

	var model models.Merchant

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Merchant{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Merchant{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Merchant{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.MerchantCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Merchant{}, nil
		}

		return internal.Merchant{}, err
	}

	return mapMerchantToInternalData(model), nil
}

func (r *MerchantRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "SumByFilter"))

	var model models.Merchant

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.MerchantCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *MerchantRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "CountByFilter"))

	var model models.Merchant

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.MerchantCollection, bsonM)
}

func (r *MerchantRepository) GetByID(ctx context.Context, id string) (internal.Merchant, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "GetByID"))

	var model models.Merchant
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.MerchantCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Merchant{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Merchant{}, err
	}
	return mapMerchantToInternalData(model), nil
}

func (r *MerchantRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Merchant, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "GetAll"))
	var model models.Merchant
	var listModels []models.Merchant

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Merchant{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Merchant{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Merchant{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.MerchantCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Merchant{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantRepository.FindAll")
	}

	var internalModels []internal.Merchant

	for _, externalModel := range listModels {
		internalModel := mapMerchantToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Merchant, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "GetAll"))
	var model models.Merchant
	var listModels []models.Merchant

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Merchant{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Merchant{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Merchant{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.MerchantCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Merchant{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Merchant

	for _, externalModel := range listModels {
		internalModel := mapMerchantToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantRepository", "GetAllWithPagination"))
	var listModels []models.Merchant
	var model models.Merchant

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.MerchantCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantPagin{}, err
	}

	var internalModels []internal.Merchant

	for _, externalModel := range listModels {
		internalModel := mapMerchantToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.MerchantPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapMerchantToInternalData(data models.Merchant) internal.Merchant {
	return internal.Merchant{
		ID:                data.ID.Hex(),
		CompanyID:         data.CompanyID.Hex(),
		ProfileID:         data.ProfileID.Hex(),
		Slug:              pkg.GetStringValue(data.Slug, ""),
		Name:              pkg.GetStringValue(data.Name, ""),
		LoginID:           pkg.GetStringValue(data.LoginID, ""),
		Email:             pkg.GetStringValue(data.Email, ""),
		ProfilePictureURL: pkg.GetStringValue(data.ProfilePictureURL, ""),
		SubAccounts:       data.SubAccounts,
		Balance:           pkg.GetFloat64Value(data.Balance, 0.0),
		SettledBalance:    pkg.GetFloat64Value(data.SettledBalance, 0.0),
		ReferrerID: func() string {
			if data.ReferrerID != nil {
				return data.ReferrerID.Hex()
			}
			return ""
		}(),
		MarketingID: func() string {
			if data.MarketingID != nil {
				return data.MarketingID.Hex()
			}
			return ""
		}(),
		VoucherID: func() string {
			if data.VoucherID != nil {
				return data.VoucherID.Hex()
			}
			return ""
		}(),
		Description: pkg.GetStringValue(data.Description, ""),
		Metadata:    pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapMerchantFromParams(params internal.CreateMerchant, action string) (models.Merchant, error) {

	model := models.Merchant{
		Slug:              pkg.SetStringPointer(params.Slug, action),
		Name:              pkg.SetStringPointer(params.Name, action),
		LoginID:           pkg.SetStringPointer(params.LoginID, action),
		Email:             pkg.SetStringPointer(params.Email, action),
		ProfilePictureURL: pkg.SetStringPointer(params.ProfilePictureURL, action),
		Balance:           pkg.SetFloat64Pointer(params.Balance, action),
		SettledBalance:    pkg.SetFloat64Pointer(params.SettledBalance, action),
		TotalDisburse:     pkg.SetFloat64Pointer(params.TotalDisburse, action),
		Description:       pkg.SetStringPointer(params.Description, action),
		Metadata:          pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Merchant{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.Merchant{}, err
		}
		model.ProfileID = objectID
	}

	// if params.SubAccounts != nil {
	// 	model.SubAccounts = params.SubAccounts
	// }

	if params.ReferrerID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ReferrerID)
		if err != nil {
			return models.Merchant{}, err
		}
		model.ReferrerID = &objectID
	}

	if params.MarketingID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MarketingID)
		if err != nil {
			return models.Merchant{}, err
		}
		model.MarketingID = &objectID
	}

	if params.VoucherID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.VoucherID)
		if err != nil {
			return models.Merchant{}, err
		}
		model.VoucherID = &objectID
	}

	return model, nil
}

func mapMerchantUpdateFromParams(params internal.CreateMerchant, action string) (models.MerchantUpdate, error) {

	model := models.MerchantUpdate{
		Slug:              pkg.SetStringPointer(params.Slug, action),
		Name:              pkg.SetStringPointer(params.Name, action),
		Email:             pkg.SetStringPointer(params.Email, action),
		ProfilePictureURL: pkg.SetStringPointer(params.ProfilePictureURL, action),
		Balance:           pkg.SetFloat64Pointer(params.Balance, action),
		SettledBalance:    pkg.SetFloat64Pointer(params.SettledBalance, action),
		TotalDisburse:     pkg.SetFloat64Pointer(params.TotalDisburse, action),
		Description:       pkg.SetStringPointer(params.Description, action),
		Metadata:          pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.MerchantUpdate{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.CompanyID")
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.MerchantUpdate{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.ProfileID")
		}
		model.ProfileID = objectID
	}

	// if params.SubAccounts != nil {
	// 	model.SubAccounts = params.SubAccounts
	// }

	if params.ReferrerID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ReferrerID)
		if err != nil {
			return models.MerchantUpdate{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.ReferrerID")
		}
		model.ReferrerID = &objectID
	}

	if params.MarketingID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MarketingID)
		if err != nil {
			return models.MerchantUpdate{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.MarketingID")
		}
		model.MarketingID = &objectID
	}

	if params.VoucherID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.VoucherID)
		if err != nil {
			return models.MerchantUpdate{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapTransferFromParams.params.VoucherID")
		}
		model.VoucherID = &objectID
	}

	return model, nil
}
