package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

type PaymentRepository struct {
	util riot.Util
}

func NewPaymentRepository(util riot.Util) *PaymentRepository {
	return &PaymentRepository{
		util: util,
	}
}

func (r *PaymentRepository) Create(ctx context.Context, params internal.CreatePayment) (internal.Payment, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapPaymentFromParams(params, "create")

	if err != nil {
		return internal.Payment{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.PaymentCollection, &model)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PaymentRepository.Create")
	}
	return mapPaymentToInternalData(model), nil
}

func (r *PaymentRepository) CreateBulk(ctx context.Context, params []internal.CreatePayment) ([]internal.Payment, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapPaymentFromParams(param, "create")

		if err != nil {
			return []internal.Payment{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Payment{}).CreateBulk(ctx, r.util.DB, models.PaymentCollection, listModels)

	if err != nil {
		return []internal.Payment{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PaymentRepository.CreateBulk")
	}

	return []internal.Payment{}, nil
}

func (r *PaymentRepository) Update(ctx context.Context, id string, params internal.CreatePayment) (internal.Payment, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository.Repo", "Update"))

	model, err := mapPaymentFromParams(params, "update")

	if err != nil {
		return internal.Payment{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	if err := model.Update(ctx, r.util.DB, models.PaymentCollection, bson.M{"_id": objectID}, &model); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "Update")
	}

	return r.GetByID(ctx, id)
}

func (r *PaymentRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreatePayment) (internal.Payment, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapPaymentFromParams(params, "update")

	if err != nil {
		return internal.Payment{}, err
	}

	model.Update(ctx, r.util.DB, models.PaymentCollection, bsonM, &model)

	return mapPaymentToInternalData(model), nil
}

func (r *PaymentRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "SoftDelete"))
	var model models.Payment
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.PaymentCollection, bson.M{"_id": idHex})
}

func (r *PaymentRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "SoftDeleteByFilter"))

	var model models.Payment

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.PaymentCollection, bsonM)
}

func (r *PaymentRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Payment, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "GetByFilter"))

	var model models.Payment

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.PaymentCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Payment{}, nil
		}

		return internal.Payment{}, err
	}

	return mapPaymentToInternalData(model), nil
}

func (r *PaymentRepository) GetByFilterWithOption(ctx context.Context, filter map[string]interface{}) (internal.Payment, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "GetByFilter"))

	var model models.Payment

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Payment{}, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	opt := options.FindOne()
	opt.SetSort(bson.D{{Key: "created_at", Value: -1}})

	err := model.FirstWithOption(ctx, r.util.DB, models.PaymentCollection, bsonM, opt, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Payment{}, nil
		}

		return internal.Payment{}, err
	}

	return mapPaymentToInternalData(model), nil
}

func (r *PaymentRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "SumByFilter"))

	var model models.Payment

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.PaymentCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *PaymentRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "CountByFilter"))

	var model models.Payment

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.PaymentCollection, bsonM)
}

func (r *PaymentRepository) GetByID(ctx context.Context, id string) (internal.Payment, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "GetByID"))

	var model models.Payment
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.PaymentCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Payment{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Payment{}, err
	}
	return mapPaymentToInternalData(model), nil
}

func (r *PaymentRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Payment, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "GetAll"))
	var model models.Payment
	var listModels []models.Payment

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.PaymentCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Payment{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PaymentRepository.FindAll")
	}

	var internalModels []internal.Payment

	for _, externalModel := range listModels {
		internalModel := mapPaymentToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *PaymentRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Payment, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "GetAll"))
	var model models.Payment
	var listModels []models.Payment

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Payment{}, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.PaymentCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Payment{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PaymentRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Payment

	for _, externalModel := range listModels {
		internalModel := mapPaymentToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *PaymentRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.PaymentPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentRepository", "GetAllWithPagination"))
	var listModels []models.Payment
	var model models.Payment

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.PaymentCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.PaymentPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.PaymentPagin{}, err
	}

	var internalModels []internal.Payment

	for _, externalModel := range listModels {
		internalModel := mapPaymentToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.PaymentPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapPaymentToInternalData(data models.Payment) internal.Payment {
	return internal.Payment{
		ID:         data.ID.Hex(),
		CompanyID:  data.CompanyID.Hex(),
		ProfileID:  data.ProfileID.Hex(),
		MerchantID: data.MerchantID.Hex(),
		BuyerID: func() string {
			if data.BuyerID != nil {
				return data.BuyerID.Hex()
			}
			return ""
		}(),
		ReferrerID: func() string {
			if data.ReferrerID != nil {
				return data.ReferrerID.Hex()
			}
			return ""
		}(),
		MarketingID: func() string {
			if data.MarketingID != nil {
				return data.MarketingID.Hex()
			}
			return ""
		}(),
		VoucherID: func() string {
			if data.VoucherID != nil {
				return data.VoucherID.Hex()
			}
			return ""
		}(),
		TransactionID:             data.TransactionID.Hex(),
		TransactionNo:             pkg.GetStringValue(data.TransactionNo, ""),
		BuyerEmail:                pkg.GetStringValue(data.BuyerEmail, ""),
		UniqueNo:                  pkg.GetStringValue(data.UniqueNo, ""),
		PaymentNo:                 pkg.GetStringValue(data.PaymentNo, ""),
		ReferenceNo:               pkg.GetStringValue(data.ReferenceNo, ""),
		ExternalID:                pkg.GetStringValue(data.ExternalID, ""),
		ProviderInvoiceID:         pkg.GetStringValue(data.ProviderInvoiceID, ""),
		ProviderPaymentID:         pkg.GetStringValue(data.ProviderPaymentID, ""),
		ProviderBusinessID:        pkg.GetStringValue(data.ProviderBusinessID, ""),
		PaymentType:               pkg.GetStringValue(data.PaymentType, ""),
		PaymentMethod:             pkg.GetStringValue(data.PaymentMethod, ""),
		PaymentChannel:            pkg.GetStringValue(data.PaymentChannel, ""),
		QRString:                  pkg.GetStringValue(data.QRString, ""),
		VirtualAccount:            pkg.GetStringValue(data.VirtualAccount, ""),
		EWalletType:               pkg.GetStringValue(data.EWalletType, ""),
		CheckoutMethod:            pkg.GetStringValue(data.CheckoutMethod, ""),
		ChannelCode:               pkg.GetStringValue(data.ChannelCode, ""),
		SuccessRedirectURL:        pkg.GetStringValue(data.SuccessRedirectURL, ""),
		FailureRedirectURL:        pkg.GetStringValue(data.FailureRedirectURL, ""),
		IsRedirectRequired:        pkg.GetBoolValue(data.IsRedirectRequired, false),
		MobileNumber:              pkg.GetStringValue(data.MobileNumber, ""),
		CashTag:                   pkg.GetStringValue(data.CashTag, ""),
		DesktopWebCheckoutURL:     pkg.GetStringValue(data.DesktopWebCheckoutURL, ""),
		MobileWebCheckoutURL:      pkg.GetStringValue(data.MobileWebCheckoutURL, ""),
		MobileDeepLinkCheckoutURL: pkg.GetStringValue(data.MobileDeepLinkCheckoutURL, ""),
		Description:               pkg.GetStringValue(data.Description, ""),
		Amount:                    pkg.GetFloat64Value(data.Amount, 0.0),
		ChargeAmount:              pkg.GetFloat64Value(data.ChargeAmount, 0.0),
		PGFee:                     pkg.GetFloat64Value(data.PGFee, 0.0),
		PGRealFee:                 pkg.GetFloat64Value(data.PGRealFee, 0.0),
		PGPlatformFee:             pkg.GetFloat64Value(data.PGPlatformFee, 0.0),
		Notes:                     pkg.GetStringValue(data.Notes, ""),
		Metadata:                  pkg.GetMapInterface(data.Metadata, map[string]interface{}{}),
		Currency:                  pkg.GetStringValue(data.Currency, ""),
		UseVoucher:                pkg.GetBoolValue(data.UseVoucher, false),
		VoucherCode:               pkg.GetStringValue(data.VoucherCode, ""),
		Provider:                  pkg.GetStringValue(data.Provider, ""),
		Status:                    constant.ReverseConstant(int(data.Status)),
		ExpiresAt:                 data.ExpiresAt,
		PaidAt:                    data.PaidAt,
		FailedAt:                  data.FailedAt,
		OverdueAt:                 data.OverdueAt,
		CreatedAt:                 data.CreatedAt,
		UpdatedAt:                 data.UpdatedAt,
	}
}

func mapPaymentFromParams(params internal.CreatePayment, action string) (models.Payment, error) {
	model := models.Payment{
		TransactionNo:             pkg.SetStringUpperPointer(params.TransactionNo, action),
		BuyerEmail:                pkg.SetStringPointer(params.BuyerEmail, action),
		UniqueNo:                  pkg.SetStringPointer(params.UniqueNo, action),
		PaymentNo:                 pkg.SetStringUpperPointer(params.PaymentNo, action),
		ReferenceNo:               pkg.SetStringUpperPointer(params.ReferenceNo, action),
		ExternalID:                pkg.SetStringUpperPointer(params.ExternalID, action),
		ProviderInvoiceID:         pkg.SetStringPointer(params.ProviderInvoiceID, action),
		ProviderPaymentID:         pkg.SetStringPointer(params.ProviderPaymentID, action),
		ProviderBusinessID:        pkg.SetStringPointer(params.ProviderBusinessID, action),
		PaymentType:               pkg.SetStringPointer(params.PaymentType, action),
		PaymentMethod:             pkg.SetStringUpperPointer(params.PaymentMethod, action),
		PaymentChannel:            pkg.SetStringPointer(params.PaymentChannel, action),
		QRString:                  pkg.SetStringPointer(params.QRString, action),
		VirtualAccount:            pkg.SetStringPointer(params.VirtualAccount, action),
		EWalletType:               pkg.SetStringPointer(params.EWalletType, action),
		CheckoutMethod:            pkg.SetStringPointer(params.CheckoutMethod, action),
		ChannelCode:               pkg.SetStringPointer(params.ChannelCode, action),
		SuccessRedirectURL:        pkg.SetStringPointer(params.SuccessRedirectURL, action),
		FailureRedirectURL:        pkg.SetStringPointer(params.FailureRedirectURL, action),
		IsRedirectRequired:        pkg.SetBoolPointer(params.IsRedirectRequired, action),
		MobileNumber:              pkg.SetStringPointer(params.MobileNumber, action),
		CashTag:                   pkg.SetStringPointer(params.CashTag, action),
		DesktopWebCheckoutURL:     pkg.SetStringPointer(params.DesktopWebCheckoutURL, action),
		MobileWebCheckoutURL:      pkg.SetStringPointer(params.MobileWebCheckoutURL, action),
		MobileDeepLinkCheckoutURL: pkg.SetStringPointer(params.MobileDeepLinkCheckoutURL, action),
		Description:               pkg.SetStringPointer(params.Description, action),
		Amount:                    pkg.SetFloat64Pointer(params.Amount, action),
		ChargeAmount:              pkg.SetFloat64Pointer(params.ChargeAmount, action),
		PGFee:                     pkg.SetFloat64Pointer(params.PGFee, action),
		PGRealFee:                 pkg.SetFloat64Pointer(params.PGRealFee, action),
		PGPlatformFee:             pkg.SetFloat64Pointer(params.PGPlatformFee, action),
		Notes:                     pkg.SetStringPointer(params.Notes, action),
		Metadata:                  pkg.SetMapInterfacePointer(params.Metadata, action),
		Currency:                  pkg.SetStringPointer(params.Currency, action),
		UseVoucher:                pkg.SetBoolPointer(params.UseVoucher, action),
		VoucherCode:               pkg.SetStringPointer(params.VoucherCode, action),
		Provider:                  pkg.SetStringPointer(params.Provider, action),
		PaidAt:                    params.PaidAt,
		FailedAt:                  params.FailedAt,
		ExpiresAt:                 params.ExpiresAt,
		OverdueAt:                 params.OverdueAt,
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Payment{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.Payment{}, err
		}
		model.ProfileID = objectID
	}

	if params.TransactionID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.TransactionID)
		if err != nil {
			return models.Payment{}, err
		}
		model.TransactionID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.Payment{}, err
		}
		model.MerchantID = objectID
	}

	if params.BuyerID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.BuyerID)
		if err != nil {
			return models.Payment{}, err
		}
		model.BuyerID = &objectID
	}

	if params.ReferrerID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ReferrerID)
		if err != nil {
			return models.Payment{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "invalid referrer id")
		}
		model.ReferrerID = &objectID
	}

	if params.MarketingID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MarketingID)
		if err != nil {
			return models.Payment{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "invalid marketing id")
		}
		model.MarketingID = &objectID
	}

	if params.VoucherID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.VoucherID)
		if err != nil {
			return models.Payment{}, riot.NewErrorf(riot.ErrorCodeNotFound, "invalid voucher id")
		}
		model.VoucherID = &objectID
	}

	return model, nil
}
