package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type BankRepository struct {
	util riot.Util
}

func NewBankRepository(util riot.Util) *BankRepository {
	return &BankRepository{
		util: util,
	}
}

func (r *BankRepository) Create(ctx context.Context, params internal.CreateBank) (internal.Bank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository.Repo", "Create"), zap.Any("params", params))

	//generate slug
	uniqSlug, err := (&models.Bank{}).GenerateUniqueSlug(ctx, r.util.DB, models.BankCollection, *params.Name)

	if err != nil {
		return internal.Bank{}, err
	}

	params.Slug = &uniqSlug
	model, err := mapBankFromParams(params, "create")

	if err != nil {
		return internal.Bank{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.BankCollection, &model)
	if err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "BankRepository.Create")
	}
	return mapBankToInternalData(model), nil
}

func (r *BankRepository) CreateBulk(ctx context.Context, params []internal.CreateBank) ([]internal.Bank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		//generate slug
		uniqSlug, err := (&models.Bank{}).GenerateUniqueSlug(ctx, r.util.DB, models.BankCollection, *param.Name)

		if err != nil {
			return []internal.Bank{}, err
		}

		param.Slug = &uniqSlug
		model, err := mapBankFromParams(param, "create")

		if err != nil {
			return []internal.Bank{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Bank{}).CreateBulk(ctx, r.util.DB, models.BankCollection, listModels)

	if err != nil {
		return []internal.Bank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "BankRepository.CreateBulk")
	}

	return []internal.Bank{}, nil
}

func (r *BankRepository) Update(ctx context.Context, id string, params internal.CreateBank) (internal.Bank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository.Repo", "Update"))

	model, err := mapBankFromParams(params, "update")

	if err != nil {
		return internal.Bank{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.BankCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *BankRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateBank) (internal.Bank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Bank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Bank{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapBankFromParams(params, "update")

	if err != nil {
		return internal.Bank{}, err
	}

	model.Update(ctx, r.util.DB, models.BankCollection, bsonM, &model)

	return mapBankToInternalData(model), nil
}

func (r *BankRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "SoftDelete"))
	var model models.Bank
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "BankRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.BankCollection, bson.M{"_id": idHex})
}

func (r *BankRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "SoftDeleteByFilter"))

	var model models.Bank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.BankCollection, bsonM)
}

func (r *BankRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Bank, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "GetByFilter"))

	var model models.Bank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Bank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Bank{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.BankCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Bank{}, nil
		}

		return internal.Bank{}, err
	}

	return mapBankToInternalData(model), nil
}

func (r *BankRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "SumByFilter"))

	var model models.Bank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.BankCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *BankRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "CountByFilter"))

	var model models.Bank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.BankCollection, bsonM)
}

func (r *BankRepository) GetByID(ctx context.Context, id string) (internal.Bank, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "GetByID"))

	var model models.Bank
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "BankRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.BankCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Bank{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Bank{}, err
	}
	return mapBankToInternalData(model), nil
}

func (r *BankRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Bank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "GetAll"))
	var model models.Bank
	var listModels []models.Bank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Bank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Bank{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.BankCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Bank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "BankRepository.FindAll")
	}

	var internalModels []internal.Bank

	for _, externalModel := range listModels {
		internalModel := mapBankToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *BankRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Bank, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "GetAll"))
	var model models.Bank
	var listModels []models.Bank

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Bank{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Bank{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.BankCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Bank{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "BankRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Bank

	for _, externalModel := range listModels {
		internalModel := mapBankToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *BankRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.BankPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BankRepository", "GetAllWithPagination"))
	var listModels []models.Bank
	var model models.Bank

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.BankCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.BankPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.BankPagin{}, err
	}

	var internalModels []internal.Bank

	for _, externalModel := range listModels {
		internalModel := mapBankToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.BankPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapBankToInternalData(data models.Bank) internal.Bank {
	return internal.Bank{
		ID:          data.ID.Hex(),
		Slug:        pkg.GetStringValue(data.Slug, ""),
		Name:        pkg.GetStringValue(data.Name, ""),
		BankCode:    pkg.GetStringValue(data.BankCode, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		Metadata:    pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CanDisburse: pkg.GetBoolValue(data.CanDisburse, false),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapBankFromParams(params internal.CreateBank, action string) (models.Bank, error) {
	model := models.Bank{
		Slug:        pkg.SetStringPointer(params.Slug, action),
		Name:        pkg.SetStringPointer(params.Name, action),
		BankCode:    pkg.SetStringPointer(params.BankCode, action),
		Description: pkg.SetStringPointer(params.Description, action),
		CanDisburse: pkg.SetBoolPointer(params.CanDisburse, action),
		Metadata:    pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	return model, nil
}
