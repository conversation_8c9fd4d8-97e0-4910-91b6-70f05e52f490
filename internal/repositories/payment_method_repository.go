package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type PaymentMethodRepository struct {
	util riot.Util
}

func NewPaymentMethodRepository(util riot.Util) *PaymentMethodRepository {
	return &PaymentMethodRepository{
		util: util,
	}
}

func (r *PaymentMethodRepository) Create(ctx context.Context, params internal.CreatePaymentMethod) (internal.PaymentMethod, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository.Repo", "Create"), zap.Any("params", params))

	//generate slug
	uniqSlug, err := (&models.PaymentMethod{}).GenerateUniqueSlug(ctx, r.util.DB, models.PaymentMethodCollection, *params.Name)

	if err != nil {
		return internal.PaymentMethod{}, err
	}

	params.Slug = &uniqSlug

	model, err := mapPaymentMethodFromParams(params, "create")

	if err != nil {
		return internal.PaymentMethod{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.PaymentMethodCollection, &model)
	if err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodRepository.Create")
	}
	return mapPaymentMethodToInternalData(model), nil
}

func (r *PaymentMethodRepository) CreateBulk(ctx context.Context, params []internal.CreatePaymentMethod) ([]internal.PaymentMethod, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapPaymentMethodFromParams(param, "create")

		if err != nil {
			return []internal.PaymentMethod{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.PaymentMethod{}).CreateBulk(ctx, r.util.DB, models.PaymentMethodCollection, listModels)

	if err != nil {
		return []internal.PaymentMethod{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodRepository.CreateBulk")
	}

	return []internal.PaymentMethod{}, nil
}

func (r *PaymentMethodRepository) Update(ctx context.Context, id string, params internal.CreatePaymentMethod) (internal.PaymentMethod, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository.Repo", "Update"))

	model, err := mapPaymentMethodFromParams(params, "update")

	if err != nil {
		return internal.PaymentMethod{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.PaymentMethodCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *PaymentMethodRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreatePaymentMethod) (internal.PaymentMethod, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PaymentMethod{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PaymentMethod{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PaymentMethod{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapPaymentMethodFromParams(params, "update")

	if err != nil {
		return internal.PaymentMethod{}, err
	}

	model.Update(ctx, r.util.DB, models.PaymentMethodCollection, bsonM, &model)

	return mapPaymentMethodToInternalData(model), nil
}

func (r *PaymentMethodRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "SoftDelete"))
	var model models.PaymentMethod
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentMethodRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.PaymentMethodCollection, bson.M{"_id": idHex})
}

func (r *PaymentMethodRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "SoftDeleteByFilter"))

	var model models.PaymentMethod

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.PaymentMethodCollection, bsonM)
}

func (r *PaymentMethodRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.PaymentMethod, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "GetByFilter"))

	var model models.PaymentMethod

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PaymentMethod{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PaymentMethod{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PaymentMethod{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.PaymentMethodCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.PaymentMethod{}, nil
		}

		return internal.PaymentMethod{}, err
	}

	return mapPaymentMethodToInternalData(model), nil
}

func (r *PaymentMethodRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "SumByFilter"))

	var model models.PaymentMethod

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.PaymentMethodCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *PaymentMethodRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "CountByFilter"))

	var model models.PaymentMethod

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.PaymentMethodCollection, bsonM)
}

func (r *PaymentMethodRepository) GetByID(ctx context.Context, id string) (internal.PaymentMethod, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "GetByID"))

	var model models.PaymentMethod
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentMethodRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.PaymentMethodCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.PaymentMethod{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.PaymentMethod{}, err
	}
	return mapPaymentMethodToInternalData(model), nil
}

func (r *PaymentMethodRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.PaymentMethod, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "GetAll"))
	var model models.PaymentMethod
	var listModels []models.PaymentMethod

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PaymentMethod{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PaymentMethod{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PaymentMethod{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.PaymentMethodCollection, bsonM, &listModels)
	if err != nil {
		return []internal.PaymentMethod{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodRepository.FindAll")
	}

	var internalModels []internal.PaymentMethod

	for _, externalModel := range listModels {
		internalModel := mapPaymentMethodToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *PaymentMethodRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.PaymentMethod, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "GetAll"))
	var model models.PaymentMethod
	var listModels []models.PaymentMethod

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PaymentMethod{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PaymentMethod{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PaymentMethod{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.PaymentMethodCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.PaymentMethod{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.PaymentMethod

	for _, externalModel := range listModels {
		internalModel := mapPaymentMethodToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *PaymentMethodRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.PaymentMethodPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodRepository", "GetAllWithPagination"))
	var listModels []models.PaymentMethod
	var model models.PaymentMethod

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.PaymentMethodCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.PaymentMethodPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.PaymentMethodPagin{}, err
	}

	var internalModels []internal.PaymentMethod

	for _, externalModel := range listModels {
		internalModel := mapPaymentMethodToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.PaymentMethodPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapPaymentMethodToInternalData(data models.PaymentMethod) internal.PaymentMethod {
	return internal.PaymentMethod{
		ID:                 data.ID.Hex(),
		CompanyID:          data.CompanyID.Hex(),
		Slug:               pkg.GetStringValue(data.Slug, ""),
		Name:               pkg.GetStringValue(data.Name, ""),
		DisplayName:        pkg.GetStringValue(data.DisplayName, ""),
		Description:        pkg.GetStringValue(data.Description, ""),
		ChannelCode:        pkg.GetStringValue(data.ChannelCode, ""),
		CheckoutMethod:     pkg.GetStringValue(data.CheckoutMethod, ""),
		FeeType:            pkg.GetStringValue(data.FeeType, ""),
		FeeValue:           pkg.GetFloat64Value(data.FeeValue, 0),
		ExtraFeeType:       pkg.GetStringValue(data.ExtraFeeType, ""),
		ExtraFeeValue:      pkg.GetFloat64Value(data.ExtraFeeValue, 0),
		PGFeeType:          pkg.GetStringValue(data.PGFeeType, ""),
		PGFeeValue:         pkg.GetFloat64Value(data.PGFeeValue, 0),
		PGExtraFeeType:     pkg.GetStringValue(data.PGExtraFeeType, ""),
		PGExtraFeeValue:    pkg.GetFloat64Value(data.PGExtraFeeValue, 0),
		Provider:           pkg.GetStringValue(data.Provider, ""),
		SuccessRedirectURL: pkg.GetStringValue(data.SuccessRedirectURL, ""),
		FailureRedirectURL: pkg.GetStringValue(data.FailureRedirectURL, ""),
		IsRedirectRequired: pkg.GetBoolValue(data.IsRedirectRequired, false),
		Metadata:           pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:             constant.ReverseConstant(int(data.Status)),
		Type:               pkg.GetStringValue(data.Type, ""),
		CreatedAt:          data.CreatedAt,
		UpdatedAt:          data.UpdatedAt,
	}
}

func mapPaymentMethodFromParams(params internal.CreatePaymentMethod, action string) (models.PaymentMethod, error) {
	model := models.PaymentMethod{
		Slug:               pkg.SetStringPointer(params.Slug, action),
		Name:               pkg.SetStringUpperPointer(params.Name, action),
		DisplayName:        pkg.SetStringPointer(params.DisplayName, action),
		Description:        pkg.SetStringPointer(params.Description, action),
		ChannelCode:        pkg.SetStringUpperPointer(params.ChannelCode, action),
		CheckoutMethod:     pkg.SetStringUpperPointer(params.CheckoutMethod, action),
		FeeType:            pkg.SetStringPointer(params.FeeType, action),
		FeeValue:           pkg.SetFloat64Pointer(params.FeeValue, action),
		ExtraFeeType:       pkg.SetStringPointer(params.ExtraFeeType, action),
		ExtraFeeValue:      pkg.SetFloat64Pointer(params.ExtraFeeValue, action),
		PGFeeType:          pkg.SetStringPointer(params.PGFeeType, action),
		PGFeeValue:         pkg.SetFloat64Pointer(params.PGFeeValue, action),
		PGExtraFeeType:     pkg.SetStringPointer(params.PGExtraFeeType, action),
		PGExtraFeeValue:    pkg.SetFloat64Pointer(params.PGExtraFeeValue, action),
		SuccessRedirectURL: pkg.SetStringPointer(params.SuccessRedirectURL, action),
		FailureRedirectURL: pkg.SetStringPointer(params.FailureRedirectURL, action),
		IsRedirectRequired: pkg.SetBoolPointer(params.IsRedirectRequired, action),
		Type:               pkg.SetStringUpperPointer(params.Type, action),
		Provider:           pkg.SetStringPointer(params.Provider, action),
		Metadata:           pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.PaymentMethod{}, err
		}
		model.CompanyID = objectID
	}

	return model, nil
}
