package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type CoinRepository struct {
	util riot.Util
}

func NewCoinRepository(util riot.Util) *CoinRepository {
	return &CoinRepository{
		util: util,
	}
}

func (r *CoinRepository) Create(ctx context.Context, params internal.CreateCoin) (internal.Coin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapCoinFromParams(params, "create")

	if err != nil {
		return internal.Coin{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.CoinCollection, &model)
	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinRepository.Create")
	}
	return mapCoinToInternalData(model), nil
}

func (r *CoinRepository) CreateBulk(ctx context.Context, params []internal.CreateCoin) ([]internal.Coin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapCoinFromParams(param, "create")

		if err != nil {
			return []internal.Coin{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Coin{}).CreateBulk(ctx, r.util.DB, models.CoinCollection, listModels)

	if err != nil {
		return []internal.Coin{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinRepository.CreateBulk")
	}

	return []internal.Coin{}, nil
}

func (r *CoinRepository) Update(ctx context.Context, id string, params internal.CreateCoin) (internal.Coin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository.Repo", "Update"))

	model, err := mapCoinFromParams(params, "update")

	if err != nil {
		return internal.Coin{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.CoinCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *CoinRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateCoin) (internal.Coin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapCoinFromParams(params, "update")

	if err != nil {
		return internal.Coin{}, err
	}

	model.Update(ctx, r.util.DB, models.CoinCollection, bsonM, &model)

	return mapCoinToInternalData(model), nil
}

func (r *CoinRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "SoftDelete"))
	var model models.Coin
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.CoinCollection, bson.M{"_id": idHex})
}

func (r *CoinRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "SoftDeleteByFilter"))

	var model models.Coin

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.CoinCollection, bsonM)
}

func (r *CoinRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Coin, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "GetByFilter"))

	var model models.Coin

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Coin{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.CoinCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Coin{}, nil
		}

		return internal.Coin{}, err
	}

	return mapCoinToInternalData(model), nil
}

func (r *CoinRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "SumByFilter"))

	var model models.Coin

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.CoinCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *CoinRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "CountByFilter"))

	var model models.Coin

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.CoinCollection, bsonM)
}

func (r *CoinRepository) GetByID(ctx context.Context, id string) (internal.Coin, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "GetByID"))

	var model models.Coin
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.CoinCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Coin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Coin{}, err
	}
	return mapCoinToInternalData(model), nil
}

func (r *CoinRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Coin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "GetAll"))
	var model models.Coin
	var listModels []models.Coin

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.CoinCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Coin{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinRepository.FindAll")
	}

	var internalModels []internal.Coin

	for _, externalModel := range listModels {
		internalModel := mapCoinToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *CoinRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Coin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "GetAll"))
	var model models.Coin
	var listModels []models.Coin

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Coin{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.CoinCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Coin{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Coin

	for _, externalModel := range listModels {
		internalModel := mapCoinToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *CoinRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.CoinPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinRepository", "GetAllWithPagination"))
	var listModels []models.Coin
	var model models.Coin

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.CoinCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.CoinPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.CoinPagin{}, err
	}

	var internalModels []internal.Coin

	for _, externalModel := range listModels {
		internalModel := mapCoinToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.CoinPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapCoinToInternalData(data models.Coin) internal.Coin {
	return internal.Coin{
		ID:          data.ID.Hex(),
		CompanyID:   data.CompanyID.Hex(),
		ProfileID:   data.ProfileID.Hex(),
		LoginID:     pkg.GetStringValue(data.LoginID, ""),
		Balance:     pkg.GetFloat64Value(data.Balance, 0),
		Description: pkg.GetStringValue(data.Description, ""),
		Metadata:    pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapCoinFromParams(params internal.CreateCoin, action string) (models.Coin, error) {
	model := models.Coin{
		LoginID:     pkg.SetStringUpperPointer(params.LoginID, action),
		Balance:     pkg.SetFloat64Pointer(params.Balance, action),
		Description: pkg.SetStringPointer(params.Description, action),
		Metadata:    pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Coin{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinFromParams.params.CompanyID")
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.Coin{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinFromParams.params.ProfileID")
		}
		model.ProfileID = objectID
	}

	return model, nil
}
