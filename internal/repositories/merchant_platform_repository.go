package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type MerchantPlatformRepository struct {
	util riot.Util
}

func NewMerchantPlatformRepository(util riot.Util) *MerchantPlatformRepository {
	return &MerchantPlatformRepository{
		util: util,
	}
}

func (r *MerchantPlatformRepository) Create(ctx context.Context, params internal.CreateMerchantPlatform) (internal.MerchantPlatform, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository.Repo", "Create"), zap.Any("params", params))

	//generate slug
	uniqSlug, err := (&models.MerchantPlatform{}).GenerateUniqueSlug(ctx, r.util.DB, models.MerchantPlatformCollection, *params.Name)

	if err != nil {
		return internal.MerchantPlatform{}, err
	}

	params.Slug = &uniqSlug

	model, err := mapMerchantPlatformFromParams(params, "create")

	if err != nil {
		return internal.MerchantPlatform{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.MerchantPlatformCollection, &model)
	if err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformRepository.Create")
	}
	return mapMerchantPlatformToInternalData(model), nil
}

func (r *MerchantPlatformRepository) CreateBulk(ctx context.Context, params []internal.CreateMerchantPlatform) ([]internal.MerchantPlatform, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapMerchantPlatformFromParams(param, "create")

		if err != nil {
			return []internal.MerchantPlatform{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.MerchantPlatform{}).CreateBulk(ctx, r.util.DB, models.MerchantPlatformCollection, listModels)

	if err != nil {
		return []internal.MerchantPlatform{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformRepository.CreateBulk")
	}

	return []internal.MerchantPlatform{}, nil
}

func (r *MerchantPlatformRepository) Update(ctx context.Context, id string, params internal.CreateMerchantPlatform) (internal.MerchantPlatform, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository.Repo", "Update"))

	model, err := mapMerchantPlatformFromParams(params, "update")

	if err != nil {
		return internal.MerchantPlatform{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.MerchantPlatformCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *MerchantPlatformRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantPlatform) (internal.MerchantPlatform, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatform{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatform{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatform{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapMerchantPlatformFromParams(params, "update")

	if err != nil {
		return internal.MerchantPlatform{}, err
	}

	model.Update(ctx, r.util.DB, models.MerchantPlatformCollection, bsonM, &model)

	return mapMerchantPlatformToInternalData(model), nil
}

func (r *MerchantPlatformRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "SoftDelete"))
	var model models.MerchantPlatform
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.MerchantPlatformCollection, bson.M{"_id": idHex})
}

func (r *MerchantPlatformRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "SoftDeleteByFilter"))

	var model models.MerchantPlatform

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.MerchantPlatformCollection, bsonM)
}

func (r *MerchantPlatformRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantPlatform, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "GetByFilter"))

	var model models.MerchantPlatform

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatform{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatform{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatform{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.MerchantPlatformCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.MerchantPlatform{}, nil
		}

		return internal.MerchantPlatform{}, err
	}

	return mapMerchantPlatformToInternalData(model), nil
}

func (r *MerchantPlatformRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "SumByFilter"))

	var model models.MerchantPlatform

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.MerchantPlatformCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *MerchantPlatformRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "CountByFilter"))

	var model models.MerchantPlatform

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.MerchantPlatformCollection, bsonM)
}

func (r *MerchantPlatformRepository) GetByID(ctx context.Context, id string) (internal.MerchantPlatform, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "GetByID"))

	var model models.MerchantPlatform
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.MerchantPlatformCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantPlatform{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantPlatform{}, err
	}
	return mapMerchantPlatformToInternalData(model), nil
}

func (r *MerchantPlatformRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantPlatform, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "GetAll"))
	var model models.MerchantPlatform
	var listModels []models.MerchantPlatform

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatform{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatform{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatform{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.MerchantPlatformCollection, bsonM, &listModels)
	if err != nil {
		return []internal.MerchantPlatform{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformRepository.FindAll")
	}

	var internalModels []internal.MerchantPlatform

	for _, externalModel := range listModels {
		internalModel := mapMerchantPlatformToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantPlatformRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantPlatform, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "GetAll"))
	var model models.MerchantPlatform
	var listModels []models.MerchantPlatform

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatform{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatform{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatform{}, err
			}
			bsonM["company_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.MerchantPlatformCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.MerchantPlatform{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.MerchantPlatform

	for _, externalModel := range listModels {
		internalModel := mapMerchantPlatformToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantPlatformRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantPlatformPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformRepository", "GetAllWithPagination"))
	var listModels []models.MerchantPlatform
	var model models.MerchantPlatform

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.MerchantPlatformCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantPlatformPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantPlatformPagin{}, err
	}

	var internalModels []internal.MerchantPlatform

	for _, externalModel := range listModels {
		internalModel := mapMerchantPlatformToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.MerchantPlatformPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapMerchantPlatformToInternalData(data models.MerchantPlatform) internal.MerchantPlatform {
	return internal.MerchantPlatform{
		ID:          data.ID.Hex(),
		CompanyID:   data.CompanyID.Hex(),
		MerchantID:  data.MerchantID.Hex(),
		Slug:        pkg.GetStringValue(data.Slug, ""),
		Name:        pkg.GetStringValue(data.Name, ""),
		DisplayName: pkg.GetStringValue(data.DisplayName, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		Metadata:    pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapMerchantPlatformFromParams(params internal.CreateMerchantPlatform, action string) (models.MerchantPlatform, error) {
	model := models.MerchantPlatform{
		Slug:        pkg.SetStringPointer(params.Slug, action),
		Name:        pkg.SetStringUpperPointer(params.Name, action),
		DisplayName: pkg.SetStringPointer(params.DisplayName, action),
		Description: pkg.SetStringPointer(params.Description, action),
		Metadata:    pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.MerchantPlatform{}, err
		}
		model.CompanyID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.MerchantPlatform{}, err
		}
		model.MerchantID = objectID
	}

	return model, nil
}
