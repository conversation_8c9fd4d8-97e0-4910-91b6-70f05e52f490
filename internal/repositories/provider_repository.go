package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ProviderRepository struct {
	util riot.Util
}

func NewProviderRepository(util riot.Util) *ProviderRepository {
	return &ProviderRepository{
		util: util,
	}
}

func (r *ProviderRepository) Create(ctx context.Context, params internal.CreateProvider) (internal.Provider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository.Repo", "Create"), zap.Any("params", params))

	//generate slug
	uniqSlug, err := (&models.Provider{}).GenerateUniqueSlug(ctx, r.util.DB, models.ProviderCollection, *params.Name)

	if err != nil {
		return internal.Provider{}, err
	}

	params.Slug = &uniqSlug

	model, err := mapProviderFromParams(params, "create")

	if err != nil {
		return internal.Provider{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.ProviderCollection, &model)
	if err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRepository.Create")
	}
	return mapProviderToInternalData(model), nil
}

func (r *ProviderRepository) CreateBulk(ctx context.Context, params []internal.CreateProvider) ([]internal.Provider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapProviderFromParams(param, "create")

		if err != nil {
			return []internal.Provider{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Provider{}).CreateBulk(ctx, r.util.DB, models.ProviderCollection, listModels)

	if err != nil {
		return []internal.Provider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRepository.CreateBulk")
	}

	return []internal.Provider{}, nil
}

func (r *ProviderRepository) Update(ctx context.Context, id string, params internal.CreateProvider) (internal.Provider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository.Repo", "Update"))

	model, err := mapProviderFromParams(params, "update")

	if err != nil {
		return internal.Provider{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.ProviderCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *ProviderRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateProvider) (internal.Provider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Provider{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Provider{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapProviderFromParams(params, "update")

	if err != nil {
		return internal.Provider{}, err
	}

	model.Update(ctx, r.util.DB, models.ProviderCollection, bsonM, &model)

	return mapProviderToInternalData(model), nil
}

func (r *ProviderRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "SoftDelete"))
	var model models.Provider
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.ProviderCollection, bson.M{"_id": idHex})
}

func (r *ProviderRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "SoftDeleteByFilter"))

	var model models.Provider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.ProviderCollection, bsonM)
}

func (r *ProviderRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Provider, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "GetByFilter"))

	var model models.Provider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Provider{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Provider{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.ProviderCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Provider{}, nil
		}

		return internal.Provider{}, err
	}

	return mapProviderToInternalData(model), nil
}

func (r *ProviderRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "SumByFilter"))

	var model models.Provider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.ProviderCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *ProviderRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "CountByFilter"))

	var model models.Provider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.ProviderCollection, bsonM)
}

func (r *ProviderRepository) GetByID(ctx context.Context, id string) (internal.Provider, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "GetByID"))

	var model models.Provider
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.ProviderCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Provider{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Provider{}, err
	}
	return mapProviderToInternalData(model), nil
}

func (r *ProviderRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Provider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "GetAll"))
	var model models.Provider
	var listModels []models.Provider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Provider{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Provider{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.ProviderCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Provider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRepository.FindAll")
	}

	var internalModels []internal.Provider

	for _, externalModel := range listModels {
		internalModel := mapProviderToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ProviderRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Provider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "GetAll"))
	var model models.Provider
	var listModels []models.Provider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Provider{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Provider{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.ProviderCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Provider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Provider

	for _, externalModel := range listModels {
		internalModel := mapProviderToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ProviderRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ProviderPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRepository", "GetAllWithPagination"))
	var listModels []models.Provider
	var model models.Provider

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.ProviderCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ProviderPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ProviderPagin{}, err
	}

	var internalModels []internal.ProviderProtected

	for _, externalModel := range listModels {
		internalModel := mapProviderToInternalDataProtected(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.ProviderPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapProviderToInternalData(data models.Provider) internal.Provider {
	return internal.Provider{
		ID:          data.ID.Hex(),
		Slug:        pkg.GetStringValue(data.Slug, ""),
		Name:        pkg.GetStringValue(data.Name, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		Secret:      pkg.GetMapstring(data.Secret, map[string]string{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapProviderToInternalDataProtected(data models.Provider) internal.ProviderProtected {
	return internal.ProviderProtected{
		ID:          data.ID.Hex(),
		Slug:        pkg.GetStringValue(data.Slug, ""),
		Name:        pkg.GetStringValue(data.Name, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapProviderFromParams(params internal.CreateProvider, action string) (models.Provider, error) {
	model := models.Provider{
		Slug:        pkg.SetStringPointer(params.Slug, action),
		Name:        pkg.SetStringPointer(params.Name, action),
		Secret:      pkg.SetMapstringPointer(params.Secret, action),
		Description: pkg.SetStringPointer(params.Description, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	return model, nil
}
