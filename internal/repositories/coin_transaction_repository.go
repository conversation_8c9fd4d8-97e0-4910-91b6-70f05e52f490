package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type CoinTransactionRepository struct {
	util riot.Util
}

func NewCoinTransactionRepository(util riot.Util) *CoinTransactionRepository {
	return &CoinTransactionRepository{
		util: util,
	}
}

func (r *CoinTransactionRepository) Create(ctx context.Context, params internal.CreateCoinTransaction) (internal.CoinTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapCoinTxnFromParams(params, "create")

	if err != nil {
		return internal.CoinTransaction{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.CoinTransactionCollection, &model)
	if err != nil {
		return internal.CoinTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionRepository.Create")
	}
	return mapCoinTxnToInternalData(model), nil
}

func (r *CoinTransactionRepository) CreateBulk(ctx context.Context, params []internal.CreateCoinTransaction) ([]internal.CoinTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapCoinTxnFromParams(param, "create")

		if err != nil {
			return []internal.CoinTransaction{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.CoinTransaction{}).CreateBulk(ctx, r.util.DB, models.CoinTransactionCollection, listModels)

	if err != nil {
		return []internal.CoinTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionRepository.CreateBulk")
	}

	return []internal.CoinTransaction{}, nil
}

func (r *CoinTransactionRepository) Update(ctx context.Context, id string, params internal.CreateCoinTransaction) (internal.CoinTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository.Repo", "Update"))

	model, err := mapCoinTxnFromParams(params, "update")

	if err != nil {
		return internal.CoinTransaction{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.CoinTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.CoinTransactionCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *CoinTransactionRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateCoinTransaction) (internal.CoinTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["coin_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["source_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["type_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapCoinTxnFromParams(params, "update")

	if err != nil {
		return internal.CoinTransaction{}, err
	}

	model.Update(ctx, r.util.DB, models.CoinTransactionCollection, bsonM, &model)

	return mapCoinTxnToInternalData(model), nil
}

func (r *CoinTransactionRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "SoftDelete"))
	var model models.CoinTransaction
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.CoinTransactionCollection, bson.M{"_id": idHex})
}

func (r *CoinTransactionRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "SoftDeleteByFilter"))

	var model models.CoinTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["coin_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["source_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["type_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.CoinTransactionCollection, bsonM)
}

func (r *CoinTransactionRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.CoinTransaction, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "GetByFilter"))

	var model models.CoinTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["coin_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["source_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["type_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinTransaction{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.CoinTransactionCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.CoinTransaction{}, nil
		}

		return internal.CoinTransaction{}, err
	}

	return mapCoinTxnToInternalData(model), nil
}

func (r *CoinTransactionRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "SumByFilter"))

	var model models.CoinTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["type_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["source_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["coin_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.CoinTransactionCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *CoinTransactionRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "CountByFilter"))

	var model models.CoinTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["type_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["source_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["coin_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.CoinTransactionCollection, bsonM)
}

func (r *CoinTransactionRepository) GetByID(ctx context.Context, id string) (internal.CoinTransaction, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "GetByID"))

	var model models.CoinTransaction
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.CoinTransactionCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.CoinTransaction{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.CoinTransaction{}, err
	}
	return mapCoinTxnToInternalData(model), nil
}

func (r *CoinTransactionRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.CoinTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "GetAll"))
	var model models.CoinTransaction
	var listModels []models.CoinTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["type_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["source_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["coin_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.CoinTransactionCollection, bsonM, &listModels)
	if err != nil {
		return []internal.CoinTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionRepository.FindAll")
	}

	var internalModels []internal.CoinTransaction

	for _, externalModel := range listModels {
		internalModel := mapCoinTxnToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *CoinTransactionRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.CoinTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "GetAll"))
	var model models.CoinTransaction
	var listModels []models.CoinTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["type_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["source_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["coin_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinTransaction{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.CoinTransactionCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.CoinTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.CoinTransaction

	for _, externalModel := range listModels {
		internalModel := mapCoinTxnToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *CoinTransactionRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.CoinTransactionPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionRepository", "GetAllWithPagination"))
	var listModels []models.CoinTransaction
	var model models.CoinTransaction

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.CoinTransactionCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.CoinTransactionPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.CoinTransactionPagin{}, err
	}

	var internalModels []internal.CoinTransaction

	for _, externalModel := range listModels {
		internalModel := mapCoinTxnToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.CoinTransactionPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapCoinTxnToInternalData(data models.CoinTransaction) internal.CoinTransaction {
	return internal.CoinTransaction{
		ID:        data.ID.Hex(),
		CompanyID: data.CompanyID.Hex(),
		ProfileID: data.ProfileID.Hex(),
		CoinID:    data.CoinID.Hex(),
		TypeID: func() string {
			if data.TypeID != nil {
				return data.TypeID.Hex()
			}
			return ""
		}(),
		SourceID: func() string {
			if data.SourceID != nil {
				return data.SourceID.Hex()
			}
			return ""
		}(),
		Type:          pkg.GetIntValue(data.Type, 0),
		Balance:       pkg.GetFloat64Value(data.Balance, 0),
		BalanceBefore: pkg.GetFloat64Value(data.BalanceBefore, 0),
		Description:   pkg.GetStringValue(data.Description, ""),
		Metadata:      pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:        constant.ReverseConstant(int(data.Status)),
		CreatedAt:     data.CreatedAt,
		UpdatedAt:     data.UpdatedAt,
	}
}

func mapCoinTxnFromParams(params internal.CreateCoinTransaction, action string) (models.CoinTransaction, error) {
	model := models.CoinTransaction{
		Type:          pkg.SetIntPointer(params.Type, action),
		Balance:       pkg.SetFloat64Pointer(params.Balance, action),
		BalanceBefore: pkg.SetFloat64Pointer(params.BalanceBefore, action),
		Description:   pkg.SetStringPointer(params.Description, action),
		Metadata:      pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.CoinTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinTxnFromParams.params.CompanyID")
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.CoinTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinTxnFromParams.params.ProfileID")
		}
		model.ProfileID = objectID
	}

	if params.CoinID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CoinID)
		if err != nil {
			return models.CoinTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinTxnFromParams.params.CoinID")
		}
		model.CoinID = objectID
	}

	if params.TypeID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.TypeID)
		if err != nil {
			return models.CoinTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinTxnFromParams.params.TypeID")
		}
		model.TypeID = &objectID
	}

	if params.SourceID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.SourceID)
		if err != nil {
			return models.CoinTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinTxnFromParams.params.SourceID")
		}
		model.SourceID = &objectID
	}

	return model, nil
}
