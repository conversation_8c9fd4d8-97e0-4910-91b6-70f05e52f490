package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ConfigurationRepository struct {
	util riot.Util
}

func NewConfigurationRepository(util riot.Util) *ConfigurationRepository {
	return &ConfigurationRepository{
		util: util,
	}
}

func (r *ConfigurationRepository) Create(ctx context.Context, params internal.CreateConfiguration) (internal.Configuration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository.Repo", "Create"), zap.Any("params", params))

	//generate slug
	uniqSlug, err := (&models.Configuration{}).GenerateUniqueSlug(ctx, r.util.DB, models.ConfigurationCollection, *params.Name)

	if err != nil {
		return internal.Configuration{}, err
	}

	params.Slug = uniqSlug

	model, err := mapConfigurationFromParams(params, "create")

	if err != nil {
		return internal.Configuration{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.ConfigurationCollection, &model)
	if err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationRepository.Create")
	}
	return mapConfigurationToInternalData(model), nil
}

func (r *ConfigurationRepository) CreateBulk(ctx context.Context, params []internal.CreateConfiguration) ([]internal.Configuration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapConfigurationFromParams(param, "create")

		if err != nil {
			return []internal.Configuration{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Configuration{}).CreateBulk(ctx, r.util.DB, models.ConfigurationCollection, listModels)

	if err != nil {
		return []internal.Configuration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationRepository.CreateBulk")
	}

	return []internal.Configuration{}, nil
}

func (r *ConfigurationRepository) Update(ctx context.Context, id string, params internal.CreateConfiguration) (internal.Configuration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository.Repo", "Update"))

	model, err := mapConfigurationFromParams(params, "update")

	if err != nil {
		return internal.Configuration{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.ConfigurationCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *ConfigurationRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateConfiguration) (internal.Configuration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Configuration{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Configuration{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Configuration{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Configuration{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapConfigurationFromParams(params, "update")

	if err != nil {
		return internal.Configuration{}, err
	}

	model.Update(ctx, r.util.DB, models.ConfigurationCollection, bsonM, &model)

	return mapConfigurationToInternalData(model), nil
}

func (r *ConfigurationRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "SoftDelete"))
	var model models.Configuration
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ConfigurationRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.ConfigurationCollection, bson.M{"_id": idHex})
}

func (r *ConfigurationRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "SoftDeleteByFilter"))

	var model models.Configuration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.ConfigurationCollection, bsonM)
}

func (r *ConfigurationRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Configuration, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "GetByFilter"))

	var model models.Configuration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Configuration{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Configuration{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Configuration{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Configuration{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.ConfigurationCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Configuration{}, nil
		}

		return internal.Configuration{}, err
	}

	return mapConfigurationToInternalData(model), nil
}

func (r *ConfigurationRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "SumByFilter"))

	var model models.Configuration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.ConfigurationCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *ConfigurationRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "CountByFilter"))

	var model models.Configuration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.ConfigurationCollection, bsonM)
}

func (r *ConfigurationRepository) GetByID(ctx context.Context, id string) (internal.Configuration, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "GetByID"))

	var model models.Configuration
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ConfigurationRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.ConfigurationCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Configuration{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Configuration{}, err
	}
	return mapConfigurationToInternalData(model), nil
}

func (r *ConfigurationRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Configuration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "GetAll"))
	var model models.Configuration
	var listModels []models.Configuration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Configuration{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Configuration{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Configuration{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Configuration{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.ConfigurationCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Configuration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationRepository.FindAll")
	}

	var internalModels []internal.Configuration

	for _, externalModel := range listModels {
		internalModel := mapConfigurationToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ConfigurationRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Configuration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "GetAll"))
	var model models.Configuration
	var listModels []models.Configuration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Configuration{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Configuration{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Configuration{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Configuration{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.ConfigurationCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Configuration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Configuration

	for _, externalModel := range listModels {
		internalModel := mapConfigurationToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ConfigurationRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ConfigurationPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationRepository", "GetAllWithPagination"))
	var listModels []models.Configuration
	var model models.Configuration

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.ConfigurationCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ConfigurationPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ConfigurationPagin{}, err
	}

	var internalModels []internal.Configuration

	for _, externalModel := range listModels {
		internalModel := mapConfigurationToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.ConfigurationPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapConfigurationToInternalData(data models.Configuration) internal.Configuration {
	return internal.Configuration{
		ID:          data.ID.Hex(),
		CompanyID:   data.CompanyID.Hex(),
		Name:        pkg.GetStringValue(data.Name, ""),
		DisplayName: pkg.GetStringValue(data.DisplayName, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		FeeType:     pkg.GetStringValue(data.FeeType, ""),
		FeeValue:    pkg.GetFloat64Value(data.FeeValue, 0),
		Currency:    pkg.GetStringValue(data.Currency, ""),
		Metadata:    pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapConfigurationFromParams(params internal.CreateConfiguration, action string) (models.Configuration, error) {
	model := models.Configuration{
		Name:        pkg.SetStringUpperPointer(params.Name, action),
		DisplayName: pkg.SetStringPointer(params.DisplayName, action),
		Description: pkg.SetStringLowerPointer(params.Description, action),
		FeeType:     pkg.SetStringLowerPointer(params.FeeType, action),
		FeeValue:    pkg.SetFloat64Pointer(params.FeeValue, action),
		Metadata:    pkg.SetMapstringPointer(params.Metadata, action),
		Currency:    pkg.SetStringUpperPointer(params.Currency, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Configuration{}, err
		}
		model.CompanyID = objectID
	}

	return model, nil
}
