package repositories

import (
	"context"
	"strings"
	"time"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type MerchantVoucherRepository struct {
	util riot.Util
}

func NewMerchantVoucherRepository(util riot.Util) *MerchantVoucherRepository {
	return &MerchantVoucherRepository{
		util: util,
	}
}

func (r *MerchantVoucherRepository) Create(ctx context.Context, params internal.CreateMerchantVoucher) (internal.MerchantVoucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapMerchantVoucherFromParams(params, "create")

	if err != nil {
		return internal.MerchantVoucher{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.MerchantVoucherCollection, &model)
	if err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherRepository.Create")
	}
	return mapMerchantVoucherToInternalData(model), nil
}

func (r *MerchantVoucherRepository) CreateBulk(ctx context.Context, params []internal.CreateMerchantVoucher) ([]internal.MerchantVoucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapMerchantVoucherFromParams(param, "create")

		if err != nil {
			return []internal.MerchantVoucher{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.MerchantVoucher{}).CreateBulk(ctx, r.util.DB, models.MerchantVoucherCollection, listModels)

	if err != nil {
		return []internal.MerchantVoucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherRepository.CreateBulk")
	}

	return []internal.MerchantVoucher{}, nil
}

func (r *MerchantVoucherRepository) Update(ctx context.Context, id string, params internal.CreateMerchantVoucher) (internal.MerchantVoucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository.Repo", "Update"))

	model, err := mapMerchantVoucherFromParams(params, "update")

	if err != nil {
		return internal.MerchantVoucher{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.MerchantVoucherCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *MerchantVoucherRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantVoucher) (internal.MerchantVoucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantVoucher{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantVoucher{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantVoucher{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantVoucher{}, err
			}
			bsonM["merchant_id"] = objectID

		default:
			bsonM[key] = value
		}
	}

	model, err := mapMerchantVoucherFromParams(params, "update")

	if err != nil {
		return internal.MerchantVoucher{}, err
	}

	model.Update(ctx, r.util.DB, models.MerchantVoucherCollection, bsonM, &model)

	return mapMerchantVoucherToInternalData(model), nil
}

func (r *MerchantVoucherRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "SoftDelete"))
	var model models.MerchantVoucher
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.MerchantVoucherCollection, bson.M{"_id": idHex})
}

func (r *MerchantVoucherRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "SoftDeleteByFilter"))

	var model models.MerchantVoucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.MerchantVoucherCollection, bsonM)
}

func (r *MerchantVoucherRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantVoucher, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "GetByFilter"))

	var model models.MerchantVoucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantVoucher{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantVoucher{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantVoucher{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantVoucher{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.MerchantVoucherCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.MerchantVoucher{}, nil
		}

		return internal.MerchantVoucher{}, err
	}

	return mapMerchantVoucherToInternalData(model), nil
}

func (r *MerchantVoucherRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "SumByFilter"))

	var model models.MerchantVoucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.MerchantVoucherCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *MerchantVoucherRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "CountByFilter"))

	var model models.MerchantVoucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.MerchantVoucherCollection, bsonM)
}

func (r *MerchantVoucherRepository) GetByID(ctx context.Context, id string) (internal.MerchantVoucher, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "GetByID"))

	var model models.MerchantVoucher
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.MerchantVoucherCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantVoucher{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantVoucher{}, err
	}
	return mapMerchantVoucherToInternalData(model), nil
}

func (r *MerchantVoucherRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantVoucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "GetAll"))
	var model models.MerchantVoucher
	var listModels []models.MerchantVoucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantVoucher{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantVoucher{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantVoucher{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantVoucher{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.MerchantVoucherCollection, bsonM, &listModels)
	if err != nil {
		return []internal.MerchantVoucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherRepository.FindAll")
	}

	var internalModels []internal.MerchantVoucher

	for _, externalModel := range listModels {
		internalModel := mapMerchantVoucherToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantVoucherRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantVoucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "GetAll"))
	var model models.MerchantVoucher
	var listModels []models.MerchantVoucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantVoucher{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantVoucher{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantVoucher{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantVoucher{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.MerchantVoucherCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.MerchantVoucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.MerchantVoucher

	for _, externalModel := range listModels {
		internalModel := mapMerchantVoucherToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantVoucherRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantVoucherPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherRepository", "GetAllWithPagination"))
	var listModels []models.MerchantVoucher
	var model models.MerchantVoucher

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.MerchantVoucherCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantVoucherPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantVoucherPagin{}, err
	}

	var internalModels []internal.MerchantVoucher

	for _, externalModel := range listModels {
		internalModel := mapMerchantVoucherToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.MerchantVoucherPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapMerchantVoucherToInternalData(data models.MerchantVoucher) internal.MerchantVoucher {
	return internal.MerchantVoucher{
		ID:               data.ID.Hex(),
		CompanyID:        data.CompanyID.Hex(),
		ProfileID:        data.ProfileID.Hex(),
		MerchantID:       data.MerchantID.Hex(),
		VoucherID:        data.VoucherID.Hex(),
		Code:             pkg.GetStringValue(data.Code, ""),
		DiscountType:     pkg.GetStringValue(data.DiscountType, ""),
		DiscountValue:    pkg.GetFloat64Value(data.DiscountValue, 0),
		MinPurchase:      pkg.GetFloat64Value(data.MinPurchase, 0),
		MaxDiscount:      pkg.GetFloat64Value(data.MaxDiscount, 0),
		ExpiresAt:        data.ExpiresAt,
		ExpireType:       data.ExpireType,
		ValidFrom:        data.ValidFrom,
		ValidTo:          data.ValidTo,
		Period:           pkg.GetIntValue(data.Period, 0),
		PeriodType:       pkg.GetStringValue(data.PeriodType, ""),
		LevelPriority:    pkg.GetIntValue(data.LevelPriority, 0),
		Type:             pkg.GetStringValue(data.Type, ""),
		Combined:         pkg.GetBoolValue(data.Combined, false),
		CombinationTypes: *data.CombinationTypes,
		ProductType:      data.ProductType,
		Metadata:         pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:           constant.ReverseConstant(int(data.Status)),
		CreatedAt:        data.CreatedAt,
		UpdatedAt:        data.UpdatedAt,
	}
}

func mapMerchantVoucherFromParams(params internal.CreateMerchantVoucher, action string) (models.MerchantVoucher, error) {
	model := models.MerchantVoucher{
		Code:             pkg.SetStringUpperPointer(params.Code, action),
		CombinationTypes: pkg.SetStringSlicerPointer(params.CombinationTypes, action),
		Combined:         pkg.SetBoolPointer(params.Combined, action),
		DiscountType:     pkg.SetStringPointer(params.DiscountType, action),
		Period:           pkg.SetIntPointer(params.Period, action),
		PeriodType:       pkg.SetStringPointer(params.PeriodType, action),
		LevelPriority:    pkg.SetIntPointer(params.LevelPriority, action),
		MinPurchase:      pkg.SetFloat64Pointer(params.MinPurchase, action),
		MaxDiscount:      pkg.SetFloat64Pointer(params.MaxDiscount, action),
		Type:             pkg.SetStringPointer(params.Type, action),
		ExpiresAt: func() *time.Time {
			if params.ExpiresAt != "" {
				expiresAt, _ := pkg.ParseDateString(params.ExpiresAt, pkg.DateLayoutDefault)
				return &expiresAt
			}
			return nil
		}(),
		ExpireType: params.ExpireType,
		ValidFrom: func() *time.Time {
			if params.ValidFrom != "" {
				validFrom, _ := pkg.ParseDateString(params.ValidFrom, pkg.DateLayoutDefault)
				return &validFrom
			}
			return nil
		}(),
		ValidTo: func() *time.Time {
			if params.ValidTo != "" {
				validTo, _ := pkg.ParseDateString(params.ValidTo, pkg.DateLayoutDefault)
				return &validTo
			}
			return nil
		}(),
		ProductType: func() string {
			if params.ProductType != "" {
				return strings.ToUpper(params.ProductType)
			}
			return ""
		}(),
		Metadata: pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if model.ValidTo != nil {
		model.ExpiresAt = model.ValidTo
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.MerchantVoucher{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.MerchantVoucher{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.MerchantVoucher{}, err
		}
		model.MerchantID = objectID
	}

	if params.VoucherID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.VoucherID)
		if err != nil {
			return models.MerchantVoucher{}, err
		}
		model.VoucherID = objectID
	}

	return model, nil
}
