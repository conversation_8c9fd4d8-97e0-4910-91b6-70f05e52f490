package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type DisbursementConfigurationRepository struct {
	util riot.Util
}

func NewDisbursementConfigurationRepository(util riot.Util) *DisbursementConfigurationRepository {
	return &DisbursementConfigurationRepository{
		util: util,
	}
}

func (r *DisbursementConfigurationRepository) Create(ctx context.Context, params internal.CreateDisbursementConfiguration) (internal.DisbursementConfiguration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapDisbursementConfigurationFromParams(params, "create")

	if err != nil {
		return internal.DisbursementConfiguration{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.DisbursementConfigurationCollection, &model)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementConfigurationRepository.Create")
	}
	return mapDisbursementConfigurationToInternalData(model), nil
}

func (r *DisbursementConfigurationRepository) CreateBulk(ctx context.Context, params []internal.CreateDisbursementConfiguration) ([]internal.DisbursementConfiguration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapDisbursementConfigurationFromParams(param, "create")

		if err != nil {
			return []internal.DisbursementConfiguration{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.DisbursementConfiguration{}).CreateBulk(ctx, r.util.DB, models.DisbursementConfigurationCollection, listModels)

	if err != nil {
		return []internal.DisbursementConfiguration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementConfigurationRepository.CreateBulk")
	}

	return []internal.DisbursementConfiguration{}, nil
}

func (r *DisbursementConfigurationRepository) Update(ctx context.Context, id string, params internal.CreateDisbursementConfiguration) (internal.DisbursementConfiguration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository.Repo", "Update"))

	model, err := mapDisbursementConfigurationFromParams(params, "update")

	if err != nil {
		return internal.DisbursementConfiguration{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	err = model.Update(ctx, r.util.DB, models.DisbursementConfigurationCollection, bson.M{"_id": objectID}, &model)

	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementConfigurationRepository.Update")
	}

	return r.GetByID(ctx, id)
}

func (r *DisbursementConfigurationRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateDisbursementConfiguration) (internal.DisbursementConfiguration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapDisbursementConfigurationFromParams(params, "update")

	if err != nil {
		return internal.DisbursementConfiguration{}, err
	}

	if err = model.Update(ctx, r.util.DB, models.DisbursementConfigurationCollection, bsonM, &model); err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementConfigurationRepository.UpdateByFilter")
	}

	return r.GetByFilter(ctx, filter)
}

func (r *DisbursementConfigurationRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "SoftDelete"))
	var model models.DisbursementConfiguration
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementConfigurationRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.DisbursementConfigurationCollection, bson.M{"_id": idHex})
}

func (r *DisbursementConfigurationRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "SoftDeleteByFilter"))

	var model models.DisbursementConfiguration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.DisbursementConfigurationCollection, bsonM)
}

func (r *DisbursementConfigurationRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.DisbursementConfiguration, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "GetByFilter"))

	var model models.DisbursementConfiguration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementConfiguration{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.DisbursementConfigurationCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.DisbursementConfiguration{}, nil
		}

		return internal.DisbursementConfiguration{}, err
	}

	return mapDisbursementConfigurationToInternalData(model), nil
}

func (r *DisbursementConfigurationRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "SumByFilter"))

	var model models.DisbursementConfiguration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.DisbursementConfigurationCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *DisbursementConfigurationRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "CountByFilter"))

	var model models.DisbursementConfiguration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.DisbursementConfigurationCollection, bsonM)
}

func (r *DisbursementConfigurationRepository) GetByID(ctx context.Context, id string) (internal.DisbursementConfiguration, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "GetByID"))

	var model models.DisbursementConfiguration
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementConfigurationRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.DisbursementConfigurationCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.DisbursementConfiguration{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.DisbursementConfiguration{}, err
	}
	return mapDisbursementConfigurationToInternalData(model), nil
}

func (r *DisbursementConfigurationRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.DisbursementConfiguration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "GetAll"))
	var model models.DisbursementConfiguration
	var listModels []models.DisbursementConfiguration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.DisbursementConfigurationCollection, bsonM, &listModels)
	if err != nil {
		return []internal.DisbursementConfiguration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementConfigurationRepository.FindAll")
	}

	var internalModels []internal.DisbursementConfiguration

	for _, externalModel := range listModels {
		internalModel := mapDisbursementConfigurationToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *DisbursementConfigurationRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.DisbursementConfiguration, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "GetAll"))
	var model models.DisbursementConfiguration
	var listModels []models.DisbursementConfiguration

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementConfiguration{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.DisbursementConfigurationCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.DisbursementConfiguration{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementConfigurationRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.DisbursementConfiguration

	for _, externalModel := range listModels {
		internalModel := mapDisbursementConfigurationToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *DisbursementConfigurationRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.DisbursementConfigurationPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementConfigurationRepository", "GetAllWithPagination"))
	var listModels []models.DisbursementConfiguration
	var model models.DisbursementConfiguration

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.DisbursementConfigurationCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.DisbursementConfigurationPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.DisbursementConfigurationPagin{}, err
	}

	var internalModels []internal.DisbursementConfiguration

	for _, externalModel := range listModels {
		internalModel := mapDisbursementConfigurationToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.DisbursementConfigurationPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapDisbursementConfigurationToInternalData(data models.DisbursementConfiguration) internal.DisbursementConfiguration {
	return internal.DisbursementConfiguration{
		ID:         data.ID.Hex(),
		CompanyID:  data.CompanyID.Hex(),
		ProfileID:  data.ProfileID.Hex(),
		MerchantID: data.MerchantID.Hex(),
		MerchantBankID: func() string {
			if data.MerchantBankID != nil {
				return data.MerchantBankID.Hex()
			}
			return ""
		}(),
		AutoDisburse: pkg.GetBoolValue(data.AutoDisburse, false),
		TotalAmount:  pkg.GetFloat64Value(data.TotalAmount, 0),
		Status:       constant.ReverseConstant(int(data.Status)),
		CreatedAt:    data.CreatedAt,
		UpdatedAt:    data.UpdatedAt,
	}
}

func mapDisbursementConfigurationFromParams(params internal.CreateDisbursementConfiguration, action string) (models.DisbursementConfiguration, error) {
	model := models.DisbursementConfiguration{
		AutoDisburse: pkg.SetBoolPointer(params.AutoDisburse, action),
		TotalAmount:  pkg.SetFloat64Pointer(params.TotalAmount, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.DisbursementConfiguration{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.DisbursementConfiguration{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.DisbursementConfiguration{}, err
		}
		model.MerchantID = objectID
	}

	if params.MerchantBankID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.MerchantBankID)
		if err != nil {
			return models.DisbursementConfiguration{}, err
		}
		model.MerchantBankID = &objectID
	}

	return model, nil
}
