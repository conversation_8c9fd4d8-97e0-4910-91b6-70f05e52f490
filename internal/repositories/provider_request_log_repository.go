package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ProviderRequestLogRepository struct {
	util riot.Util
}

func NewProviderRequestLogRepository(util riot.Util) *ProviderRequestLogRepository {
	return &ProviderRequestLogRepository{
		util: util,
	}
}

func (r *ProviderRequestLogRepository) Create(ctx context.Context, params internal.CreateProviderRequestLog) (internal.ProviderRequestLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapProviderRequestLogFromParams(params, "create")

	if err != nil {
		return internal.ProviderRequestLog{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.ProviderRequestLogCollection, &model)
	if err != nil {
		return internal.ProviderRequestLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRequestLogRepository.Create")
	}
	return mapProviderRequestLogToInternalData(model), nil
}

func (r *ProviderRequestLogRepository) CreateBulk(ctx context.Context, params []internal.CreateProviderRequestLog) ([]internal.ProviderRequestLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapProviderRequestLogFromParams(param, "create")

		if err != nil {
			return []internal.ProviderRequestLog{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.ProviderRequestLog{}).CreateBulk(ctx, r.util.DB, models.ProviderRequestLogCollection, listModels)

	if err != nil {
		return []internal.ProviderRequestLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRequestLogRepository.CreateBulk")
	}

	return []internal.ProviderRequestLog{}, nil
}

func (r *ProviderRequestLogRepository) Update(ctx context.Context, id string, params internal.CreateProviderRequestLog) (internal.ProviderRequestLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository.Repo", "Update"))

	model, err := mapProviderRequestLogFromParams(params, "update")

	if err != nil {
		return internal.ProviderRequestLog{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.ProviderRequestLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.ProviderRequestLogCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *ProviderRequestLogRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateProviderRequestLog) (internal.ProviderRequestLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderRequestLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderRequestLog{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapProviderRequestLogFromParams(params, "update")

	if err != nil {
		return internal.ProviderRequestLog{}, err
	}

	model.Update(ctx, r.util.DB, models.ProviderRequestLogCollection, bsonM, &model)

	return mapProviderRequestLogToInternalData(model), nil
}

func (r *ProviderRequestLogRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "SoftDelete"))
	var model models.ProviderRequestLog
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderRequestLogRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.ProviderRequestLogCollection, bson.M{"_id": idHex})
}

func (r *ProviderRequestLogRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "SoftDeleteByFilter"))

	var model models.ProviderRequestLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.ProviderRequestLogCollection, bsonM)
}

func (r *ProviderRequestLogRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.ProviderRequestLog, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "GetByFilter"))

	var model models.ProviderRequestLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderRequestLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderRequestLog{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.ProviderRequestLogCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.ProviderRequestLog{}, nil
		}

		return internal.ProviderRequestLog{}, err
	}

	return mapProviderRequestLogToInternalData(model), nil
}

func (r *ProviderRequestLogRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "SumByFilter"))

	var model models.ProviderRequestLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.ProviderRequestLogCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *ProviderRequestLogRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "CountByFilter"))

	var model models.ProviderRequestLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.ProviderRequestLogCollection, bsonM)
}

func (r *ProviderRequestLogRepository) GetByID(ctx context.Context, id string) (internal.ProviderRequestLog, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "GetByID"))

	var model models.ProviderRequestLog
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderRequestLogRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.ProviderRequestLogCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ProviderRequestLog{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ProviderRequestLog{}, err
	}
	return mapProviderRequestLogToInternalData(model), nil
}

func (r *ProviderRequestLogRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.ProviderRequestLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "GetAll"))
	var model models.ProviderRequestLog
	var listModels []models.ProviderRequestLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderRequestLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderRequestLog{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.ProviderRequestLogCollection, bsonM, &listModels)
	if err != nil {
		return []internal.ProviderRequestLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRequestLogRepository.FindAll")
	}

	var internalModels []internal.ProviderRequestLog

	for _, externalModel := range listModels {
		internalModel := mapProviderRequestLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ProviderRequestLogRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.ProviderRequestLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "GetAll"))
	var model models.ProviderRequestLog
	var listModels []models.ProviderRequestLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderRequestLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderRequestLog{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.ProviderRequestLogCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.ProviderRequestLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRequestLogRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.ProviderRequestLog

	for _, externalModel := range listModels {
		internalModel := mapProviderRequestLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ProviderRequestLogRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ProviderRequestLogPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogRepository", "GetAllWithPagination"))
	var listModels []models.ProviderRequestLog
	var model models.ProviderRequestLog

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.ProviderRequestLogCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ProviderRequestLogPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ProviderRequestLogPagin{}, err
	}

	var internalModels []internal.ProviderRequestLog

	for _, externalModel := range listModels {
		internalModel := mapProviderRequestLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.ProviderRequestLogPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapProviderRequestLogToInternalData(data models.ProviderRequestLog) internal.ProviderRequestLog {
	return internal.ProviderRequestLog{
		ID:        data.ID.Hex(),
		CompanyID: data.CompanyID.Hex(),
		Provider:  data.Provider,
		URL:       pkg.GetStringValue(data.URL, ""),
		TransactionID: func() string {
			if data.TransactionID != nil {
				return data.TransactionID.Hex()
			}
			return ""
		}(),
		ExternalID:   pkg.GetStringValue(data.ExternalID, ""),
		SubAccountID: pkg.GetStringValue(data.SubAccountID, ""),
		RequestBody:  pkg.GetStringValue(data.RequestBody, ""),
		ResponseBody: pkg.GetStringValue(data.ResponseBody, ""),
		StatusCode:   pkg.GetIntValue(data.StatusCode, 0),
		Status:       constant.ReverseConstant(int(data.Status)),
		CreatedAt:    data.CreatedAt,
		UpdatedAt:    data.UpdatedAt,
	}
}

func mapProviderRequestLogFromParams(params internal.CreateProviderRequestLog, action string) (models.ProviderRequestLog, error) {
	model := models.ProviderRequestLog{
		Provider:     params.Provider,
		URL:          pkg.SetStringLowerPointer(params.URL, action),
		ExternalID:   pkg.SetStringPointer(params.ExternalID, action),
		SubAccountID: pkg.SetStringPointer(params.SubAccountID, action),
		RequestBody:  pkg.SetStringPointer(params.RequestBody, action),
		ResponseBody: pkg.SetStringPointer(params.ResponseBody, action),
		StatusCode:   pkg.SetIntPointer(params.StatusCode, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.ProviderRequestLog{}, err
		}
		model.CompanyID = objectID
	}

	if params.TransactionID != nil && *params.TransactionID != "" {
		objectID, err := primitive.ObjectIDFromHex(*params.TransactionID)
		if err != nil {
			return models.ProviderRequestLog{}, err
		}
		model.TransactionID = &objectID
	}

	return model, nil
}
