package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type FeeRepository struct {
	util riot.Util
}

func NewFeeRepository(util riot.Util) *FeeRepository {
	return &FeeRepository{
		util: util,
	}
}

func (r *FeeRepository) Create(ctx context.Context, params internal.CreateFee) (internal.Fee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapFeeFromParams(params, "create")

	if err != nil {
		return internal.Fee{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.FeeCollection, &model)
	if err != nil {
		return internal.Fee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "FeeRepository.Create")
	}
	return mapFeeToInternalData(model), nil
}

func (r *FeeRepository) CreateBulk(ctx context.Context, params []internal.CreateFee) ([]internal.Fee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapFeeFromParams(param, "create")

		if err != nil {
			return []internal.Fee{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Fee{}).CreateBulk(ctx, r.util.DB, models.FeeCollection, listModels)

	if err != nil {
		return []internal.Fee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "FeeRepository.CreateBulk")
	}

	return []internal.Fee{}, nil
}

func (r *FeeRepository) Update(ctx context.Context, id string, params internal.CreateFee) (internal.Fee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository.Repo", "Update"))

	model, err := mapFeeFromParams(params, "update")

	if err != nil {
		return internal.Fee{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Fee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.FeeCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *FeeRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateFee) (internal.Fee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["provider_id"] = objectID
		case "object_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["object_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapFeeFromParams(params, "update")

	if err != nil {
		return internal.Fee{}, err
	}

	model.Update(ctx, r.util.DB, models.FeeCollection, bsonM, &model)

	return mapFeeToInternalData(model), nil
}

func (r *FeeRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "SoftDelete"))
	var model models.Fee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "FeeRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.FeeCollection, bson.M{"_id": idHex})
}

func (r *FeeRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "SoftDeleteByFilter"))

	var model models.Fee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["provider_id"] = objectID
		case "object_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["object_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.FeeCollection, bsonM)
}

func (r *FeeRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Fee, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "GetByFilter"))

	var model models.Fee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["provider_id"] = objectID
		case "object_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Fee{}, err
			}
			bsonM["object_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.FeeCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Fee{}, nil
		}

		return internal.Fee{}, err
	}

	return mapFeeToInternalData(model), nil
}

func (r *FeeRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "SumByFilter"))

	var model models.Fee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["provider_id"] = objectID
		case "object_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["object_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.FeeCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *FeeRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "CountByFilter"))

	var model models.Fee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["provider_id"] = objectID
		case "object_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["object_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.FeeCollection, bsonM)
}

func (r *FeeRepository) GetByID(ctx context.Context, id string) (internal.Fee, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "GetByID"))

	var model models.Fee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "FeeRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.FeeCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Fee{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Fee{}, err
	}
	return mapFeeToInternalData(model), nil
}

func (r *FeeRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Fee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "GetAll"))
	var model models.Fee
	var listModels []models.Fee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["provider_id"] = objectID
		case "object_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["object_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.FeeCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Fee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "FeeRepository.FindAll")
	}

	var internalModels []internal.Fee

	for _, externalModel := range listModels {
		internalModel := mapFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *FeeRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Fee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "GetAll"))
	var model models.Fee
	var listModels []models.Fee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["provider_id"] = objectID
		case "object_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Fee{}, err
			}
			bsonM["object_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.FeeCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Fee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "FeeRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Fee

	for _, externalModel := range listModels {
		internalModel := mapFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *FeeRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.FeePagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeeRepository", "GetAllWithPagination"))
	var listModels []models.Fee
	var model models.Fee

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.FeeCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.FeePagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.FeePagin{}, err
	}

	var internalModels []internal.Fee

	for _, externalModel := range listModels {
		internalModel := mapFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.FeePagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapFeeToInternalData(data models.Fee) internal.Fee {
	return internal.Fee{
		ID:        data.ID.Hex(),
		CompanyID: data.CompanyID.Hex(),
		ProfileID: func() string {
			if data.ProfileID != nil {
				return data.ProfileID.Hex()
			}
			return ""
		}(),
		ObjectType: pkg.GetStringValue(data.ObjectType, ""),
		ObjectID: func() string {
			if data.ObjectID != nil {
				return data.ObjectID.Hex()
			}
			return ""
		}(),
		PaymentMethod:     pkg.GetStringValue(data.PaymentMethod, ""),
		FeeType:           pkg.GetStringValue(data.FeeType, ""),
		FeeValue:          pkg.GetFloat64Value(data.FeeValue, 0),
		PGFeeType:         pkg.GetStringValue(data.PGFeeType, ""),
		PGFeeValue:        pkg.GetFloat64Value(data.PGFeeValue, 0),
		ProviderFeeType:   pkg.GetStringValue(data.ProviderFeeType, ""),
		ProviderFeeValue:  pkg.GetFloat64Value(data.ProviderFeeValue, 0),
		PlatformFeeType:   pkg.GetStringValue(data.PlatformFeeType, ""),
		PlatformFeeValue:  pkg.GetFloat64Value(data.PlatformFeeValue, 0),
		ReferralFeeType:   pkg.GetStringValue(data.ReferralFeeType, ""),
		ReferralFeeValue:  pkg.GetFloat64Value(data.ReferralFeeValue, 0),
		MarkeringFeeType:  pkg.GetStringValue(data.MarkeringFeeType, ""),
		MarkeringFeeValue: pkg.GetFloat64Value(data.MarkeringFeeValue, 0),
		IsDefault:         pkg.GetBoolValue(data.IsDefault, false),
		Currency:          pkg.GetStringValue(data.Currency, ""),
		Status:            constant.ReverseConstant(int(data.Status)),
		CreatedAt:         data.CreatedAt,
		UpdatedAt:         data.UpdatedAt,
	}
}

func mapFeeFromParams(params internal.CreateFee, action string) (models.Fee, error) {
	model := models.Fee{
		ObjectType:        pkg.SetStringPointer(params.ObjectType, action),
		PaymentMethod:     pkg.SetStringUpperPointer(params.PaymentMethod, action),
		FeeType:           pkg.SetStringPointer(params.FeeType, action),
		FeeValue:          pkg.SetFloat64Pointer(params.FeeValue, action),
		PGFeeType:         pkg.SetStringPointer(params.PGFeeType, action),
		PGFeeValue:        pkg.SetFloat64Pointer(params.PGFeeValue, action),
		ProviderFeeType:   pkg.SetStringPointer(params.ProviderFeeType, action),
		ProviderFeeValue:  pkg.SetFloat64Pointer(params.ProviderFeeValue, action),
		PlatformFeeType:   pkg.SetStringPointer(params.PlatformFeeType, action),
		PlatformFeeValue:  pkg.SetFloat64Pointer(params.PlatformFeeValue, action),
		ReferralFeeType:   pkg.SetStringPointer(params.ReferralFeeType, action),
		ReferralFeeValue:  pkg.SetFloat64Pointer(params.ReferralFeeValue, action),
		MarkeringFeeType:  pkg.SetStringPointer(params.MarkeringFeeType, action),
		MarkeringFeeValue: pkg.SetFloat64Pointer(params.MarkeringFeeValue, action),
		IsDefault:         pkg.SetBoolPointer(params.IsDefault, action),
		Currency:          pkg.SetStringUpperPointer(params.Currency, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Fee{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.Fee{}, err
		}
		model.ProfileID = &objectID
	}

	if params.ObjectID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ObjectID)
		if err != nil {
			return models.Fee{}, err
		}
		model.ObjectID = &objectID
	}
	return model, nil
}
