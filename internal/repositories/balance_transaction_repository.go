package repositories

import (
	"context"
	"time"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type BalanceTransactionRepository struct {
	util riot.Util
}

func NewBalanceTransactionRepository(util riot.Util) *BalanceTransactionRepository {
	return &BalanceTransactionRepository{
		util: util,
	}
}

func (r *BalanceTransactionRepository) Create(ctx context.Context, params internal.CreateBalanceTransaction) (internal.BalanceTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapBalanceTxnFromParams(params, "create")

	if err != nil {
		return internal.BalanceTransaction{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.BalanceTransactionCollection, &model)
	if err != nil {
		return internal.BalanceTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionRepository.Create")
	}
	return mapBalanceTxnToInternalData(model), nil
}

func (r *BalanceTransactionRepository) CreateBulk(ctx context.Context, params []internal.CreateBalanceTransaction) ([]internal.BalanceTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapBalanceTxnFromParams(param, "create")

		if err != nil {
			return []internal.BalanceTransaction{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.BalanceTransaction{}).CreateBulk(ctx, r.util.DB, models.BalanceTransactionCollection, listModels)

	if err != nil {
		return []internal.BalanceTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionRepository.CreateBulk")
	}

	return []internal.BalanceTransaction{}, nil
}

func (r *BalanceTransactionRepository) Update(ctx context.Context, id string, params internal.CreateBalanceTransaction) (internal.BalanceTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository.Repo", "Update"))

	model, err := mapBalanceTxnFromParams(params, "update")

	if err != nil {
		return internal.BalanceTransaction{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.BalanceTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.BalanceTransactionCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *BalanceTransactionRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateBalanceTransaction) (internal.BalanceTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["coin_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["source_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["type_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapBalanceTxnFromParams(params, "update")

	if err != nil {
		return internal.BalanceTransaction{}, err
	}

	model.Update(ctx, r.util.DB, models.BalanceTransactionCollection, bsonM, &model)

	return mapBalanceTxnToInternalData(model), nil
}

func (r *BalanceTransactionRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "SoftDelete"))
	var model models.BalanceTransaction
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "BalanceTransactionRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.BalanceTransactionCollection, bson.M{"_id": idHex})
}

func (r *BalanceTransactionRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "SoftDeleteByFilter"))

	var model models.BalanceTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["coin_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["source_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["type_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.BalanceTransactionCollection, bsonM)
}

func (r *BalanceTransactionRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.BalanceTransaction, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "GetByFilter"))

	var model models.BalanceTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["coin_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["source_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["type_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.BalanceTransaction{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.BalanceTransactionCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.BalanceTransaction{}, nil
		}

		return internal.BalanceTransaction{}, err
	}

	return mapBalanceTxnToInternalData(model), nil
}

func (r *BalanceTransactionRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "SumByFilter"))

	var model models.BalanceTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["type_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["source_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["coin_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.BalanceTransactionCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *BalanceTransactionRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "CountByFilter"))

	var model models.BalanceTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["type_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["source_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["coin_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.BalanceTransactionCollection, bsonM)
}

func (r *BalanceTransactionRepository) GetByID(ctx context.Context, id string) (internal.BalanceTransaction, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "GetByID"))

	var model models.BalanceTransaction
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "BalanceTransactionRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.BalanceTransactionCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.BalanceTransaction{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.BalanceTransaction{}, err
	}
	return mapBalanceTxnToInternalData(model), nil
}

func (r *BalanceTransactionRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.BalanceTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "GetAll"))
	var model models.BalanceTransaction
	var listModels []models.BalanceTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["type_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["source_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["coin_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.BalanceTransactionCollection, bsonM, &listModels)
	if err != nil {
		return []internal.BalanceTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionRepository.FindAll")
	}

	var internalModels []internal.BalanceTransaction

	for _, externalModel := range listModels {
		internalModel := mapBalanceTxnToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *BalanceTransactionRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.BalanceTransaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "GetAll"))
	var model models.BalanceTransaction
	var listModels []models.BalanceTransaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "type_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["type_id"] = objectID
		case "source_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["source_id"] = objectID
		case "coin_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["coin_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.BalanceTransaction{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.BalanceTransactionCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.BalanceTransaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.BalanceTransaction

	for _, externalModel := range listModels {
		internalModel := mapBalanceTxnToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *BalanceTransactionRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.BalanceTransactionPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "GetAllWithPagination"))
	var listModels []models.BalanceTransaction
	var model models.BalanceTransaction

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.BalanceTransactionCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.BalanceTransactionPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.BalanceTransactionPagin{}, err
	}

	var internalModels []internal.BalanceTransaction

	for _, externalModel := range listModels {
		internalModel := mapBalanceTxnToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.BalanceTransactionPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapBalanceTxnToInternalData(data models.BalanceTransaction) internal.BalanceTransaction {
	return internal.BalanceTransaction{
		ID:         data.ID.Hex(),
		CompanyID:  data.CompanyID.Hex(),
		ProfileID:  data.ProfileID.Hex(),
		MerchantID: data.MerchantID.Hex(),
		TypeID: func() string {
			if data.TypeID != nil {
				return data.TypeID.Hex()
			}
			return ""
		}(),
		SourceID: func() string {
			if data.SourceID != nil {
				return data.SourceID.Hex()
			}
			return ""
		}(),
		Type:          pkg.GetIntValue(data.Type, 0),
		TypeObject:    pkg.GetStringValue(data.TypeObject, ""),
		Balance:       pkg.GetFloat64Value(data.Balance, 0),
		BalanceBefore: pkg.GetFloat64Value(data.BalanceBefore, 0),
		Description:   pkg.GetStringValue(data.Description, ""),
		Metadata:      pkg.GetMapInterface(data.Metadata, map[string]interface{}{}),
		ProductType:   pkg.GetStringValue(data.ProductType, ""),
		Status:        constant.ReverseConstant(int(data.Status)),
		CreatedAt:     data.CreatedAt,
		UpdatedAt:     data.UpdatedAt,
	}
}

func mapBalanceTxnFromParams(params internal.CreateBalanceTransaction, action string) (models.BalanceTransaction, error) {
	model := models.BalanceTransaction{
		Type:          pkg.SetIntPointer(params.Type, action),
		TypeObject:    pkg.SetStringPointer(params.TypeObject, action),
		Balance:       pkg.SetFloat64Pointer(params.Balance, action),
		BalanceBefore: pkg.SetFloat64Pointer(params.BalanceBefore, action),
		Description:   pkg.SetStringPointer(params.Description, action),
		Metadata:      pkg.SetMapInterfacePointer(params.Metadata, action),
		ProductType:   pkg.SetStringPointer(params.ProductType, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.BalanceTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapBalanceTxnFromParams.params.CompanyID")
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.BalanceTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapBalanceTxnFromParams.params.ProfileID")
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.BalanceTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapBalanceTxnFromParams.params.MerchantID")
		}
		model.MerchantID = objectID
	}

	if params.TypeID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.TypeID)
		if err != nil {
			return models.BalanceTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapBalanceTxnFromParams.params.TypeID")
		}
		model.TypeID = &objectID
	}

	if params.SourceID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.SourceID)
		if err != nil {
			return models.BalanceTransaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapBalanceTxnFromParams.params.SourceID")
		}
		model.SourceID = &objectID
	}

	return model, nil
}

// GetStatistics retrieves aggregated statistics for balance transactions
func (r *BalanceTransactionRepository) GetStatistics(ctx context.Context, filter internal.BalanceTransactionStatsFilter) (internal.BalanceTransactionStatistics, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "GetStatistics"))

	// Build match stage for aggregation pipeline
	matchStage := r.buildMatchStage(filter)

	// Build aggregation pipeline
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
	}

	// Add group stage for basic statistics
	groupStage := bson.D{
		{Key: "$group", Value: bson.D{
			{Key: "_id", Value: nil},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}},
	}
	pipeline = append(pipeline, groupStage)

	// Execute aggregation
	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return internal.BalanceTransactionStatistics{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "GetStatistics.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return internal.BalanceTransactionStatistics{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "GetStatistics.CursorAll")
	}

	stats := internal.BalanceTransactionStatistics{
		Period:    filter.Period,
		StartDate: *filter.StartDate,
		EndDate:   *filter.EndDate,
	}

	if len(results) > 0 {
		result := results[0]
		if totalAmount, ok := result["totalAmount"].(float64); ok {
			stats.TotalAmount = totalAmount
		}
		if totalCount, ok := result["totalCount"].(int32); ok {
			stats.TotalCount = int64(totalCount)
		}
	}

	// Get breakdown by type if requested
	if filter.IncludeTypes {
		typeStats, err := r.getStatsByType(ctx, matchStage)
		if err != nil {
			return internal.BalanceTransactionStatistics{}, err
		}
		stats.ByType = typeStats
	}

	// Get breakdown by merchant if requested
	if filter.IncludeMerchants {
		merchantStats, err := r.getStatsByMerchant(ctx, matchStage)
		if err != nil {
			return internal.BalanceTransactionStatistics{}, err
		}
		stats.ByMerchant = merchantStats
	}

	// Get daily breakdown if requested
	if filter.IncludeDaily {
		dailyStats, err := r.getDailyStats(ctx, matchStage)
		if err != nil {
			return internal.BalanceTransactionStatistics{}, err
		}
		stats.DailyBreakdown = dailyStats
	}

	// Get monthly breakdown if requested
	if filter.IncludeMonthly {
		monthlyStats, err := r.getMonthlyStats(ctx, matchStage)
		if err != nil {
			return internal.BalanceTransactionStatistics{}, err
		}
		stats.MonthlyBreakdown = monthlyStats
	}

	return stats, nil
}

// buildMatchStage builds the match stage for aggregation pipeline based on filter
func (r *BalanceTransactionRepository) buildMatchStage(filter internal.BalanceTransactionStatsFilter) bson.M {
	matchStage := bson.M{}

	// Add date range filter
	if filter.StartDate != nil || filter.EndDate != nil {
		dateFilter := bson.M{}
		if filter.StartDate != nil {
			dateFilter["$gte"] = *filter.StartDate
		}
		if filter.EndDate != nil {
			dateFilter["$lte"] = *filter.EndDate
		}
		matchStage["created_at"] = dateFilter
	}

	// Add company filter
	if filter.CompanyID != nil {
		if objectID, err := primitive.ObjectIDFromHex(*filter.CompanyID); err == nil {
			matchStage["company_id"] = objectID
		}
	}

	// Add merchant filter
	if filter.MerchantID != nil {
		if objectID, err := primitive.ObjectIDFromHex(*filter.MerchantID); err == nil {
			matchStage["merchant_id"] = objectID
		}
	}

	// Add profile filter
	if filter.ProfileID != nil {
		if objectID, err := primitive.ObjectIDFromHex(*filter.ProfileID); err == nil {
			matchStage["profile_id"] = objectID
		}
	}

	// Add transaction types filter
	if len(filter.Types) > 0 {
		matchStage["type"] = bson.M{"$in": filter.Types}
	}

	// Add status filter (only active transactions)
	matchStage["status"] = constant.TypeActive

	return matchStage
}

// getStatsByType gets statistics grouped by transaction type
func (r *BalanceTransactionRepository) getStatsByType(ctx context.Context, matchStage bson.M) ([]internal.BalanceTransactionTypeStats, error) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: "$type"},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{Key: "totalAmount", Value: -1}}}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getStatsByType.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getStatsByType.CursorAll")
	}

	// Calculate total for percentage calculation
	var grandTotal float64
	for _, result := range results {
		if amount, ok := result["totalAmount"].(float64); ok {
			grandTotal += amount
		}
	}

	var typeStats []internal.BalanceTransactionTypeStats
	for _, result := range results {
		stat := internal.BalanceTransactionTypeStats{}

		if typeVal, ok := result["_id"].(int32); ok {
			stat.Type = int(typeVal)
			stat.TypeName = constant.ReverseTxnConstant(int(typeVal))
		}
		if amount, ok := result["totalAmount"].(float64); ok {
			stat.TotalAmount = amount
			if grandTotal > 0 {
				stat.Percentage = (amount / grandTotal) * 100
			}
		}
		if count, ok := result["totalCount"].(int32); ok {
			stat.TotalCount = int64(count)
		}

		typeStats = append(typeStats, stat)
	}

	return typeStats, nil
}

// getStatsByMerchant gets statistics grouped by merchant
func (r *BalanceTransactionRepository) getStatsByMerchant(ctx context.Context, matchStage bson.M) ([]internal.BalanceTransactionMerchantStats, error) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: "$merchant_id"},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{Key: "totalAmount", Value: -1}}}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getStatsByMerchant.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getStatsByMerchant.CursorAll")
	}

	// Calculate total for percentage calculation
	var grandTotal float64
	for _, result := range results {
		if amount, ok := result["totalAmount"].(float64); ok {
			grandTotal += amount
		}
	}

	var merchantStats []internal.BalanceTransactionMerchantStats
	for _, result := range results {
		stat := internal.BalanceTransactionMerchantStats{}

		if merchantID, ok := result["_id"].(primitive.ObjectID); ok {
			stat.MerchantID = merchantID.Hex()
		}
		if amount, ok := result["totalAmount"].(float64); ok {
			stat.TotalAmount = amount
			if grandTotal > 0 {
				stat.Percentage = (amount / grandTotal) * 100
			}
		}
		if count, ok := result["totalCount"].(int32); ok {
			stat.TotalCount = int64(count)
		}

		merchantStats = append(merchantStats, stat)
	}

	return merchantStats, nil
}

// getDailyStats gets daily statistics
func (r *BalanceTransactionRepository) getDailyStats(ctx context.Context, matchStage bson.M) ([]internal.BalanceTransactionDailyStats, error) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "year", Value: bson.D{{Key: "$year", Value: "$created_at"}}},
				{Key: "month", Value: bson.D{{Key: "$month", Value: "$created_at"}}},
				{Key: "day", Value: bson.D{{Key: "$dayOfMonth", Value: "$created_at"}}},
			}},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{Key: "_id", Value: 1}}}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getDailyStats.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getDailyStats.CursorAll")
	}

	var dailyStats []internal.BalanceTransactionDailyStats
	for _, result := range results {
		stat := internal.BalanceTransactionDailyStats{}

		if dateInfo, ok := result["_id"].(bson.M); ok {
			if year, ok := dateInfo["year"].(int32); ok {
				if month, ok := dateInfo["month"].(int32); ok {
					if day, ok := dateInfo["day"].(int32); ok {
						stat.Date = time.Date(int(year), time.Month(month), int(day), 0, 0, 0, 0, time.UTC)
					}
				}
			}
		}
		if amount, ok := result["totalAmount"].(float64); ok {
			stat.TotalAmount = amount
		}
		if count, ok := result["totalCount"].(int32); ok {
			stat.TotalCount = int64(count)
		}

		dailyStats = append(dailyStats, stat)
	}

	return dailyStats, nil
}

// getMonthlyStats gets monthly statistics
func (r *BalanceTransactionRepository) getMonthlyStats(ctx context.Context, matchStage bson.M) ([]internal.BalanceTransactionMonthlyStats, error) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "year", Value: bson.D{{Key: "$year", Value: "$created_at"}}},
				{Key: "month", Value: bson.D{{Key: "$month", Value: "$created_at"}}},
			}},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{Key: "_id", Value: 1}}}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getMonthlyStats.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getMonthlyStats.CursorAll")
	}

	var monthlyStats []internal.BalanceTransactionMonthlyStats
	for _, result := range results {
		stat := internal.BalanceTransactionMonthlyStats{}

		if dateInfo, ok := result["_id"].(bson.M); ok {
			if year, ok := dateInfo["year"].(int32); ok {
				stat.Year = int(year)
			}
			if month, ok := dateInfo["month"].(int32); ok {
				stat.Month = int(month)
			}
		}
		if amount, ok := result["totalAmount"].(float64); ok {
			stat.TotalAmount = amount
		}
		if count, ok := result["totalCount"].(int32); ok {
			stat.TotalCount = int64(count)
		}

		monthlyStats = append(monthlyStats, stat)
	}

	return monthlyStats, nil
}

// GetAllTimeStatistics retrieves all-time statistics for balance transactions
func (r *BalanceTransactionRepository) GetAllTimeStatistics(ctx context.Context, filter internal.BalanceTransactionAllTimeFilter) (internal.BalanceTransactionAllTimeStats, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionRepository", "GetAllTimeStatistics"))

	// Build match stage for all-time filter
	matchStage := r.buildAllTimeMatchStage(filter)

	// Get basic all-time statistics
	basicStats, err := r.getAllTimeBasicStats(ctx, matchStage)
	if err != nil {
		return internal.BalanceTransactionAllTimeStats{}, err
	}

	result := internal.BalanceTransactionAllTimeStats{
		TotalIncome:              basicStats.TotalIncome,
		TotalExpenses:            basicStats.TotalExpenses,
		NetAmount:                basicStats.NetAmount,
		TotalTransactions:        basicStats.TotalTransactions,
		AverageTransactionAmount: basicStats.AverageTransactionAmount,
		FirstTransactionDate:     basicStats.FirstTransactionDate,
		LastTransactionDate:      basicStats.LastTransactionDate,
	}

	// Get top transaction types if requested
	if filter.IncludeTypes {
		topLimit := filter.TopLimit
		if topLimit == 0 {
			topLimit = 10 // Default limit
		}
		typeStats, err := r.getTopTransactionTypes(ctx, matchStage, topLimit)
		if err != nil {
			return internal.BalanceTransactionAllTimeStats{}, err
		}
		result.TopTransactionTypes = typeStats
	}

	// Get top merchants if requested
	if filter.IncludeMerchants {
		topLimit := filter.TopLimit
		if topLimit == 0 {
			topLimit = 10 // Default limit
		}
		merchantStats, err := r.getTopMerchants(ctx, matchStage, topLimit)
		if err != nil {
			return internal.BalanceTransactionAllTimeStats{}, err
		}
		result.TopMerchants = merchantStats
	}

	// Get trends if requested
	if filter.IncludeTrends {
		monthlyTrends, err := r.getMonthlyTrends(ctx, matchStage)
		if err != nil {
			return internal.BalanceTransactionAllTimeStats{}, err
		}
		result.MonthlyTrends = monthlyTrends

		yearlyTrends, err := r.getYearlyTrends(ctx, matchStage)
		if err != nil {
			return internal.BalanceTransactionAllTimeStats{}, err
		}
		result.YearlyTrends = yearlyTrends
	}

	return result, nil
}

// buildAllTimeMatchStage builds the match stage for all-time statistics
func (r *BalanceTransactionRepository) buildAllTimeMatchStage(filter internal.BalanceTransactionAllTimeFilter) bson.M {
	matchStage := bson.M{}

	// Add company filter
	if filter.CompanyID != nil {
		if objectID, err := primitive.ObjectIDFromHex(*filter.CompanyID); err == nil {
			matchStage["company_id"] = objectID
		}
	}

	// Add merchant filter
	if filter.MerchantID != nil {
		if objectID, err := primitive.ObjectIDFromHex(*filter.MerchantID); err == nil {
			matchStage["merchant_id"] = objectID
		}
	}

	// Add profile filter
	if filter.ProfileID != nil {
		if objectID, err := primitive.ObjectIDFromHex(*filter.ProfileID); err == nil {
			matchStage["profile_id"] = objectID
		}
	}

	// Add transaction types filter
	if len(filter.Types) > 0 {
		matchStage["type"] = bson.M{"$in": filter.Types}
	}

	// Add status filter (only active transactions)
	matchStage["status"] = constant.TypeActive

	return matchStage
}

// getAllTimeBasicStats gets basic all-time statistics
func (r *BalanceTransactionRepository) getAllTimeBasicStats(ctx context.Context, matchStage bson.M) (struct {
	TotalIncome              float64
	TotalExpenses            float64
	NetAmount                float64
	TotalTransactions        int64
	AverageTransactionAmount float64
	FirstTransactionDate     *time.Time
	LastTransactionDate      *time.Time
}, error) {

	result := struct {
		TotalIncome              float64
		TotalExpenses            float64
		NetAmount                float64
		TotalTransactions        int64
		AverageTransactionAmount float64
		FirstTransactionDate     *time.Time
		LastTransactionDate      *time.Time
	}{}

	// Pipeline for basic statistics
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: nil},
			{Key: "totalIncome", Value: bson.D{{Key: "$sum", Value: bson.D{
				{Key: "$cond", Value: bson.A{
					bson.D{{Key: "$gt", Value: bson.A{"$balance", 0}}},
					"$balance",
					0,
				}},
			}}}},
			{Key: "totalExpenses", Value: bson.D{{Key: "$sum", Value: bson.D{
				{Key: "$cond", Value: bson.A{
					bson.D{{Key: "$lt", Value: bson.A{"$balance", 0}}},
					bson.D{{Key: "$abs", Value: "$balance"}},
					0,
				}},
			}}}},
			{Key: "netAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
			{Key: "avgAmount", Value: bson.D{{Key: "$avg", Value: "$balance"}}},
			{Key: "firstDate", Value: bson.D{{Key: "$min", Value: "$created_at"}}},
			{Key: "lastDate", Value: bson.D{{Key: "$max", Value: "$created_at"}}},
		}}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return result, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getAllTimeBasicStats.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return result, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getAllTimeBasicStats.CursorAll")
	}

	if len(results) > 0 {
		res := results[0]
		if totalIncome, ok := res["totalIncome"].(float64); ok {
			result.TotalIncome = totalIncome
		}
		if totalExpenses, ok := res["totalExpenses"].(float64); ok {
			result.TotalExpenses = totalExpenses
		}
		if netAmount, ok := res["netAmount"].(float64); ok {
			result.NetAmount = netAmount
		}
		if totalCount, ok := res["totalCount"].(int32); ok {
			result.TotalTransactions = int64(totalCount)
		}
		if avgAmount, ok := res["avgAmount"].(float64); ok {
			result.AverageTransactionAmount = avgAmount
		}
		if firstDate, ok := res["firstDate"].(primitive.DateTime); ok {
			t := firstDate.Time()
			result.FirstTransactionDate = &t
		}
		if lastDate, ok := res["lastDate"].(primitive.DateTime); ok {
			t := lastDate.Time()
			result.LastTransactionDate = &t
		}
	}

	return result, nil
}

// getTopTransactionTypes gets top transaction types by amount
func (r *BalanceTransactionRepository) getTopTransactionTypes(ctx context.Context, matchStage bson.M, limit int) ([]internal.BalanceTransactionTypeStats, error) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: "$type"},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{Key: "totalAmount", Value: -1}}}},
		bson.D{{Key: "$limit", Value: limit}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getTopTransactionTypes.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getTopTransactionTypes.CursorAll")
	}

	// Calculate total for percentage calculation
	var grandTotal float64
	for _, result := range results {
		if amount, ok := result["totalAmount"].(float64); ok {
			grandTotal += amount
		}
	}

	var typeStats []internal.BalanceTransactionTypeStats
	for _, result := range results {
		stat := internal.BalanceTransactionTypeStats{}

		if typeVal, ok := result["_id"].(int32); ok {
			stat.Type = int(typeVal)
			stat.TypeName = constant.ReverseTxnConstant(int(typeVal))
		}
		if amount, ok := result["totalAmount"].(float64); ok {
			stat.TotalAmount = amount
			if grandTotal > 0 {
				stat.Percentage = (amount / grandTotal) * 100
			}
		}
		if count, ok := result["totalCount"].(int32); ok {
			stat.TotalCount = int64(count)
		}

		typeStats = append(typeStats, stat)
	}

	return typeStats, nil
}

// getTopMerchants gets top merchants by amount
func (r *BalanceTransactionRepository) getTopMerchants(ctx context.Context, matchStage bson.M, limit int) ([]internal.BalanceTransactionMerchantStats, error) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: "$merchant_id"},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{Key: "totalAmount", Value: -1}}}},
		bson.D{{Key: "$limit", Value: limit}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getTopMerchants.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getTopMerchants.CursorAll")
	}

	// Calculate total for percentage calculation
	var grandTotal float64
	for _, result := range results {
		if amount, ok := result["totalAmount"].(float64); ok {
			grandTotal += amount
		}
	}

	var merchantStats []internal.BalanceTransactionMerchantStats
	for _, result := range results {
		stat := internal.BalanceTransactionMerchantStats{}

		if merchantID, ok := result["_id"].(primitive.ObjectID); ok {
			stat.MerchantID = merchantID.Hex()
		}
		if amount, ok := result["totalAmount"].(float64); ok {
			stat.TotalAmount = amount
			if grandTotal > 0 {
				stat.Percentage = (amount / grandTotal) * 100
			}
		}
		if count, ok := result["totalCount"].(int32); ok {
			stat.TotalCount = int64(count)
		}

		merchantStats = append(merchantStats, stat)
	}

	return merchantStats, nil
}

// getMonthlyTrends gets monthly trends with growth rates
func (r *BalanceTransactionRepository) getMonthlyTrends(ctx context.Context, matchStage bson.M) ([]internal.BalanceTransactionMonthlyTrend, error) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "year", Value: bson.D{{Key: "$year", Value: "$created_at"}}},
				{Key: "month", Value: bson.D{{Key: "$month", Value: "$created_at"}}},
			}},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{Key: "_id", Value: 1}}}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getMonthlyTrends.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getMonthlyTrends.CursorAll")
	}

	var monthlyTrends []internal.BalanceTransactionMonthlyTrend
	var prevAmount float64

	for i, result := range results {
		trend := internal.BalanceTransactionMonthlyTrend{}

		if dateInfo, ok := result["_id"].(bson.M); ok {
			if year, ok := dateInfo["year"].(int32); ok {
				trend.Year = int(year)
			}
			if month, ok := dateInfo["month"].(int32); ok {
				trend.Month = int(month)
			}
		}
		if amount, ok := result["totalAmount"].(float64); ok {
			trend.TotalAmount = amount

			// Calculate growth rate
			if i > 0 && prevAmount > 0 {
				trend.GrowthRate = ((amount - prevAmount) / prevAmount) * 100
			}
			prevAmount = amount
		}
		if count, ok := result["totalCount"].(int32); ok {
			trend.TotalCount = int64(count)
		}

		monthlyTrends = append(monthlyTrends, trend)
	}

	return monthlyTrends, nil
}

// getYearlyTrends gets yearly trends with growth rates
func (r *BalanceTransactionRepository) getYearlyTrends(ctx context.Context, matchStage bson.M) ([]internal.BalanceTransactionYearlyTrend, error) {
	pipeline := mongo.Pipeline{
		bson.D{{Key: "$match", Value: matchStage}},
		bson.D{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: bson.D{{Key: "year", Value: bson.D{{Key: "$year", Value: "$created_at"}}}}},
			{Key: "totalAmount", Value: bson.D{{Key: "$sum", Value: "$balance"}}},
			{Key: "totalCount", Value: bson.D{{Key: "$sum", Value: 1}}},
		}}},
		bson.D{{Key: "$sort", Value: bson.D{{Key: "_id", Value: 1}}}},
	}

	cursor, err := r.util.DB.Collection(models.BalanceTransactionCollection).Aggregate(ctx, pipeline)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getYearlyTrends.Aggregate")
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "getYearlyTrends.CursorAll")
	}

	var yearlyTrends []internal.BalanceTransactionYearlyTrend
	var prevAmount float64

	for i, result := range results {
		trend := internal.BalanceTransactionYearlyTrend{}

		if dateInfo, ok := result["_id"].(bson.M); ok {
			if year, ok := dateInfo["year"].(int32); ok {
				trend.Year = int(year)
			}
		}
		if amount, ok := result["totalAmount"].(float64); ok {
			trend.TotalAmount = amount

			// Calculate growth rate
			if i > 0 && prevAmount > 0 {
				trend.GrowthRate = ((amount - prevAmount) / prevAmount) * 100
			}
			prevAmount = amount
		}
		if count, ok := result["totalCount"].(int32); ok {
			trend.TotalCount = int64(count)
		}

		yearlyTrends = append(yearlyTrends, trend)
	}

	return yearlyTrends, nil
}
