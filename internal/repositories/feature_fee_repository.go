package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type FeatureFeeRepository struct {
	util riot.Util
}

func NewFeatureFeeRepository(util riot.Util) *FeatureFeeRepository {
	return &FeatureFeeRepository{
		util: util,
	}
}

func (r *FeatureFeeRepository) Create(ctx context.Context, params internal.CreateFeatureFee) (internal.FeatureFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapFeatureFeeFromParams(params, "create")

	if err != nil {
		return internal.FeatureFee{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.FeatureFeeCollection, &model)
	if err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeRepository.Create")
	}
	return mapFeatureFeeToInternalData(model), nil
}

func (r *FeatureFeeRepository) CreateBulk(ctx context.Context, params []internal.CreateFeatureFee) ([]internal.FeatureFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapFeatureFeeFromParams(param, "create")

		if err != nil {
			return []internal.FeatureFee{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.FeatureFee{}).CreateBulk(ctx, r.util.DB, models.FeatureFeeCollection, listModels)

	if err != nil {
		return []internal.FeatureFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeRepository.CreateBulk")
	}

	return []internal.FeatureFee{}, nil
}

func (r *FeatureFeeRepository) Update(ctx context.Context, id string, params internal.CreateFeatureFee) (internal.FeatureFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository.Repo", "Update"))

	model, err := mapFeatureFeeFromParams(params, "update")

	if err != nil {
		return internal.FeatureFee{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.FeatureFeeCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *FeatureFeeRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateFeatureFee) (internal.FeatureFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.FeatureFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.FeatureFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.FeatureFee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.FeatureFee{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapFeatureFeeFromParams(params, "update")

	if err != nil {
		return internal.FeatureFee{}, err
	}

	model.Update(ctx, r.util.DB, models.FeatureFeeCollection, bsonM, &model)

	return mapFeatureFeeToInternalData(model), nil
}

func (r *FeatureFeeRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "SoftDelete"))
	var model models.FeatureFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "FeatureFeeRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.FeatureFeeCollection, bson.M{"_id": idHex})
}

func (r *FeatureFeeRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "SoftDeleteByFilter"))

	var model models.FeatureFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.FeatureFeeCollection, bsonM)
}

func (r *FeatureFeeRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.FeatureFee, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "GetByFilter"))

	var model models.FeatureFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.FeatureFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.FeatureFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.FeatureFee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.FeatureFee{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.FeatureFeeCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.FeatureFee{}, nil
		}

		return internal.FeatureFee{}, err
	}

	return mapFeatureFeeToInternalData(model), nil
}

func (r *FeatureFeeRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "SumByFilter"))

	var model models.FeatureFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.FeatureFeeCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *FeatureFeeRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "CountByFilter"))

	var model models.FeatureFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.FeatureFeeCollection, bsonM)
}

func (r *FeatureFeeRepository) GetByID(ctx context.Context, id string) (internal.FeatureFee, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "GetByID"))

	var model models.FeatureFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "FeatureFeeRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.FeatureFeeCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.FeatureFee{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.FeatureFee{}, err
	}
	return mapFeatureFeeToInternalData(model), nil
}

func (r *FeatureFeeRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.FeatureFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "GetAll"))
	var model models.FeatureFee
	var listModels []models.FeatureFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.FeatureFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.FeatureFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.FeatureFee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.FeatureFee{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.FeatureFeeCollection, bsonM, &listModels)
	if err != nil {
		return []internal.FeatureFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeRepository.FindAll")
	}

	var internalModels []internal.FeatureFee

	for _, externalModel := range listModels {
		internalModel := mapFeatureFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *FeatureFeeRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.FeatureFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "GetAll"))
	var model models.FeatureFee
	var listModels []models.FeatureFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.FeatureFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.FeatureFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.FeatureFee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.FeatureFee{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.FeatureFeeCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.FeatureFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.FeatureFee

	for _, externalModel := range listModels {
		internalModel := mapFeatureFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *FeatureFeeRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.FeatureFeePagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeRepository", "GetAllWithPagination"))
	var listModels []models.FeatureFee
	var model models.FeatureFee

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.FeatureFeeCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.FeatureFeePagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.FeatureFeePagin{}, err
	}

	var internalModels []internal.FeatureFee

	for _, externalModel := range listModels {
		internalModel := mapFeatureFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.FeatureFeePagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapFeatureFeeToInternalData(data models.FeatureFee) internal.FeatureFee {
	return internal.FeatureFee{
		ID:             data.ID.Hex(),
		CompanyID:      data.CompanyID.Hex(),
		Name:           data.Name,
		Description:    pkg.GetStringValue(data.Description, ""),
		FeeType:        pkg.GetStringValue(data.FeeType, ""),
		FeeValue:       pkg.GetFloat64Value(data.FeeValue, 0),
		MinTransaction: pkg.GetFloat64Value(data.MinTransaction, 0),
		MaxTransaction: pkg.GetFloat64Value(data.MaxTransaction, 0),
		Currency:       pkg.GetStringValue(data.Currency, ""),
		Status:         constant.ReverseConstant(int(data.Status)),
		CreatedAt:      data.CreatedAt,
		UpdatedAt:      data.UpdatedAt,
	}
}

func mapFeatureFeeFromParams(params internal.CreateFeatureFee, action string) (models.FeatureFee, error) {
	model := models.FeatureFee{
		Name:           params.Name,
		Description:    pkg.SetStringLowerPointer(params.Description, action),
		FeeType:        pkg.SetStringLowerPointer(params.FeeType, action),
		FeeValue:       pkg.SetFloat64Pointer(params.FeeValue, action),
		MinTransaction: pkg.SetFloat64Pointer(params.MinTransaction, action),
		MaxTransaction: pkg.SetFloat64Pointer(params.MaxTransaction, action),
		Currency:       pkg.SetStringUpperPointer(params.Currency, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.FeatureFee{}, err
		}
		model.CompanyID = objectID
	}

	return model, nil
}
