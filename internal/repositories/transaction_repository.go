package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type TransactionRepository struct {
	util riot.Util
}

func NewTransactionRepository(util riot.Util) *TransactionRepository {
	return &TransactionRepository{
		util: util,
	}
}

func (r *TransactionRepository) Create(ctx context.Context, params internal.CreateTransaction) (internal.Transaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapTransactionFromParams(params, "create")

	if err != nil {
		return internal.Transaction{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.TransactionCollection, &model)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransactionRepository.Create")
	}
	return mapTransactionToInternalData(model), nil
}

func (r *TransactionRepository) CreateBulk(ctx context.Context, params []internal.CreateTransaction) ([]internal.Transaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapTransactionFromParams(param, "create")

		if err != nil {
			return []internal.Transaction{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Transaction{}).CreateBulk(ctx, r.util.DB, models.TransactionCollection, listModels)

	if err != nil {
		return []internal.Transaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransactionRepository.CreateBulk")
	}

	return []internal.Transaction{}, nil
}

func (r *TransactionRepository) Update(ctx context.Context, id string, params internal.CreateTransaction) (internal.Transaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository.Repo", "Update"))

	model, err := mapTransactionFromParams(params, "update")

	if err != nil {
		return internal.Transaction{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	if err := model.Update(ctx, r.util.DB, models.TransactionCollection, bson.M{"_id": objectID}, &model); err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransactionRepository.Update")
	}

	return r.GetByID(ctx, id)
}

func (r *TransactionRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateTransaction) (internal.Transaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapTransactionFromParams(params, "update")

	if err != nil {
		return internal.Transaction{}, err
	}

	err = model.Update(ctx, r.util.DB, models.TransactionCollection, bsonM, &model)

	if err != nil {
		return internal.Transaction{}, err
	}

	return r.GetByFilter(ctx, filter)
}

func (r *TransactionRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "SoftDelete"))
	var model models.Transaction
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.TransactionCollection, bson.M{"_id": idHex})
}

func (r *TransactionRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "SoftDeleteByFilter"))

	var model models.Transaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.TransactionCollection, bsonM)
}

func (r *TransactionRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Transaction, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "GetByFilter"))

	var model models.Transaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Transaction{}, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.TransactionCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Transaction{}, nil
		}

		return internal.Transaction{}, err
	}

	return mapTransactionToInternalData(model), nil
}

func (r *TransactionRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "SumByFilter"))

	var model models.Transaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.TransactionCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *TransactionRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "CountByFilter"))

	var model models.Transaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.TransactionCollection, bsonM)
}

func (r *TransactionRepository) GetByID(ctx context.Context, id string) (internal.Transaction, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "GetByID"))

	var model models.Transaction
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.TransactionCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Transaction{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Transaction{}, err
	}
	return mapTransactionToInternalData(model), nil
}

func (r *TransactionRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Transaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "GetAll"))
	var model models.Transaction
	var listModels []models.Transaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.TransactionCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Transaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransactionRepository.FindAll")
	}

	var internalModels []internal.Transaction

	for _, externalModel := range listModels {
		internalModel := mapTransactionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *TransactionRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Transaction, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "GetAll"))
	var model models.Transaction
	var listModels []models.Transaction

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["merchant_id"] = objectID
		case "buyer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["buyer_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["transaction_id"] = objectID
		case "referrer_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["referrer_id"] = objectID
		case "marketing_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["marketing_id"] = objectID
		case "voucher_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Transaction{}, err
			}
			bsonM["voucher_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.TransactionCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Transaction{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "TransactionRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Transaction

	for _, externalModel := range listModels {
		internalModel := mapTransactionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *TransactionRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.TransactionPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionRepository", "GetAllWithPagination"))
	var listModels []models.Transaction
	var model models.Transaction

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.TransactionCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.TransactionPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.TransactionPagin{}, err
	}

	var internalModels []internal.Transaction

	for _, externalModel := range listModels {
		internalModel := mapTransactionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.TransactionPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapTransactionToInternalData(data models.Transaction) internal.Transaction {
	return internal.Transaction{
		ID:        data.ID.Hex(),
		CompanyID: data.CompanyID.Hex(),
		ProfileID: data.ProfileID.Hex(),
		BuyerID: func() string {
			if data.BuyerID != nil {
				return data.BuyerID.Hex()
			}
			return ""
		}(),
		MerchantID: data.MerchantID.Hex(),
		ReferrerID: func() string {
			if data.ReferrerID != nil {
				return data.ReferrerID.Hex()
			}
			return ""
		}(),
		MarketingID: func() string {
			if data.MarketingID != nil {
				return data.MarketingID.Hex()
			}
			return ""
		}(),
		VoucherID: func() string {
			if data.VoucherID != nil {
				return data.VoucherID.Hex()
			}
			return ""
		}(),
		Fee: func() *internal.Fee {
			if data.Fee != nil {
				fee := mapFeeToInternalData(*data.Fee)
				return &fee
			}
			return nil
		}(),
		UniqueNo:           pkg.GetStringValue(data.UniqueNo, ""),
		TransactionNo:      pkg.GetStringValue(data.TransactionNo, ""),
		BuyerName:          pkg.GetStringValue(data.BuyerName, ""),
		BuyerEmail:         pkg.GetStringValue(data.BuyerEmail, ""),
		SellerName:         pkg.GetStringValue(data.SellerName, ""),
		SellerEmail:        pkg.GetStringValue(data.SellerEmail, ""),
		ReferenceNo:        pkg.GetStringValue(data.ReferenceNo, ""),
		Description:        pkg.GetStringValue(data.Description, ""),
		Amount:             pkg.GetFloat64Value(data.Amount, 0),
		NetAmount:          pkg.GetFloat64Value(data.NetAmount, 0),
		TotalPayment:       pkg.GetFloat64Value(data.TotalPayment, 0),
		PlatformFeeType:    pkg.GetStringValue(data.PlatformFeeType, ""),
		PlatformFeeValue:   pkg.GetFloat64Value(data.PlatformFeeValue, 0),
		TotalReferrerFee:   pkg.GetFloat64Value(data.TotalReferrerFee, 0),
		TotalMarketingFee:  pkg.GetFloat64Value(data.TotalMarketingFee, 0),
		TotalPlatformFee:   pkg.GetFloat64Value(data.TotalPlatformFee, 0),
		TotalFeatureFee:    pkg.GetFloat64Value(data.TotalFeatureFee, 0),
		TotalSubsidyFee:    pkg.GetFloat64Value(data.TotalSubsidyFee, 0),
		TotalFee:           pkg.GetFloat64Value(data.TotalFee, 0),
		ThirdPartyFee:      pkg.GetFloat64Value(data.ThirdPartyFee, 0),
		TotalDiscount:      pkg.GetFloat64Value(data.TotalDiscount, 0),
		PGFee:              pkg.GetFloat64Value(data.PGFee, 0),
		PGRealFee:          pkg.GetFloat64Value(data.PGRealFee, 0),
		PGPlatformFee:      pkg.GetFloat64Value(data.PGPlatformFee, 0),
		PaymentMethod:      pkg.GetStringValue(data.PaymentMethod, ""),
		PaymentType:        pkg.GetStringValue(data.PaymentType, ""),
		PaymentChannel:     pkg.GetStringValue(data.PaymentChannel, ""),
		DescriptionSubsidy: pkg.GetStringValue(data.DescriptionSubsidy, ""),
		Tax:                pkg.GetFloat64Value(data.Tax, 0),
		Features:           pkg.GetArrStringValue(data.Features, []string{}),
		UseVoucher:         pkg.GetBoolValue(data.UseVoucher, false),
		VoucherCode:        pkg.GetStringValue(data.VoucherCode, ""),
		ProductType:        pkg.GetStringValue(data.ProductType, ""),
		Metadata:           pkg.GetMapInterface(data.Metadata, map[string]interface{}{}),
		Status:             constant.ReverseConstant(int(data.Status)),
		CreatedAt:          data.CreatedAt,
		UpdatedAt:          data.UpdatedAt,
	}
}

func mapTransactionFromParams(params internal.CreateTransaction, action string) (models.Transaction, error) {
	model := models.Transaction{
		UniqueNo:           pkg.SetStringPointer(params.UniqueNo, action),
		BuyerName:          pkg.SetStringPointer(params.BuyerName, action),
		BuyerEmail:         pkg.SetStringPointer(params.BuyerEmail, action),
		TransactionNo:      pkg.SetStringUpperPointer(params.TransactionNo, action),
		ReferenceNo:        pkg.SetStringUpperPointer(params.ReferenceNo, action),
		Description:        pkg.SetStringPointer(params.Description, action),
		Amount:             pkg.SetFloat64Pointer(params.Amount, action),
		PaymentMethod:      pkg.SetStringPointer(params.PaymentMethod, action),
		PaymentType:        pkg.SetStringPointer(params.PaymentType, action),
		PaymentChannel:     pkg.SetStringPointer(params.PaymentChannel, action),
		NetAmount:          pkg.SetFloat64Pointer(params.NetAmount, action),
		TotalPayment:       pkg.SetFloat64Pointer(params.TotalPayment, action),
		PlatformFeeType:    pkg.SetStringLowerPointer(params.PlatformFeeType, action),
		PlatformFeeValue:   pkg.SetFloat64Pointer(params.PlatformFeeValue, action),
		TotalReferrerFee:   pkg.SetFloat64Pointer(params.TotalReferrerFee, action),
		TotalMarketingFee:  pkg.SetFloat64Pointer(params.TotalMarketingFee, action),
		TotalPlatformFee:   pkg.SetFloat64Pointer(params.TotalPlatformFee, action),
		TotalFeatureFee:    pkg.SetFloat64Pointer(params.TotalFeatureFee, action),
		TotalSubsidyFee:    pkg.SetFloat64Pointer(params.TotalSubsidyFee, action),
		TotalPaymentFee:    pkg.SetFloat64Pointer(params.TotalPaymentFee, action),
		TotalDiscount:      pkg.SetFloat64Pointer(params.TotalDiscount, action),
		TotalFee:           pkg.SetFloat64Pointer(params.TotalFee, action),
		ThirdPartyFee:      pkg.SetFloat64Pointer(params.ThirdPartyFee, action),
		PGFee:              pkg.SetFloat64Pointer(params.PGFee, action),
		PGRealFee:          pkg.SetFloat64Pointer(params.PGRealFee, action),
		PGPlatformFee:      pkg.SetFloat64Pointer(params.PGPlatformFee, action),
		DescriptionSubsidy: pkg.SetStringPointer(params.DescriptionSubsidy, action),
		Features:           pkg.SetArrStringPointer(params.Features, action),
		UseVoucher:         pkg.SetBoolPointer(params.UseVoucher, action),
		VoucherCode:        pkg.SetStringPointer(params.VoucherCode, action),
		Tax:                pkg.SetFloat64Pointer(params.Tax, action),
		Metadata:           pkg.SetMapInterfacePointer(params.Metadata, action),
		ProductType:        pkg.SetStringUpperPointer(params.ProductType, action),
		Fee: func() *models.Fee {
			if params.Fee != nil {
				model, err := mapFeeFromParams(*params.Fee, action)
				if err != nil {
					return nil
				}
				return &model
			}
			return nil
		}(),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Transaction{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.Transaction{}, err
		}
		model.ProfileID = objectID
	}

	if params.BuyerID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.BuyerID)
		if err != nil {
			return models.Transaction{}, err
		}
		model.BuyerID = &objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.Transaction{}, err
		}
		model.MerchantID = objectID
	}

	if params.ReferrerID != nil && *params.ReferrerID != "" {
		objectID, err := primitive.ObjectIDFromHex(*params.ReferrerID)
		if err != nil {
			return models.Transaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "invalid referrer id")
		}
		model.ReferrerID = &objectID
	}

	if params.MarketingID != nil && *params.MarketingID != "" {
		objectID, err := primitive.ObjectIDFromHex(*params.MarketingID)
		if err != nil {
			return models.Transaction{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "invalid marketing id")
		}
		model.MarketingID = &objectID
	}

	if params.VoucherID != nil && *params.VoucherID != "" {
		objectID, err := primitive.ObjectIDFromHex(*params.VoucherID)
		if err != nil {
			return models.Transaction{}, riot.NewErrorf(riot.ErrorCodeNotFound, "invalid voucher id")
		}
		model.VoucherID = &objectID
	}

	return model, nil
}
