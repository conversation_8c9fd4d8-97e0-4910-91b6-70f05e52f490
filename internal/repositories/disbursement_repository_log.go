package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type DisbursementLogRepository struct {
	util riot.Util
}

func NewDisbursementLogRepository(util riot.Util) *DisbursementLogRepository {
	return &DisbursementLogRepository{
		util: util,
	}
}

func (r *DisbursementLogRepository) Create(ctx context.Context, params internal.CreateDisbursementLog) (internal.DisbursementLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapDisbursementLogFromParams(params, "create")

	if err != nil {
		return internal.DisbursementLog{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.DisbursementLogCollection, &model)
	if err != nil {
		return internal.DisbursementLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementLogRepository.Create")
	}
	return mapDisbursementLogToInternalData(model), nil
}

func (r *DisbursementLogRepository) CreateBulk(ctx context.Context, params []internal.CreateDisbursementLog) ([]internal.DisbursementLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapDisbursementLogFromParams(param, "create")

		if err != nil {
			return []internal.DisbursementLog{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.DisbursementLog{}).CreateBulk(ctx, r.util.DB, models.DisbursementLogCollection, listModels)

	if err != nil {
		return []internal.DisbursementLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementLogRepository.CreateBulk")
	}

	return []internal.DisbursementLog{}, nil
}

func (r *DisbursementLogRepository) Update(ctx context.Context, id string, params internal.CreateDisbursementLog) (internal.DisbursementLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository.Repo", "Update"))

	model, err := mapDisbursementLogFromParams(params, "update")

	if err != nil {
		return internal.DisbursementLog{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.DisbursementLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.DisbursementLogCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *DisbursementLogRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateDisbursementLog) (internal.DisbursementLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["transaction_id"] = objectID
		case "disbursement_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["disbursement_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapDisbursementLogFromParams(params, "update")

	if err != nil {
		return internal.DisbursementLog{}, err
	}

	model.Update(ctx, r.util.DB, models.DisbursementLogCollection, bsonM, &model)

	return mapDisbursementLogToInternalData(model), nil
}

func (r *DisbursementLogRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "SoftDelete"))
	var model models.DisbursementLog
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementLogRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.DisbursementLogCollection, bson.M{"_id": idHex})
}

func (r *DisbursementLogRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "SoftDeleteByFilter"))

	var model models.DisbursementLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		case "disbursement_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["disbursement_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.DisbursementLogCollection, bsonM)
}

func (r *DisbursementLogRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.DisbursementLog, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "GetByFilter"))

	var model models.DisbursementLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["transaction_id"] = objectID
		case "disbursement_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.DisbursementLog{}, err
			}
			bsonM["disbursement_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.DisbursementLogCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.DisbursementLog{}, nil
		}

		return internal.DisbursementLog{}, err
	}

	return mapDisbursementLogToInternalData(model), nil
}

func (r *DisbursementLogRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "SumByFilter"))

	var model models.DisbursementLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		case "disbursement_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["disbursement_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.DisbursementLogCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *DisbursementLogRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "CountByFilter"))

	var model models.DisbursementLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		case "disbursement_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["disbursement_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.DisbursementLogCollection, bsonM)
}

func (r *DisbursementLogRepository) GetByID(ctx context.Context, id string) (internal.DisbursementLog, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "GetByID"))

	var model models.DisbursementLog
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementLogRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.DisbursementLogCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.DisbursementLog{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.DisbursementLog{}, err
	}
	return mapDisbursementLogToInternalData(model), nil
}

func (r *DisbursementLogRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.DisbursementLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "GetAll"))
	var model models.DisbursementLog
	var listModels []models.DisbursementLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["transaction_id"] = objectID
		case "disbursement_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["disbursement_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.DisbursementLogCollection, bsonM, &listModels)
	if err != nil {
		return []internal.DisbursementLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementLogRepository.FindAll")
	}

	var internalModels []internal.DisbursementLog

	for _, externalModel := range listModels {
		internalModel := mapDisbursementLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *DisbursementLogRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.DisbursementLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "GetAll"))
	var model models.DisbursementLog
	var listModels []models.DisbursementLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["transaction_id"] = objectID
		case "disbursement_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.DisbursementLog{}, err
			}
			bsonM["disbursement_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.DisbursementLogCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.DisbursementLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementLogRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.DisbursementLog

	for _, externalModel := range listModels {
		internalModel := mapDisbursementLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *DisbursementLogRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.DisbursementLogPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementLogRepository", "GetAllWithPagination"))
	var listModels []models.DisbursementLog
	var model models.DisbursementLog

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.DisbursementLogCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.DisbursementLogPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.DisbursementLogPagin{}, err
	}

	var internalModels []internal.DisbursementLog

	for _, externalModel := range listModels {
		internalModel := mapDisbursementLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.DisbursementLogPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapDisbursementLogToInternalData(data models.DisbursementLog) internal.DisbursementLog {
	return internal.DisbursementLog{
		ID:                     data.ID.Hex(),
		CompanyID:              data.CompanyID.Hex(),
		ProfileID:              data.ProfileID.Hex(),
		MerchantID:             data.MerchantID.Hex(),
		MerchantBankID:         data.MerchantBankID.Hex(),
		DisbursementID:         data.DisbursementID.Hex(),
		ExternalID:             pkg.GetStringValue(data.ExternalID, ""),
		DisbursementNo:         pkg.GetStringValue(data.DisbursementNo, ""),
		ProviderDisbursementID: pkg.GetStringValue(data.ProviderDisbursementID, ""),
		Provider:               pkg.GetStringValue(data.Provider, ""),
		Status:                 constant.ReverseConstant(int(data.Status)),
		ErrorMessage:           pkg.GetStringValue(data.ErrorMessage, ""),
		ProviderStatus:         pkg.GetStringValue(data.ProviderStatus, ""),
		CreatedAt:              data.CreatedAt,
		UpdatedAt:              data.UpdatedAt,
	}
}

func mapDisbursementLogFromParams(params internal.CreateDisbursementLog, action string) (models.DisbursementLog, error) {
	model := models.DisbursementLog{
		ExternalID:             pkg.SetStringPointer(params.ExternalID, action),
		DisbursementNo:         pkg.SetStringPointer(params.DisbursementNo, action),
		ProviderDisbursementID: pkg.SetStringPointer(params.ProviderDisbursementID, action),
		Provider:               pkg.SetStringPointer(params.Provider, action),
		ErrorMessage:           pkg.SetStringPointer(params.ErrorMessage, action),
		ProviderStatus:         pkg.SetStringPointer(params.ProviderStatus, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.DisbursementLog{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.DisbursementLog{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.DisbursementLog{}, err
		}
		model.MerchantID = objectID
	}

	if params.MerchantBankID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantBankID)
		if err != nil {
			return models.DisbursementLog{}, err
		}
		model.MerchantBankID = objectID
	}

	if params.DisbursementID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.DisbursementID)
		if err != nil {
			return models.DisbursementLog{}, err
		}
		model.DisbursementID = objectID
	}

	return model, nil
}
