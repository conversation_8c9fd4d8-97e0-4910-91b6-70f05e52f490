package repositories

import (
	"context"
	"strings"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type MerchantPlatformFeeRepository struct {
	util riot.Util
}

func NewMerchantPlatformFeeRepository(util riot.Util) *MerchantPlatformFeeRepository {
	return &MerchantPlatformFeeRepository{
		util: util,
	}
}

func (r *MerchantPlatformFeeRepository) Create(ctx context.Context, params internal.CreateMerchantPlatformFee) (internal.MerchantPlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapMerchantPlatformFeeFromParams(params, "create")

	if err != nil {
		return internal.MerchantPlatformFee{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.MerchantPlatformFeeCollection, &model)
	if err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeRepository.Create")
	}
	return mapMerchantPlatformFeeToInternalData(model), nil
}

func (r *MerchantPlatformFeeRepository) CreateBulk(ctx context.Context, params []internal.CreateMerchantPlatformFee) ([]internal.MerchantPlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapMerchantPlatformFeeFromParams(param, "create")

		if err != nil {
			return []internal.MerchantPlatformFee{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.MerchantPlatformFee{}).CreateBulk(ctx, r.util.DB, models.MerchantPlatformFeeCollection, listModels)

	if err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeRepository.CreateBulk")
	}

	return []internal.MerchantPlatformFee{}, nil
}

func (r *MerchantPlatformFeeRepository) Update(ctx context.Context, id string, params internal.CreateMerchantPlatformFee) (internal.MerchantPlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository.Repo", "Update"))

	model, err := mapMerchantPlatformFeeUpdateFromParams(params, "update")

	if err != nil {
		return internal.MerchantPlatformFee{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	err = (&models.MerchantPlatformFee{}).Update(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bson.M{"_id": objectID}, &model)

	if err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeRepository.Update")
	}

	return r.GetByID(ctx, id)
}

func (r *MerchantPlatformFeeRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantPlatformFee) (internal.MerchantPlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatformFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatformFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatformFee{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatformFee{}, err
			}
			bsonM["merchant_id"] = objectID

		default:
			bsonM[key] = value
		}
	}

	model, err := mapMerchantPlatformFeeUpdateFromParams(params, "update")

	if err != nil {
		return internal.MerchantPlatformFee{}, err
	}

	err = (&models.MerchantPlatformFee{}).Update(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bsonM, &model)

	if err != nil {
		return internal.MerchantPlatformFee{}, err
	}

	return r.GetByFilter(ctx, filter)
}

func (r *MerchantPlatformFeeRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "SoftDelete"))
	var model models.MerchantPlatformFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bson.M{"_id": idHex})
}

func (r *MerchantPlatformFeeRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "SoftDeleteByFilter"))

	var model models.MerchantPlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bsonM)
}

func (r *MerchantPlatformFeeRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantPlatformFee, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "GetByFilter"))

	var model models.MerchantPlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatformFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatformFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatformFee{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.MerchantPlatformFee{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.MerchantPlatformFee{}, nil
		}

		return internal.MerchantPlatformFee{}, err
	}

	return mapMerchantPlatformFeeToInternalData(model), nil
}

func (r *MerchantPlatformFeeRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "SumByFilter"))

	var model models.MerchantPlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.MerchantPlatformFeeCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *MerchantPlatformFeeRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "CountByFilter"))

	var model models.MerchantPlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bsonM)
}

func (r *MerchantPlatformFeeRepository) GetByID(ctx context.Context, id string) (internal.MerchantPlatformFee, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "GetByID"))

	var model models.MerchantPlatformFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantPlatformFee{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantPlatformFee{}, err
	}
	return mapMerchantPlatformFeeToInternalData(model), nil
}

func (r *MerchantPlatformFeeRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantPlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "GetAll"))
	var model models.MerchantPlatformFee
	var listModels []models.MerchantPlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatformFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatformFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatformFee{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatformFee{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bsonM, &listModels)
	if err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeRepository.FindAll")
	}

	var internalModels []internal.MerchantPlatformFee

	for _, externalModel := range listModels {
		internalModel := mapMerchantPlatformFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantPlatformFeeRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantPlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "GetAll"))
	var model models.MerchantPlatformFee
	var listModels []models.MerchantPlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatformFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatformFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatformFee{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.MerchantPlatformFee{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.MerchantPlatformFeeCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.MerchantPlatformFee

	for _, externalModel := range listModels {
		internalModel := mapMerchantPlatformFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *MerchantPlatformFeeRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantPlatformFeePagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeRepository", "GetAllWithPagination"))
	var listModels []models.MerchantPlatformFee
	var model models.MerchantPlatformFee

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.MerchantPlatformFeeCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.MerchantPlatformFeePagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.MerchantPlatformFeePagin{}, err
	}

	var internalModels []internal.MerchantPlatformFee

	for _, externalModel := range listModels {
		internalModel := mapMerchantPlatformFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.MerchantPlatformFeePagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapMerchantPlatformFeeToInternalData(data models.MerchantPlatformFee) internal.MerchantPlatformFee {
	return internal.MerchantPlatformFee{
		ID:                     data.ID.Hex(),
		CompanyID:              data.CompanyID.Hex(),
		ProfileID:              data.ProfileID.Hex(),
		MerchantID:             data.MerchantID.Hex(),
		HasMarketingFee:        pkg.GetBoolValue(data.HasMarketingFee, false),
		HasReferralFee:         pkg.GetBoolValue(data.HasReferralFee, false),
		HasCashbackProviderFee: pkg.GetBoolValue(data.HasCashbackProviderFee, false),
		ProductType:            data.ProductType,
		Metadata:               pkg.GetMapstring(data.Metadata, map[string]string{}),
		Provider:               pkg.GetStringValue(data.Provider, ""),
		Status:                 constant.ReverseConstant(int(data.Status)),
		CreatedAt:              data.CreatedAt,
		UpdatedAt:              data.UpdatedAt,
	}
}

func mapMerchantPlatformFeeFromParams(params internal.CreateMerchantPlatformFee, action string) (models.MerchantPlatformFee, error) {
	model := models.MerchantPlatformFee{
		ProductType: func() string {
			if params.ProductType != "" {
				return strings.ToUpper(params.ProductType)
			}
			return ""
		}(),
		HasMarketingFee:        pkg.SetBoolPointer(params.HasMarketingFee, action),
		HasReferralFee:         pkg.SetBoolPointer(params.HasReferralFee, action),
		HasCashbackProviderFee: pkg.SetBoolPointer(params.HasCashbackProviderFee, action),
		Metadata:               pkg.SetMapstringPointer(params.Metadata, action),
		Provider:               pkg.SetStringPointer(params.Provider, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.MerchantPlatformFee{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.MerchantPlatformFee{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.MerchantPlatformFee{}, err
		}
		model.MerchantID = objectID
	}

	if params.ReferrerID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ReferrerID)
		if err != nil {
			return models.MerchantPlatformFee{}, err
		}
		model.ReferrerID = &objectID
	}

	if params.MarketingID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MarketingID)
		if err != nil {
			return models.MerchantPlatformFee{}, err
		}
		model.MarketingID = &objectID
	}

	return model, nil
}

func mapMerchantPlatformFeeUpdateFromParams(params internal.CreateMerchantPlatformFee, action string) (models.MerchantPlatformFeeUpdate, error) {
	model := models.MerchantPlatformFeeUpdate{
		ProductType: func() string {
			if params.ProductType != "" {
				return strings.ToUpper(params.ProductType)
			}
			return ""
		}(),
		HasMarketingFee:        pkg.SetBoolPointer(params.HasMarketingFee, action),
		HasReferralFee:         pkg.SetBoolPointer(params.HasReferralFee, action),
		HasCashbackProviderFee: pkg.SetBoolPointer(params.HasCashbackProviderFee, action),
		Metadata:               pkg.SetMapstringPointer(params.Metadata, action),
		Provider:               pkg.SetStringPointer(params.Provider, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.MerchantPlatformFeeUpdate{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.MerchantPlatformFeeUpdate{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.MerchantPlatformFeeUpdate{}, err
		}
		model.MerchantID = objectID
	}

	if params.ReferrerID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ReferrerID)
		if err != nil {
			return models.MerchantPlatformFeeUpdate{}, err
		}
		model.ReferrerID = &objectID
	}

	if params.MarketingID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MarketingID)
		if err != nil {
			return models.MerchantPlatformFeeUpdate{}, err
		}
		model.MarketingID = &objectID
	}

	return model, nil
}
