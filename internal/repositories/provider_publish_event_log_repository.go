package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ProviderPublishEventLogRepository struct {
	util riot.Util
}

func NewProviderPublishEventLogRepository(util riot.Util) *ProviderPublishEventLogRepository {
	return &ProviderPublishEventLogRepository{
		util: util,
	}
}

func (r *ProviderPublishEventLogRepository) Create(ctx context.Context, params internal.CreateProviderPublishEventLog) (internal.ProviderPublishEventLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapProviderPublishEventLogFromParams(params, "create")

	if err != nil {
		return internal.ProviderPublishEventLog{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.ProviderPublishEventLogCollection, &model)
	if err != nil {
		return internal.ProviderPublishEventLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderPublishEventLogRepository.Create")
	}
	return mapProviderPublishEventLogToInternalData(model), nil
}

func (r *ProviderPublishEventLogRepository) CreateBulk(ctx context.Context, params []internal.CreateProviderPublishEventLog) ([]internal.ProviderPublishEventLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapProviderPublishEventLogFromParams(param, "create")

		if err != nil {
			return []internal.ProviderPublishEventLog{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.ProviderPublishEventLog{}).CreateBulk(ctx, r.util.DB, models.ProviderPublishEventLogCollection, listModels)

	if err != nil {
		return []internal.ProviderPublishEventLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderPublishEventLogRepository.CreateBulk")
	}

	return []internal.ProviderPublishEventLog{}, nil
}

func (r *ProviderPublishEventLogRepository) Update(ctx context.Context, id string, params internal.CreateProviderPublishEventLog) (internal.ProviderPublishEventLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository.Repo", "Update"))

	model, err := mapProviderPublishEventLogFromParams(params, "update")

	if err != nil {
		return internal.ProviderPublishEventLog{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.ProviderPublishEventLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *ProviderPublishEventLogRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateProviderPublishEventLog) (internal.ProviderPublishEventLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderPublishEventLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderPublishEventLog{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapProviderPublishEventLogFromParams(params, "update")

	if err != nil {
		return internal.ProviderPublishEventLog{}, err
	}

	model.Update(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bsonM, &model)

	return mapProviderPublishEventLogToInternalData(model), nil
}

func (r *ProviderPublishEventLogRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "SoftDelete"))
	var model models.ProviderPublishEventLog
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderPublishEventLogRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bson.M{"_id": idHex})
}

func (r *ProviderPublishEventLogRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "SoftDeleteByFilter"))

	var model models.ProviderPublishEventLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bsonM)
}

func (r *ProviderPublishEventLogRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.ProviderPublishEventLog, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "GetByFilter"))

	var model models.ProviderPublishEventLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderPublishEventLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderPublishEventLog{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.ProviderPublishEventLog{}, nil
		}

		return internal.ProviderPublishEventLog{}, err
	}

	return mapProviderPublishEventLogToInternalData(model), nil
}

func (r *ProviderPublishEventLogRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "SumByFilter"))

	var model models.ProviderPublishEventLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.ProviderPublishEventLogCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *ProviderPublishEventLogRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "CountByFilter"))

	var model models.ProviderPublishEventLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bsonM)
}

func (r *ProviderPublishEventLogRepository) GetByID(ctx context.Context, id string) (internal.ProviderPublishEventLog, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "GetByID"))

	var model models.ProviderPublishEventLog
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderPublishEventLogRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ProviderPublishEventLog{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ProviderPublishEventLog{}, err
	}
	return mapProviderPublishEventLogToInternalData(model), nil
}

func (r *ProviderPublishEventLogRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.ProviderPublishEventLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "GetAll"))
	var model models.ProviderPublishEventLog
	var listModels []models.ProviderPublishEventLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderPublishEventLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderPublishEventLog{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bsonM, &listModels)
	if err != nil {
		return []internal.ProviderPublishEventLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderPublishEventLogRepository.FindAll")
	}

	var internalModels []internal.ProviderPublishEventLog

	for _, externalModel := range listModels {
		internalModel := mapProviderPublishEventLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ProviderPublishEventLogRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.ProviderPublishEventLog, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "GetAll"))
	var model models.ProviderPublishEventLog
	var listModels []models.ProviderPublishEventLog

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderPublishEventLog{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderPublishEventLog{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.ProviderPublishEventLogCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.ProviderPublishEventLog{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderPublishEventLogRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.ProviderPublishEventLog

	for _, externalModel := range listModels {
		internalModel := mapProviderPublishEventLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ProviderPublishEventLogRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ProviderPublishEventLogPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderPublishEventLogRepository", "GetAllWithPagination"))
	var listModels []models.ProviderPublishEventLog
	var model models.ProviderPublishEventLog

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.ProviderPublishEventLogCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ProviderPublishEventLogPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ProviderPublishEventLogPagin{}, err
	}

	var internalModels []internal.ProviderPublishEventLog

	for _, externalModel := range listModels {
		internalModel := mapProviderPublishEventLogToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.ProviderPublishEventLogPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapProviderPublishEventLogToInternalData(data models.ProviderPublishEventLog) internal.ProviderPublishEventLog {
	return internal.ProviderPublishEventLog{
		ID:        data.ID.Hex(),
		CompanyID: data.CompanyID.Hex(),
		Provider:  data.Provider,
		Event:     pkg.GetStringValue(data.Event, ""),
		TransactionID: func() string {
			if data.TransactionID != nil {
				return data.TransactionID.Hex()
			}
			return ""
		}(),
		ExternalID:   pkg.GetStringValue(data.ExternalID, ""),
		SubAccountID: pkg.GetStringValue(data.SubAccountID, ""),
		Data:         pkg.GetStringValue(data.Data, ""),
		Status:       constant.ReverseConstant(int(data.Status)),
		CreatedAt:    data.CreatedAt,
		UpdatedAt:    data.UpdatedAt,
	}
}

func mapProviderPublishEventLogFromParams(params internal.CreateProviderPublishEventLog, action string) (models.ProviderPublishEventLog, error) {
	model := models.ProviderPublishEventLog{
		Provider:     params.Provider,
		Event:        pkg.SetStringPointer(params.Event, action),
		Data:         pkg.SetStringPointer(params.Data, action),
		ExternalID:   pkg.SetStringPointer(params.ExternalID, action),
		SubAccountID: pkg.SetStringPointer(params.SubAccountID, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.ProviderPublishEventLog{}, err
		}
		model.CompanyID = objectID
	}

	if params.TransactionID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.TransactionID)
		if err != nil {
			return models.ProviderPublishEventLog{}, err
		}
		model.TransactionID = &objectID
	}

	return model, nil
}
