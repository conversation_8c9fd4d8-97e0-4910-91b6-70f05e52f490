package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ProviderFeeRepository struct {
	util riot.Util
}

func NewProviderFeeRepository(util riot.Util) *ProviderFeeRepository {
	return &ProviderFeeRepository{
		util: util,
	}
}

func (r *ProviderFeeRepository) Create(ctx context.Context, params internal.CreateProviderFee) (internal.ProviderFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapProviderFeeFromParams(params, "create")

	if err != nil {
		return internal.ProviderFee{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.ProviderFeeCollection, &model)
	if err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeRepository.Create")
	}
	return mapProviderFeeToInternalData(model), nil
}

func (r *ProviderFeeRepository) CreateBulk(ctx context.Context, params []internal.CreateProviderFee) ([]internal.ProviderFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapProviderFeeFromParams(param, "create")

		if err != nil {
			return []internal.ProviderFee{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.ProviderFee{}).CreateBulk(ctx, r.util.DB, models.ProviderFeeCollection, listModels)

	if err != nil {
		return []internal.ProviderFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeRepository.CreateBulk")
	}

	return []internal.ProviderFee{}, nil
}

func (r *ProviderFeeRepository) Update(ctx context.Context, id string, params internal.CreateProviderFee) (internal.ProviderFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository.Repo", "Update"))

	model, err := mapProviderFeeFromParams(params, "update")

	if err != nil {
		return internal.ProviderFee{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.ProviderFeeCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *ProviderFeeRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateProviderFee) (internal.ProviderFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderFee{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapProviderFeeFromParams(params, "update")

	if err != nil {
		return internal.ProviderFee{}, err
	}

	model.Update(ctx, r.util.DB, models.ProviderFeeCollection, bsonM, &model)

	return mapProviderFeeToInternalData(model), nil
}

func (r *ProviderFeeRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "SoftDelete"))
	var model models.ProviderFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderFeeRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.ProviderFeeCollection, bson.M{"_id": idHex})
}

func (r *ProviderFeeRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "SoftDeleteByFilter"))

	var model models.ProviderFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.ProviderFeeCollection, bsonM)
}

func (r *ProviderFeeRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.ProviderFee, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "GetByFilter"))

	var model models.ProviderFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ProviderFee{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.ProviderFeeCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.ProviderFee{}, nil
		}

		return internal.ProviderFee{}, err
	}

	return mapProviderFeeToInternalData(model), nil
}

func (r *ProviderFeeRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "SumByFilter"))

	var model models.ProviderFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.ProviderFeeCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *ProviderFeeRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "CountByFilter"))

	var model models.ProviderFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.ProviderFeeCollection, bsonM)
}

func (r *ProviderFeeRepository) GetByID(ctx context.Context, id string) (internal.ProviderFee, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "GetByID"))

	var model models.ProviderFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderFeeRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.ProviderFeeCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ProviderFee{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ProviderFee{}, err
	}
	return mapProviderFeeToInternalData(model), nil
}

func (r *ProviderFeeRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.ProviderFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "GetAll"))
	var model models.ProviderFee
	var listModels []models.ProviderFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderFee{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.ProviderFeeCollection, bsonM, &listModels)
	if err != nil {
		return []internal.ProviderFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeRepository.FindAll")
	}

	var internalModels []internal.ProviderFee

	for _, externalModel := range listModels {
		internalModel := mapProviderFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ProviderFeeRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.ProviderFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "GetAll"))
	var model models.ProviderFee
	var listModels []models.ProviderFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ProviderFee{}, err
			}
			bsonM["profile_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.ProviderFeeCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.ProviderFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.ProviderFee

	for _, externalModel := range listModels {
		internalModel := mapProviderFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ProviderFeeRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ProviderFeePagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeRepository", "GetAllWithPagination"))
	var listModels []models.ProviderFee
	var model models.ProviderFee

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.ProviderFeeCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ProviderFeePagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ProviderFeePagin{}, err
	}

	var internalModels []internal.ProviderFee

	for _, externalModel := range listModels {
		internalModel := mapProviderFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.ProviderFeePagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapProviderFeeToInternalData(data models.ProviderFee) internal.ProviderFee {
	return internal.ProviderFee{
		ID:         data.ID.Hex(),
		CompanyID:  data.CompanyID.Hex(),
		ProfileID:  data.ProfileID.Hex(),
		ProviderID: data.ProviderID.Hex(),
		Fees: func() []internal.FeeDetail {
			if data.Fees != nil {
				feeDetails := make([]internal.FeeDetail, len(data.Fees))
				for i, feeDetail := range data.Fees {
					feeDetails[i] = internal.FeeDetail{
						PaymentMethod: feeDetail.PaymentMethod,
					}
				}
				return feeDetails
			}
			return []internal.FeeDetail{}
		}(),
		Metadata:  pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:    constant.ReverseConstant(int(data.Status)),
		CreatedAt: data.CreatedAt,
		UpdatedAt: data.UpdatedAt,
	}
}

func mapProviderFeeFromParams(params internal.CreateProviderFee, action string) (models.ProviderFee, error) {
	model := models.ProviderFee{}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.ProviderFee{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.ProviderFee{}, err
		}
		model.ProfileID = objectID
	}

	if params.ProviderID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProviderID)
		if err != nil {
			return models.ProviderFee{}, err
		}
		model.ProviderID = objectID
	}

	return model, nil
}
