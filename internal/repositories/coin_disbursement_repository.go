package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type CoinDisbursementRepository struct {
	util riot.Util
}

func NewCoinDisbursementRepository(util riot.Util) *CoinDisbursementRepository {
	return &CoinDisbursementRepository{
		util: util,
	}
}

func (r *CoinDisbursementRepository) Create(ctx context.Context, params internal.CreateCoinDisbursement) (internal.CoinDisbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapCoinDisbFromParams(params, "create")

	if err != nil {
		return internal.CoinDisbursement{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.CoinDisbursementCollection, &model)
	if err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementRepository.Create")
	}
	return mapCoinDisbToInternalData(model), nil
}

func (r *CoinDisbursementRepository) CreateBulk(ctx context.Context, params []internal.CreateCoinDisbursement) ([]internal.CoinDisbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapCoinDisbFromParams(param, "create")

		if err != nil {
			return []internal.CoinDisbursement{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.CoinDisbursement{}).CreateBulk(ctx, r.util.DB, models.CoinDisbursementCollection, listModels)

	if err != nil {
		return []internal.CoinDisbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementRepository.CreateBulk")
	}

	return []internal.CoinDisbursement{}, nil
}

func (r *CoinDisbursementRepository) Update(ctx context.Context, id string, params internal.CreateCoinDisbursement) (internal.CoinDisbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository.Repo", "Update"))

	model, err := mapCoinDisbFromParams(params, "update")

	if err != nil {
		return internal.CoinDisbursement{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.CoinDisbursementCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *CoinDisbursementRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateCoinDisbursement) (internal.CoinDisbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapCoinDisbFromParams(params, "update")

	if err != nil {
		return internal.CoinDisbursement{}, err
	}

	model.Update(ctx, r.util.DB, models.CoinDisbursementCollection, bsonM, &model)

	return mapCoinDisbToInternalData(model), nil
}

func (r *CoinDisbursementRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "SoftDelete"))
	var model models.CoinDisbursement
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinDisbursementRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.CoinDisbursementCollection, bson.M{"_id": idHex})
}

func (r *CoinDisbursementRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "SoftDeleteByFilter"))

	var model models.CoinDisbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.CoinDisbursementCollection, bsonM)
}

func (r *CoinDisbursementRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.CoinDisbursement, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "GetByFilter"))

	var model models.CoinDisbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.CoinDisbursement{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.CoinDisbursementCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.CoinDisbursement{}, nil
		}

		return internal.CoinDisbursement{}, err
	}

	return mapCoinDisbToInternalData(model), nil
}

func (r *CoinDisbursementRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "SumByFilter"))

	var model models.CoinDisbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.CoinDisbursementCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *CoinDisbursementRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "CountByFilter"))

	var model models.CoinDisbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.CoinDisbursementCollection, bsonM)
}

func (r *CoinDisbursementRepository) GetByID(ctx context.Context, id string) (internal.CoinDisbursement, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "GetByID"))

	var model models.CoinDisbursement
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinDisbursementRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.CoinDisbursementCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.CoinDisbursement{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.CoinDisbursement{}, err
	}
	return mapCoinDisbToInternalData(model), nil
}

func (r *CoinDisbursementRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.CoinDisbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "GetAll"))
	var model models.CoinDisbursement
	var listModels []models.CoinDisbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.CoinDisbursementCollection, bsonM, &listModels)
	if err != nil {
		return []internal.CoinDisbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementRepository.FindAll")
	}

	var internalModels []internal.CoinDisbursement

	for _, externalModel := range listModels {
		internalModel := mapCoinDisbToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *CoinDisbursementRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.CoinDisbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "GetAll"))
	var model models.CoinDisbursement
	var listModels []models.CoinDisbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.CoinDisbursement{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.CoinDisbursementCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.CoinDisbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.CoinDisbursement

	for _, externalModel := range listModels {
		internalModel := mapCoinDisbToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *CoinDisbursementRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.CoinDisbursementPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementRepository", "GetAllWithPagination"))
	var listModels []models.CoinDisbursement
	var model models.CoinDisbursement

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.CoinDisbursementCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.CoinDisbursementPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.CoinDisbursementPagin{}, err
	}

	var internalModels []internal.CoinDisbursement

	for _, externalModel := range listModels {
		internalModel := mapCoinDisbToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.CoinDisbursementPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapCoinDisbToInternalData(data models.CoinDisbursement) internal.CoinDisbursement {
	return internal.CoinDisbursement{
		ID:          data.ID.Hex(),
		CompanyID:   data.CompanyID.Hex(),
		ProfileID:   data.ProfileID.Hex(),
		CoinID:      data.CoinID.Hex(),
		Amount:      pkg.GetFloat64Value(data.Amount, 0),
		Description: pkg.GetStringValue(data.Description, ""),
		Metadata:    pkg.GetMapInterface(data.Metadata, map[string]interface{}{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapCoinDisbFromParams(params internal.CreateCoinDisbursement, action string) (models.CoinDisbursement, error) {
	model := models.CoinDisbursement{
		Amount:      pkg.SetFloat64Pointer(params.Amount, action),
		Description: pkg.SetStringPointer(params.Description, action),
		Metadata:    pkg.SetMapInterfacePointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.CoinDisbursement{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinDisbFromParams.params.CompanyID")
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.CoinDisbursement{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinDisbFromParams.params.ProfileID")
		}
		model.ProfileID = objectID
	}

	if params.CoinID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CoinID)
		if err != nil {
			return models.CoinDisbursement{}, riot.WrapErrorf(err, riot.ErrorCodeNotFound, "mapCoinDisbFromParams.params.CoinID")
		}
		model.CoinID = objectID
	}

	return model, nil
}
