package repositories

import (
	"context"
	"strings"
	"time"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type VoucherRepository struct {
	util riot.Util
}

func NewVoucherRepository(util riot.Util) *VoucherRepository {
	return &VoucherRepository{
		util: util,
	}
}

func (r *VoucherRepository) Create(ctx context.Context, params internal.CreateVoucher) (internal.Voucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapVoucherFromParams(params, "create")

	if err != nil {
		return internal.Voucher{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.VoucherCollection, &model)
	if err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "VoucherRepository.Create")
	}
	return mapToInternalData(model), nil
}

func (r *VoucherRepository) CreateBulk(ctx context.Context, params []internal.CreateVoucher) ([]internal.Voucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapVoucherFromParams(param, "create")

		if err != nil {
			return []internal.Voucher{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Voucher{}).CreateBulk(ctx, r.util.DB, models.VoucherCollection, listModels)

	if err != nil {
		return []internal.Voucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "VoucherRepository.CreateBulk")
	}

	return []internal.Voucher{}, nil
}

func (r *VoucherRepository) Update(ctx context.Context, id string, params internal.CreateVoucher) (internal.Voucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository.Repo", "Update"))

	model, err := mapVoucherFromParams(params, "update")

	if err != nil {
		return internal.Voucher{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.VoucherCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *VoucherRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateVoucher) (internal.Voucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Voucher{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Voucher{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Voucher{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Voucher{}, err
			}
			bsonM["merchant_id"] = objectID

		default:
			bsonM[key] = value
		}
	}

	model, err := mapVoucherFromParams(params, "update")

	if err != nil {
		return internal.Voucher{}, err
	}

	model.Update(ctx, r.util.DB, models.VoucherCollection, bsonM, &model)

	return mapToInternalData(model), nil
}

func (r *VoucherRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "SoftDelete"))
	var model models.Voucher
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.VoucherCollection, bson.M{"_id": idHex})
}

func (r *VoucherRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "SoftDeleteByFilter"))

	var model models.Voucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.VoucherCollection, bsonM)
}

func (r *VoucherRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Voucher, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "GetByFilter"))

	var model models.Voucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Voucher{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Voucher{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Voucher{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Voucher{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.VoucherCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Voucher{}, nil
		}

		return internal.Voucher{}, err
	}

	return mapToInternalData(model), nil
}

func (r *VoucherRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "SumByFilter"))

	var model models.Voucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.VoucherCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *VoucherRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "CountByFilter"))

	var model models.Voucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.VoucherCollection, bsonM)
}

func (r *VoucherRepository) GetByID(ctx context.Context, id string) (internal.Voucher, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "GetByID"))

	var model models.Voucher
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.VoucherCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Voucher{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Voucher{}, err
	}
	return mapToInternalData(model), nil
}

func (r *VoucherRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Voucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "GetAll"))
	var model models.Voucher
	var listModels []models.Voucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Voucher{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Voucher{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Voucher{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Voucher{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.VoucherCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Voucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "VoucherRepository.FindAll")
	}

	var internalModels []internal.Voucher

	for _, externalModel := range listModels {
		internalModel := mapToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *VoucherRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Voucher, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "GetAll"))
	var model models.Voucher
	var listModels []models.Voucher

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Voucher{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Voucher{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Voucher{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Voucher{}, err
			}
			bsonM["merchant_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.VoucherCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Voucher{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "VoucherRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Voucher

	for _, externalModel := range listModels {
		internalModel := mapToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *VoucherRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.VoucherPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherRepository", "GetAllWithPagination"))
	var listModels []models.Voucher
	var model models.Voucher

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.VoucherCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.VoucherPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.VoucherPagin{}, err
	}

	var internalModels []internal.Voucher

	for _, externalModel := range listModels {
		internalModel := mapToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.VoucherPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapToInternalData(data models.Voucher) internal.Voucher {
	return internal.Voucher{
		ID:               data.ID.Hex(),
		CompanyID:        data.CompanyID.Hex(),
		Code:             pkg.GetStringValue(data.Code, ""),
		DiscountType:     pkg.GetStringValue(data.DiscountType, ""),
		DiscountValue:    pkg.GetFloat64Value(data.DiscountValue, 0),
		MinPurchase:      pkg.GetFloat64Value(data.MinPurchase, 0),
		MaxDiscount:      pkg.GetFloat64Value(data.MaxDiscount, 0),
		UsageLimit:       pkg.GetIntValue(data.UsageLimit, 0),
		EnableLimit:      pkg.GetBoolValue(data.EnableLimit, false),
		RemainingUsage:   pkg.GetIntValue(data.RemainingUsage, 0),
		ExpiresAt:        data.ExpiresAt,
		ExpireType:       data.ExpireType,
		ValidFrom:        data.ValidFrom,
		ValidTo:          data.ValidTo,
		Period:           pkg.GetIntValue(data.Period, 0),
		PeriodType:       pkg.GetStringValue(data.PeriodType, ""),
		LevelPriority:    pkg.GetIntValue(data.LevelPriority, 0),
		Type:             pkg.GetStringValue(data.Type, ""),
		Combined:         pkg.GetBoolValue(data.Combined, false),
		CombinationTypes: *data.CombinationTypes,
		ProductType:      data.ProductType,
		Description:      pkg.GetStringValue(data.Description, ""),
		Metadata:         pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:           constant.ReverseConstant(int(data.Status)),
		CreatedAt:        data.CreatedAt,
		UpdatedAt:        data.UpdatedAt,
	}
}

func mapVoucherFromParams(params internal.CreateVoucher, action string) (models.Voucher, error) {
	model := models.Voucher{
		Code:             pkg.SetStringUpperPointer(params.Code, action),
		CombinationTypes: pkg.SetStringSlicerPointer(params.CombinationTypes, action),
		Combined:         pkg.SetBoolPointer(params.Combined, action),
		DiscountType:     pkg.SetStringPointer(params.DiscountType, action),
		Period:           pkg.SetIntPointer(params.Period, action),
		PeriodType:       pkg.SetStringPointer(params.PeriodType, action),
		LevelPriority:    pkg.SetIntPointer(params.LevelPriority, action),
		MinPurchase:      pkg.SetFloat64Pointer(params.MinPurchase, action),
		MaxDiscount:      pkg.SetFloat64Pointer(params.MaxDiscount, action),
		UsageLimit:       pkg.SetIntPointer(params.UsageLimit, action),
		EnableLimit:      pkg.SetBoolPointer(params.EnableLimit, action),
		RemainingUsage:   pkg.SetIntPointer(params.RemainingUsage, action),
		Description:      pkg.SetStringPointer(params.Description, action),
		Type:             pkg.SetStringPointer(params.Type, action),
		ExpireType:       params.ExpireType,
		ValidFrom: func() *time.Time {
			if params.ValidFrom != "" && params.ExpireType == "fixed_range" {
				validFrom, _ := pkg.ParseDateString(params.ValidFrom, pkg.DateLayoutDefault)
				return &validFrom
			}
			return nil
		}(),
		ValidTo: func() *time.Time {
			if params.ValidTo != "" && params.ExpireType == "fixed_range" {
				validTo, _ := pkg.ParseDateString(params.ValidTo, pkg.DateLayoutDefault)
				return &validTo
			}
			return nil
		}(),
		ProductType: func() string {
			if params.ProductType != "" {
				return strings.ToUpper(params.ProductType)
			}
			return ""
		}(),
		Metadata: pkg.SetMapstringPointer(params.Metadata, action),
		ExpiresAt: func() *time.Time {
			if params.ExpiresAt != "" {
				expiresAt, _ := pkg.ParseDateString(params.ExpiresAt, pkg.DateLayoutDefault)
				return &expiresAt
			}
			return nil
		}(),
	}

	if model.ValidTo != nil {
		model.ExpiresAt = model.ValidTo
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Voucher{}, err
		}
		model.CompanyID = objectID
	}

	return model, nil
}
