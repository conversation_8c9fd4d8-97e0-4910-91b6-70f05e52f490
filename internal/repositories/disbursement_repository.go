package repositories

import (
	"context"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type DisbursementRepository struct {
	util riot.Util
}

func NewDisbursementRepository(util riot.Util) *DisbursementRepository {
	return &DisbursementRepository{
		util: util,
	}
}

func (r *DisbursementRepository) Create(ctx context.Context, params internal.CreateDisbursement) (internal.Disbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapDisbursementFromParams(params, "create")

	if err != nil {
		return internal.Disbursement{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.DisbursementCollection, &model)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementRepository.Create")
	}
	return mapDisbursementToInternalData(model), nil
}

func (r *DisbursementRepository) CreateBulk(ctx context.Context, params []internal.CreateDisbursement) ([]internal.Disbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapDisbursementFromParams(param, "create")

		if err != nil {
			return []internal.Disbursement{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Disbursement{}).CreateBulk(ctx, r.util.DB, models.DisbursementCollection, listModels)

	if err != nil {
		return []internal.Disbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementRepository.CreateBulk")
	}

	return []internal.Disbursement{}, nil
}

func (r *DisbursementRepository) Update(ctx context.Context, id string, params internal.CreateDisbursement) (internal.Disbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository.Repo", "Update"))

	model, err := mapDisbursementFromParams(params, "update")

	if err != nil {
		return internal.Disbursement{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	if err := model.Update(ctx, r.util.DB, models.DisbursementCollection, bson.M{"_id": objectID}, &model); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementRepository.Update")
	}

	return r.GetByID(ctx, id)
}

func (r *DisbursementRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateDisbursement) (internal.Disbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapDisbursementFromParams(params, "update")

	if err != nil {
		return internal.Disbursement{}, err
	}

	model.Update(ctx, r.util.DB, models.DisbursementCollection, bsonM, &model)

	return mapDisbursementToInternalData(model), nil
}

func (r *DisbursementRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "SoftDelete"))
	var model models.Disbursement
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.DisbursementCollection, bson.M{"_id": idHex})
}

func (r *DisbursementRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "SoftDeleteByFilter"))

	var model models.Disbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.DisbursementCollection, bsonM)
}

func (r *DisbursementRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Disbursement, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "GetByFilter"))

	var model models.Disbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Disbursement{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.DisbursementCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Disbursement{}, nil
		}

		return internal.Disbursement{}, err
	}

	return mapDisbursementToInternalData(model), nil
}

func (r *DisbursementRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "SumByFilter"))

	var model models.Disbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.DisbursementCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *DisbursementRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "CountByFilter"))

	var model models.Disbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.DisbursementCollection, bsonM)
}

func (r *DisbursementRepository) GetByID(ctx context.Context, id string) (internal.Disbursement, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "GetByID"))

	var model models.Disbursement
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.DisbursementCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Disbursement{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Disbursement{}, err
	}
	return mapDisbursementToInternalData(model), nil
}

func (r *DisbursementRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Disbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "GetAll"))
	var model models.Disbursement
	var listModels []models.Disbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.DisbursementCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Disbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementRepository.FindAll")
	}

	var internalModels []internal.Disbursement

	for _, externalModel := range listModels {
		internalModel := mapDisbursementToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *DisbursementRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Disbursement, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "GetAll"))
	var model models.Disbursement
	var listModels []models.Disbursement

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["company_id"] = objectID
		case "merchant_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["merchant_id"] = objectID
		case "transaction_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Disbursement{}, err
			}
			bsonM["transaction_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.DisbursementCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Disbursement{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Disbursement

	for _, externalModel := range listModels {
		internalModel := mapDisbursementToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *DisbursementRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.DisbursementPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementRepository", "GetAllWithPagination"))
	var listModels []models.Disbursement
	var model models.Disbursement

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.DisbursementCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.DisbursementPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.DisbursementPagin{}, err
	}

	var internalModels []internal.Disbursement

	for _, externalModel := range listModels {
		internalModel := mapDisbursementToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.DisbursementPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapDisbursementToInternalData(data models.Disbursement) internal.Disbursement {
	return internal.Disbursement{
		ID:                     data.ID.Hex(),
		CompanyID:              data.CompanyID.Hex(),
		ProfileID:              data.ProfileID.Hex(),
		MerchantID:             data.MerchantID.Hex(),
		MerchantBankID:         data.MerchantBankID.Hex(),
		ExternalID:             pkg.GetStringValue(data.ExternalID, ""),
		DisbursementNo:         pkg.GetStringValue(data.DisbursementNo, ""),
		ProviderDisbursementID: pkg.GetStringValue(data.ProviderDisbursementID, ""),
		Amount:                 pkg.GetFloat64Value(data.Amount, 0.00),
		NetAmount:              pkg.GetFloat64Value(data.NetAmount, 0.00),
		PlatformFee:            pkg.GetFloat64Value(data.PlatformFee, 0.00),
		PGFee:                  pkg.GetFloat64Value(data.PGFee, 0.00),
		Fee:                    pkg.GetFloat64Value(data.Fee, 0.00),
		BankCode:               pkg.GetStringValue(data.BankCode, ""),
		AccountHolderName:      pkg.GetStringValue(data.AccountHolderName, ""),
		AccountNumber:          pkg.GetStringValue(data.AccountNumber, ""),
		Description:            pkg.GetStringValue(data.Description, ""),
		Provider:               pkg.GetStringValue(data.Provider, ""),
		IsInstant:              pkg.GetBoolValue(data.IsInstant, false),
		IsAuto:                 pkg.GetBoolValue(data.IsAuto, false),
		Status:                 constant.ReverseConstant(int(data.Status)),
		ErrorMessage:           pkg.GetStringValue(data.ErrorMessage, ""),
		ProviderStatus:         pkg.GetStringValue(data.ProviderStatus, ""),
		DisbursedAt:            data.DisbursedAt,
		FailedAt:               data.FailedAt,
		CreatedAt:              data.CreatedAt,
		UpdatedAt:              data.UpdatedAt,
	}
}

func mapDisbursementFromParams(params internal.CreateDisbursement, action string) (models.Disbursement, error) {
	model := models.Disbursement{
		ExternalID:             pkg.SetStringPointer(params.ExternalID, action),
		DisbursementNo:         pkg.SetStringPointer(params.DisbursementNo, action),
		ProviderDisbursementID: pkg.SetStringPointer(params.ProviderDisbursementID, action),
		Amount:                 pkg.SetFloat64Pointer(params.Amount, action),
		NetAmount:              pkg.SetFloat64Pointer(params.NetAmount, action),
		PlatformFee:            pkg.SetFloat64Pointer(params.PlatformFee, action),
		PGFee:                  pkg.SetFloat64Pointer(params.PGFee, action),
		Fee:                    pkg.SetFloat64Pointer(params.Fee, action),
		BankCode:               pkg.SetStringPointer(params.BankCode, action),
		AccountHolderName:      pkg.SetStringPointer(params.AccountHolderName, action),
		AccountNumber:          pkg.SetStringPointer(params.AccountNumber, action),
		Description:            pkg.SetStringPointer(params.Description, action),
		Provider:               pkg.SetStringPointer(params.Provider, action),
		ErrorMessage:           pkg.SetStringPointer(params.ErrorMessage, action),
		ProviderStatus:         pkg.SetStringPointer(params.ProviderStatus, action),
		IsInstant:              pkg.SetBoolPointer(params.IsInstant, action),
		IsAuto:                 pkg.SetBoolPointer(params.IsAuto, action),
		DisbursedAt:            params.DisbursedAt,
		FailedAt:               params.FailedAt,
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.Disbursement{}, err
		}
		model.CompanyID = objectID
	}

	if params.ProfileID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ProfileID)
		if err != nil {
			return models.Disbursement{}, err
		}
		model.ProfileID = objectID
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return models.Disbursement{}, err
		}
		model.MerchantID = objectID
	}

	if params.MerchantBankID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantBankID)
		if err != nil {
			return models.Disbursement{}, err
		}
		model.MerchantBankID = objectID
	}

	return model, nil
}
