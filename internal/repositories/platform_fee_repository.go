package repositories

import (
	"context"
	"strings"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/models"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/constant"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type PlatformFeeRepository struct {
	util riot.Util
}

func NewPlatformFeeRepository(util riot.Util) *PlatformFeeRepository {
	return &PlatformFeeRepository{
		util: util,
	}
}

func (r *PlatformFeeRepository) Create(ctx context.Context, params internal.CreatePlatformFee) (internal.PlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapPlatformFeeFromParams(params, "create")

	if err != nil {
		return internal.PlatformFee{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.PlatformFeeCollection, &model)
	if err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeRepository.Create")
	}
	return mapPlatformFeeToInternalData(model), nil
}

func (r *PlatformFeeRepository) CreateBulk(ctx context.Context, params []internal.CreatePlatformFee) ([]internal.PlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapPlatformFeeFromParams(param, "create")

		if err != nil {
			return []internal.PlatformFee{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.PlatformFee{}).CreateBulk(ctx, r.util.DB, models.PlatformFeeCollection, listModels)

	if err != nil {
		return []internal.PlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeRepository.CreateBulk")
	}

	return []internal.PlatformFee{}, nil
}

func (r *PlatformFeeRepository) Update(ctx context.Context, id string, params internal.CreatePlatformFee) (internal.PlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository.Repo", "Update"))

	model, err := mapPlatformFeeFromParams(params, "update")

	if err != nil {
		return internal.PlatformFee{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.PlatformFeeCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *PlatformFeeRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreatePlatformFee) (internal.PlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PlatformFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PlatformFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PlatformFee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PlatformFee{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapPlatformFeeFromParams(params, "update")

	if err != nil {
		return internal.PlatformFee{}, err
	}

	model.Update(ctx, r.util.DB, models.PlatformFeeCollection, bsonM, &model)

	return mapPlatformFeeToInternalData(model), nil
}

func (r *PlatformFeeRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "SoftDelete"))
	var model models.PlatformFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.PlatformFeeCollection, bson.M{"_id": idHex})
}

func (r *PlatformFeeRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "SoftDeleteByFilter"))

	var model models.PlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.PlatformFeeCollection, bsonM)
}

func (r *PlatformFeeRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.PlatformFee, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "GetByFilter"))

	var model models.PlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PlatformFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PlatformFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PlatformFee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.PlatformFee{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.PlatformFeeCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.PlatformFee{}, nil
		}

		return internal.PlatformFee{}, err
	}

	return mapPlatformFeeToInternalData(model), nil
}

func (r *PlatformFeeRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "SumByFilter"))

	var model models.PlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.PlatformFeeCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *PlatformFeeRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "CountByFilter"))

	var model models.PlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.PlatformFeeCollection, bsonM)
}

func (r *PlatformFeeRepository) GetByID(ctx context.Context, id string) (internal.PlatformFee, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "GetByID"))

	var model models.PlatformFee
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.PlatformFeeCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.PlatformFee{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.PlatformFee{}, err
	}
	return mapPlatformFeeToInternalData(model), nil
}

func (r *PlatformFeeRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.PlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "GetAll"))
	var model models.PlatformFee
	var listModels []models.PlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PlatformFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PlatformFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PlatformFee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PlatformFee{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.PlatformFeeCollection, bsonM, &listModels)
	if err != nil {
		return []internal.PlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeRepository.FindAll")
	}

	var internalModels []internal.PlatformFee

	for _, externalModel := range listModels {
		internalModel := mapPlatformFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *PlatformFeeRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.PlatformFee, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "GetAll"))
	var model models.PlatformFee
	var listModels []models.PlatformFee

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PlatformFee{}, err
			}
			bsonM["_id"] = objectID
		case "profile_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PlatformFee{}, err
			}
			bsonM["profile_id"] = objectID
		case "company_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PlatformFee{}, err
			}
			bsonM["company_id"] = objectID
		case "provider_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.PlatformFee{}, err
			}
			bsonM["provider_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.PlatformFeeCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.PlatformFee{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.PlatformFee

	for _, externalModel := range listModels {
		internalModel := mapPlatformFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *PlatformFeeRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.PlatformFeePagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeRepository", "GetAllWithPagination"))
	var listModels []models.PlatformFee
	var model models.PlatformFee

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.PlatformFeeCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.PlatformFeePagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.PlatformFeePagin{}, err
	}

	var internalModels []internal.PlatformFee

	for _, externalModel := range listModels {
		internalModel := mapPlatformFeeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.PlatformFeePagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapPlatformFeeToInternalData(data models.PlatformFee) internal.PlatformFee {
	return internal.PlatformFee{
		ID:          data.ID.Hex(),
		CompanyID:   data.CompanyID.Hex(),
		Provider:    pkg.GetStringValue(data.Provider, ""),
		ProductType: data.ProductType,
		Metadata:    pkg.GetMapstring(data.Metadata, map[string]string{}),
		Status:      constant.ReverseConstant(int(data.Status)),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapPlatformFeeFromParams(params internal.CreatePlatformFee, action string) (models.PlatformFee, error) {
	model := models.PlatformFee{
		Provider: pkg.SetStringPointer(params.Provider, action),
		ProductType: func() string {
			if params.ProductType != "" {
				return strings.ToUpper(params.ProductType)
			}
			return ""
		}(),
		Metadata: pkg.SetMapstringPointer(params.Metadata, action),
	}

	if params.Status != "" {
		model.Status = uint8(constant.Constant(params.Status))
	}

	if params.CompanyID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.CompanyID)
		if err != nil {
			return models.PlatformFee{}, err
		}
		model.CompanyID = objectID
	}

	return model, nil
}
