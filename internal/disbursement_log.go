package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type DisbursementLog struct {
	ID                     string     `json:"_id"`
	CompanyID              string     `json:"company_id"`
	ProfileID              string     `json:"profile_id"`
	MerchantID             string     `json:"merchant_id"`
	MerchantBankID         string     `json:"merchant_bank_id"`
	DisbursementID         string     `json:"disbursement_id"`
	ExternalID             string     `json:"external_id"`
	DisbursementNo         string     `json:"disbursement_no"`
	ProviderDisbursementID string     `json:"provider_disbursement_id"`
	ErrorMessage           string     `json:"error_message"`
	Provider               string     `json:"provider"`
	ProviderStatus         string     `json:"provider_status"`
	Status                 string     `json:"status"`
	CreatedAt              *time.Time `json:"created_at"`
	UpdatedAt              *time.Time `json:"updated_at"`
}

type DisbursementLogPagin struct {
	Limit        int               `json:"limit"`
	Page         int               `json:"page"`
	Sort         string            `json:"sort"`
	TotalRecords int               `json:"total_records"`
	TotalPages   int               `json:"total_pages"`
	Records      []DisbursementLog `json:"records"`
}

func (b DisbursementLog) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b DisbursementLog) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b DisbursementLog) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *DisbursementLog) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateDisbursementLog struct {
	CompanyID              string  `json:"company_id,omitempty"`
	ProfileID              string  `json:"profile_id,omitempty"`
	MerchantID             string  `json:"merchant_id,omitempty"`
	MerchantBankID         string  `json:"merchant_bank_id"`
	DisbursementID         string  `json:"disbursement_id"`
	ProviderDisbursementID *string `json:"provider_disbursement_id"`
	Provider               *string `json:"provider,omitempty"`
	ExternalID             *string `json:"external_id,omitempty"`
	DisbursementNo         *string `json:"disbursement_no,omitempty"`
	Status                 string  `json:"status,omitempty"`
	ErrorMessage           *string `json:"error_message,omitempty"`
	ProviderStatus         *string `json:"provider_status,omitempty"`
}

func (ct CreateDisbursementLog) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.MerchantID, validation.Required),
		validation.Field(&ct.MerchantBankID, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
