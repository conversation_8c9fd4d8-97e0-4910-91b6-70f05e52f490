package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type CoinDisbursement struct {
	ID          string                 `json:"_id"`
	CompanyID   string                 `json:"company_id"`
	ProfileID   string                 `json:"profile_id"`
	CoinID      string                 `json:"coin_id"`
	Amount      float64                `json:"amount"`
	Description string                 `json:"description"`
	Metadata    map[string]interface{} `json:"metadata"`
	Status      string                 `json:"status"`
	CreatedAt   *time.Time             `json:"created_at"`
	UpdatedAt   *time.Time             `json:"updated_at"`
}

type CoinDisbursementProtected struct {
	ID          string                 `json:"_id"`
	CompanyID   string                 `json:"company_id"`
	ProfileID   string                 `json:"profile_id"`
	CoinID      string                 `json:"coin_id"`
	Amount      float64                `json:"amount"`
	Description string                 `json:"description"`
	Metadata    map[string]interface{} `json:"metadata"`
	Status      string                 `json:"status"`
	CreatedAt   *time.Time             `json:"created_at"`
	UpdatedAt   *time.Time             `json:"updated_at"`
}

type CoinDisbursementPagin struct {
	Limit        int                `json:"limit"`
	Page         int                `json:"page"`
	Sort         string             `json:"sort"`
	TotalRecords int                `json:"total_records"`
	TotalPages   int                `json:"total_pages"`
	Records      []CoinDisbursement `json:"records"`
}

func (b CoinDisbursement) IsExist() bool {
	return b.ID != ""
}

func (b CoinDisbursement) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b CoinDisbursement) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ID)),
		}
	}
	return nil
}

func (b CoinDisbursement) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b CoinDisbursement) ValidateUserNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			"User": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ProfileID)),
		}
	}
	return nil
}

func (b CoinDisbursement) ValidateSourceNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			"Source": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ProfileID)),
		}
	}
	return nil
}

func (b *CoinDisbursement) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateCoinDisbursement struct {
	CompanyID   string                  `json:"company_id,omitempty"`
	ProfileID   string                  `json:"profile_id,omitempty"`
	CoinID      string                  `json:"coin_id,omitempty"`
	Amount      *float64                `json:"amount,omitempty"`
	Description *string                 `json:"description,omitempty"`
	Metadata    *map[string]interface{} `json:"metadata,omitempty"`
	Status      string                  `json:"status,omitempty"`
}

func (ct CreateCoinDisbursement) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.CoinID, validation.Required),
		validation.Field(&ct.Amount, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
