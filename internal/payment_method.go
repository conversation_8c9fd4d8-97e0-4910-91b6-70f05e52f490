package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type PaymentMethod struct {
	ID                 string  `json:"_id"`
	CompanyID          string  `json:"company_id"`
	Slug               string  `json:"slug"`
	Name               string  `json:"name"`
	DisplayName        string  `json:"display_name"`
	Description        string  `json:"description"`
	ChannelCode        string  `json:"channel_code"`
	CheckoutMethod     string  `json:"checkout_method"`
	FeeType            string  `json:"fee_type"`
	FeeValue           float64 `json:"fee_value"`
	ExtraFeeType       string  `json:"extra_fee_type"`
	ExtraFeeValue      float64 `json:"extra_fee_value"`
	PGFeeType          string  `json:"pg_fee_type"`
	PGFeeValue         float64 `json:"pg_fee_value"`
	PGExtraFeeType     string  `json:"pg_extra_fee_type"`
	PGExtraFeeValue    float64 `json:"pg_extra_fee_value"`
	SuccessRedirectURL string  `json:"success_redirect_url"`
	FailureRedirectURL string  `json:"failure_redirect_url"`
	IsRedirectRequired bool    `json:"is_redirect_required"`
	Metadata           map[string]string
	Provider           string     `json:"provider"`
	Type               string     `json:"type"`
	Status             string     `json:"status"`
	CreatedAt          *time.Time `json:"created_at"`
	UpdatedAt          *time.Time `json:"updated_at"`
}

type PaymentMethodProtected struct {
	ID          string     `json:"_id"`
	Slug        string     `json:"slug"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Status      string     `json:"status"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
}

type PaymentMethodPagin struct {
	Limit        int             `json:"limit"`
	Page         int             `json:"page"`
	Sort         string          `json:"sort"`
	TotalRecords int             `json:"total_records"`
	TotalPages   int             `json:"total_pages"`
	Records      []PaymentMethod `json:"records"`
}

func (b PaymentMethod) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b PaymentMethod) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.Name)),
		}
	}
	return nil
}

func (b PaymentMethod) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *PaymentMethod) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreatePaymentMethod struct {
	CompanyID          string   `json:"company_id,omitempty"`
	Slug               *string  `json:"slug,omitempty"`
	Name               *string  `json:"name,omitempty"`
	DisplayName        *string  `json:"display_name,omitempty"`
	Description        *string  `json:"description,omitempty"`
	ChannelCode        *string  `json:"channel_code,omitempty"`
	CheckoutMethod     *string  `json:"checkout_method,omitempty"`
	FeeType            *string  `json:"fee_type,omitempty"`
	FeeValue           *float64 `json:"fee_value,omitempty"`
	ExtraFeeType       *string  `json:"extra_fee_type,omitempty"`
	ExtraFeeValue      *float64 `json:"extra_fee_value,omitempty"`
	PGFeeType          *string  `json:"pg_fee_type,omitempty"`
	PGFeeValue         *float64 `json:"pg_fee_value,omitempty"`
	PGExtraFeeType     *string  `json:"pg_extra_fee_type,omitempty"`
	PGExtraFeeValue    *float64 `json:"pg_extra_fee_value,omitempty"`
	SuccessRedirectURL *string  `json:"success_redirect_url,omitempty"`
	FailureRedirectURL *string  `json:"failure_redirect_url,omitempty"`
	IsRedirectRequired *bool    `json:"is_redirect_required,omitempty"`
	Provider           *string  `json:"provider,omitempty"`
	Metadata           *map[string]string
	Type               *string `json:"type,omitempty"`
	Status             string  `json:"status,omitempty"`
}

func (ct CreatePaymentMethod) Validate() error {

	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.DisplayName, validation.Required),
		validation.Field(&ct.ChannelCode, validation.Required),
		validation.Field(&ct.FeeType, validation.Required, validation.In("percentage", "fixed")),
		validation.Field(&ct.FeeValue, validation.Required),
		validation.Field(&ct.ExtraFeeType, validation.In("percentage", "fixed")),
		validation.Field(&ct.ExtraFeeValue),
		validation.Field(&ct.PGFeeType, validation.Required, validation.In("percentage", "fixed")),
		validation.Field(&ct.PGFeeValue, validation.Required),
		validation.Field(&ct.PGExtraFeeType, validation.In("percentage", "fixed")),
		validation.Field(&ct.PGExtraFeeValue),
		validation.Field(&ct.PGExtraFeeType, validation.In("percentage", "fixed")),
		validation.Field(&ct.PGExtraFeeValue),
		validation.Field(&ct.Provider, validation.Required, validation.In("xendit")),
		validation.Field(&ct.Type, validation.Required, validation.In("credit_card", "va", "ewallet")),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
