package services

import (
	"context"
	"math"
	"math/big"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type MerchantPlatformFeeRepository interface {
	Create(ctx context.Context, params internal.CreateMerchantPlatformFee) (internal.MerchantPlatformFee, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateMerchantPlatformFee) (internal.MerchantPlatformFee, error)
	GetByID(ctx context.Context, id string) (internal.MerchantPlatformFee, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantPlatformFee, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantPlatformFeePagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantPlatformFee, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantPlatformFee) (internal.MerchantPlatformFee, error)
}

type MerchantPlatformFeeService struct {
	repo            MerchantPlatformFeeRepository
	marchantRepo    MerchantRepository
	platformFeeRepo PlatformFeeRepository
	feeRepo         FeeRepository
	configRepo      ConfigurationRepository
	cb              *circuitbreaker.CircuitBreaker
	util            riot.Util
}

func NewMerchantPlatformFeeService(util riot.Util, repo MerchantPlatformFeeRepository, marchantRepo MerchantRepository, platformFeeRepo PlatformFeeRepository, feeRepo FeeRepository, configRepo ConfigurationRepository) *MerchantPlatformFeeService {
	return &MerchantPlatformFeeService{
		repo:            repo,
		marchantRepo:    marchantRepo,
		platformFeeRepo: platformFeeRepo,
		feeRepo:         feeRepo,
		configRepo:      configRepo,
		util:            util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *MerchantPlatformFeeService) Create(ctx context.Context, identifier internal.Identifier, params internal.CreateMerchantPlatformFee, nexusData riot.Nexus) (_ []internal.MerchantPlatformFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeService", "Create"))

	if !s.cb.Ready() {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	if err := identifier.ValidateMarchantID(); err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.Create.identifier.Validate")
	}

	profile := riot.GetNexusProfile(nexusData)
	hasMarketingFee, hasReferralFee, hasCbProviderFee := false, false, false

	//find config marketing fee
	filterConfig := map[string]interface{}{
		"name":       "MARKETING",
		"company_id": params.CompanyID,
		"status":     constant.TypeActive,
	}

	config, err := s.configRepo.GetByFilter(ctx, filterConfig)
	if err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.s.repo.GetByFilter")
	}

	if err := config.ValidateNotExist(); err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.Create.s.repo.GetByFilter")
	}

	switch profile.Role {
	case riot.RoleCompany:
		params.CompanyID = profile.ID
	case riot.RoleCustomer:
		params.CompanyID = profile.ParentID
	}

	params.MerchantID = identifier.MerchantID

	if err = params.Validate(params.Type); err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.params.Validate")
	}

	uplinePlatformFee := internal.MerchantPlatformFee{}
	uplineFees := []internal.Fee{}

	//find mechant
	filterMerchant := map[string]interface{}{
		"id":         identifier.MerchantID,
		"company_id": params.CompanyID,
		"status":     constant.TypeActive,
	}

	merchant, err := s.marchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.s.repo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.Create.s.repo.GetByFilter")
	}

	s.util.Logger.Info("MerchantPlatformFeeService.Create.Info", zap.Any("merchant", merchant))

	//find platform fee
	filterPlatformFee := map[string]interface{}{
		"company_id": merchant.CompanyID,
		"status":     constant.TypeActive,
	}

	platformFees, err := s.platformFeeRepo.GetAll(ctx, filterPlatformFee)
	if err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.platformFeeRepo.GetByFilter")
	}

	objectType := constant.TypeFeeMerchant

	for _, platformFee := range platformFees {

		s.util.Logger.Info("MerchantPlatformFeeService.Create.Info", zap.Any("platformFee", platformFee))

		//find fee
		fees, err := s.feeRepo.GetAll(ctx, map[string]interface{}{
			"object_id":  platformFee.ID,
			"is_default": true,
			"status":     constant.TypeActive,
		})

		s.util.Logger.Info("MerchantPlatformFeeService.Create.Info", zap.Any("fees", fees))

		paramsFees := []internal.CreateFee{}

		if err != nil {
			return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.feeRepo.GetByFilter")
		}

		if merchant.ReferrerID != "" || merchant.MarketingID != "" {

			if merchant.ReferrerID != "" {

				hasReferralFee = true

				filterUplineMerchant := map[string]interface{}{
					"company_id": params.CompanyID,
					"profile_id": params.ReferrerID,
					"status":     constant.TypeActive,
				}

				uplineMerchant, err := s.marchantRepo.GetByFilter(ctx, filterUplineMerchant)
				if err != nil {
					return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.marchantRepo.GetByFilter")
				}

				s.util.Logger.Info("MerchantPlatformFeeService.Create.Info", zap.Any("uplineMerchant", uplineMerchant))

				//find upline merchant platform fee
				filterUplinePlatformFee := map[string]interface{}{
					"company_id":  uplineMerchant.CompanyID,
					"merchant_id": uplineMerchant.ID,
					"status":      constant.TypeActive,
				}

				uplinePlatformFee, err = s.repo.GetByFilter(ctx, filterUplinePlatformFee)

				if err != nil {
					return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.platformFeeRepo.GetByFilter")
				}

				//find fee
				uplineFees, err = s.feeRepo.GetAll(ctx, map[string]interface{}{
					"object_id": uplinePlatformFee.ID,
					"status":    constant.TypeActive,
				})

				if err != nil {
					return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.feeRepo.GetByFilter")
				}
			}

			if !uplinePlatformFee.IsExist() {
				uplineFees = fees
			}

			s.util.Logger.Info("MerchantPlatformFeeService.Create.Info", zap.Any("uplinePlatformFee", uplinePlatformFee))

			for _, uplineFee := range uplineFees {
				for _, fee := range fees {
					if uplineFee.PaymentMethod != "" && uplineFee.PaymentMethod == fee.PaymentMethod {
						marketingFeeType := ""
						marketingFeeValue := 0.0

						marketingValue := new(big.Float).SetPrec(128).SetFloat64(0.0)

						if uplineFee.MarkeringFeeValue != 0.0 {
							marketingValue = new(big.Float).SetPrec(128).SetFloat64(uplineFee.MarkeringFeeValue)
						}

						if params.MarketingID != "" {
							marketingValue = new(big.Float).SetPrec(128).SetFloat64(config.FeeValue)
							hasMarketingFee = true
						}

						feeValue := new(big.Float).SetPrec(128).SetFloat64(fee.FeeValue)
						uplineFeeBig := new(big.Float).SetPrec(128).SetFloat64(uplineFee.FeeValue)
						plFeeBig := new(big.Float).SetPrec(128).SetFloat64(fee.PlatformFeeValue)

						referralFeeBig := new(big.Float).Sub(feeValue, uplineFeeBig)
						platformFeeBig := new(big.Float).Sub(plFeeBig, feeValue)
						platformFeeBig.Sub(platformFeeBig, marketingValue)
						platformFeeBig.Add(platformFeeBig, uplineFeeBig)

						referralFee, _ := referralFeeBig.Float64()
						platformFeeValue, _ := platformFeeBig.Float64()
						marketingValueFloat, _ := marketingValue.Float64()

						referralFee = math.Round(referralFee*100) / 100
						platformFeeValue = math.Round(platformFeeValue*100) / 100

						s.util.Logger.Info("MerchantPlatformFeeService.Create.uplinePlatformFee (after rounding)",
							zap.Any("feeValue", feeValue),
							zap.Any("uplineFeeBig", uplineFeeBig),
							zap.Any("plFeeBig", plFeeBig),
							zap.Any("referralFeeBig", referralFeeBig),
							zap.Any("platformFeeBig", platformFeeBig),
							zap.Any("referralFee", referralFee),
							zap.Any("platformFeeValue", platformFeeValue),
							zap.Any("marketingValueFloat", marketingValueFloat),
							zap.Any("marketingValue", marketingValue),
						)

						paramFee := internal.CreateFee{
							CompanyID:         params.CompanyID,
							ProfileID:         params.ProfileID,
							PaymentMethod:     &fee.PaymentMethod,
							FeeType:           &fee.FeeType,
							FeeValue:          &fee.FeeValue,
							PGFeeType:         &fee.PGFeeType,
							PGFeeValue:        &fee.PGFeeValue,
							PlatformFeeType:   &fee.FeeType,
							PlatformFeeValue:  &platformFeeValue,
							ReferralFeeType:   &fee.PlatformFeeType,
							ReferralFeeValue:  &referralFee,
							MarkeringFeeType:  &marketingFeeType,
							MarkeringFeeValue: &marketingFeeValue,
							ObjectType:        &fee.ObjectType,
							Currency:          &fee.Currency,
							Status:            constant.StatusActive,
						}

						if marketingValue.Cmp(big.NewFloat(0.0)) > 0 {
							paramFee.MarkeringFeeType = &fee.PlatformFeeType
							paramFee.MarkeringFeeValue = &marketingValueFloat
						}

						paramsFees = append(paramsFees, paramFee)

					}

				}
			}

		}

		if merchant.ReferrerID == "" && merchant.MarketingID == "" {
			for _, fee := range fees {
				if fee.PaymentMethod != "" && fee.PaymentMethod == constant.PaymentMethodQRCode {
					paramsFees = append(paramsFees, internal.CreateFee{
						CompanyID:        params.CompanyID,
						ProfileID:        params.ProfileID,
						PaymentMethod:    &fee.PaymentMethod,
						FeeType:          &fee.FeeType,
						FeeValue:         &fee.FeeValue,
						PGFeeType:        &fee.PGFeeType,
						PGFeeValue:       &fee.PGFeeValue,
						PlatformFeeType:  &fee.PlatformFeeType,
						PlatformFeeValue: &fee.PlatformFeeValue,
						ObjectType:       &objectType,
						Currency:         &fee.Currency,
						Status:           constant.StatusActive,
					})
				}
			}
		}

		//create merchant platform fee
		paramsMerchantPlatformFee := internal.CreateMerchantPlatformFee{
			CompanyID:              merchant.CompanyID,
			ProfileID:              merchant.ProfileID,
			MerchantID:             merchant.ID,
			ReferrerID:             merchant.ReferrerID,
			MarketingID:            merchant.MarketingID,
			ProductType:            platformFee.ProductType,
			Provider:               &platformFee.Provider,
			HasMarketingFee:        &hasMarketingFee,
			HasReferralFee:         &hasReferralFee,
			HasCashbackProviderFee: &hasCbProviderFee,
			Status:                 constant.StatusActive,
		}

		mp, err := s.repo.Create(ctx, paramsMerchantPlatformFee)

		if err != nil {
			return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.s.repo.Create")
		}

		//create fee

		for _, param := range paramsFees {
			objectType := constant.TypeFeeMerchant
			param.ObjectID = mp.ID
			param.ObjectType = &objectType

			s.util.Logger.Info("MerchantPlatformFeeService.Create.Info", zap.Any("param", param))
			_, err = s.feeRepo.Create(ctx, param)
			if err != nil {
				return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.s.feeRepo.Create")
			}
		}
	}

	//find all merchant platform fee
	filter := map[string]interface{}{
		"merchant_id": merchant.ID,
		"status":      constant.TypeActive,
	}

	res, err := s.repo.GetAll(ctx, filter)
	if err != nil {
		return []internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.Create.s.repo.GetAll")
	}

	return res, nil
}

func (s *MerchantPlatformFeeService) Delete(ctx context.Context, identifier internal.Identifier, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *MerchantPlatformFeeService) Update(ctx context.Context, identifier internal.Identifier, id string, params internal.CreateMerchantPlatformFee, nexusData riot.Nexus) (_ internal.MerchantPlatformFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeService", "Update"))

	if !s.cb.Ready() {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.s.repo.Update")
	}

	return res, nil
}

func (s *MerchantPlatformFeeService) UpdateByMerchantID(ctx context.Context, identifier internal.Identifier, params internal.UpdateMerchantPlatformFee, nexusData riot.Nexus) (_ internal.MerchantPlatformFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeService", "Update"))

	if !s.cb.Ready() {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         identifier.MerchantID,
		"profile_id": profile.ID,
		"status":     constant.TypeActive,
	}

	merchant, err := s.marchantRepo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.s.marchantRepo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.s.merchant.ValidateNotExist")
	}

	filterMerchantPlatformFee := map[string]interface{}{
		"merchant_id": identifier.MerchantID,
		"status":      constant.TypeActive,
	}

	resMerchantPlatformFee, err := s.repo.GetByFilter(ctx, filterMerchantPlatformFee)

	if err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.s.repo.GetByFilter")
	}

	if err := resMerchantPlatformFee.ValidateNotExist(); err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.s.repo.GetByFilter")
	}

	if err := params.Validate(); err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.params.Validate")
	}

	for _, fee := range params.Fees {
		if err := fee.ValidateUpdateFee(); err != nil {
			return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.fee.ValidateUpdateFee")
		}
	}

	// if has referral fee or is child of referral fee
	if resMerchantPlatformFee.HasReferralFee {

		filterMerchantPlatformFee := map[string]interface{}{
			"merchant_id": resMerchantPlatformFee.ReferrerID,
			"status":      constant.TypeActive,
		}

		resMerchantReffPlatformFee, err := s.repo.GetByFilter(ctx, filterMerchantPlatformFee)

		if err != nil {
			return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.resMerchantReffPlatformFee.repo.GetByFilter")
		}

		if err := resMerchantReffPlatformFee.ValidateNotExist(); err != nil {
			return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.resMerchantReffPlatformFee.ValidateNotExist")
		}

	}

	return internal.MerchantPlatformFee{}, nil
}

func (s *MerchantPlatformFeeService) GetByID(ctx context.Context, identifier internal.Identifier, id string, includes []string, nexusData riot.Nexus) (_ internal.MerchantPlatformFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeService", "GetByID"))

	if !s.cb.Ready() {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.MerchantPlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.s.repo.GetByID")
	}

	return res, nil
}

func (s *MerchantPlatformFeeService) GetAllWithPagination(ctx context.Context, identifier internal.Identifier, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantPlatformFeePagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformFeeService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.MerchantPlatformFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	if err := identifier.ValidateMarchantID(); err != nil {
		return internal.MerchantPlatformFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformFeeService.identifier.Validate")
	}

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.MerchantPlatformFeePagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.MerchantPlatformFeePagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if identifier.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(identifier.MerchantID)
		if err != nil {
			return internal.MerchantPlatformFeePagin{}, err
		}
		filterMap["merchant_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.MerchantPlatformFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformFeeService.repo.GetAllWithPagination")
	}

	return res, nil
}
