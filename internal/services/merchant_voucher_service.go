package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type MerchantVoucherRepository interface {
	Create(ctx context.Context, params internal.CreateMerchantVoucher) (internal.MerchantVoucher, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateMerchantVoucher) (internal.MerchantVoucher, error)
	GetByID(ctx context.Context, id string) (internal.MerchantVoucher, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantVoucher, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantVoucherPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantVoucher, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantVoucher) (internal.MerchantVoucher, error)
}

type MerchantVoucherService struct {
	repo         MerchantVoucherRepository
	marchantRepo MerchantRepository
	voucherRepo  VoucherRepository
	feeRepo      FeeRepository
	cb           *circuitbreaker.CircuitBreaker
	util         riot.Util
}

func NewMerchantVoucherService(util riot.Util, repo MerchantVoucherRepository, marchantRepo MerchantRepository, voucherRepo VoucherRepository, feeRepo FeeRepository) *MerchantVoucherService {
	return &MerchantVoucherService{
		repo:         repo,
		marchantRepo: marchantRepo,
		voucherRepo:  voucherRepo,
		feeRepo:      feeRepo,
		util:         util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *MerchantVoucherService) Create(ctx context.Context, identifier internal.Identifier, params internal.CreateMerchantVoucher, nexusData riot.Nexus) (_ internal.MerchantVoucher, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherService", "Create"))

	if !s.cb.Ready() {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	remainingUsage := 0

	if profile.ID != "" {
		switch profile.Role {
		case riot.RoleCompany:
			params.CompanyID = profile.ID
		case riot.RoleCustomer:
			params.CompanyID = profile.ParentID
		}
	} else if webservice.ID != "" {
		params.CompanyID = webservice.ID
	}

	if err := identifier.ValidateMarchantID(); err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.Create.identifier.Validate")
	}

	if err := params.Validate(); err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.Create.params.Validate")
	}

	if err := params.ValidateExpireType(); err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.Create.params.ValidateExpireType")
	}

	//find marchant
	filterMarchant := map[string]interface{}{
		"id":         identifier.MerchantID,
		"profile_id": profile.ID,
	}

	resMarchant, err := s.marchantRepo.GetByFilter(ctx, filterMarchant)

	if err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.Create.marchantRepo.GetByFilter")
	}

	if err := resMarchant.ValidateNotExist(); err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.Create.resMarchant.ValidateNotExist")
	}

	//find voucher
	filterVoucher := map[string]interface{}{
		"company_id": profile.ParentID,
	}

	if params.VoucherID != "" {
		filterVoucher["id"] = params.VoucherID
	} else if *params.Code != "" {
		filterVoucher["code"] = params.Code
	}

	resVoucher, err := s.voucherRepo.GetByFilter(ctx, filterVoucher)

	if err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.Create.voucherRepo.GetByFilter")
	}

	if err := resVoucher.ValidateNotExist(); err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.Create.resVoucher.ValidateNotExist")
	}

	if resVoucher.EnableLimit {

		if err := resVoucher.ValidateUsageRemaining(); err != nil {
			return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.Create.resVoucher.ValidateUsageRemaining")
		}

		remainingUsage = resVoucher.RemainingUsage - 1
	}

	switch params.ExpireType {
	case "dynamic_period":
		now := time.Now()

		switch *params.PeriodType {
		case "day":
			period := time.Duration(*params.Period) * time.Hour * 24
			expiresAt := now.Add(period)

			expiresAtStr := expiresAt.Format(pkg.DateLayoutDefault)
			params.ExpiresAt = expiresAtStr

		case "week":
			period := time.Duration(*params.Period) * time.Hour * 24 * 7
			expiresAt := now.Add(period)
			expiresAtStr := expiresAt.Format(pkg.DateLayoutDefault)
			params.ExpiresAt = expiresAtStr

		case "month":
			period := time.Duration(*params.Period) * time.Hour * 24 * 30
			expiresAt := now.Add(period)
			expiresAtStr := expiresAt.Format(pkg.DateLayoutDefault)
			params.ExpiresAt = expiresAtStr

		case "year":
			period := time.Duration(*params.Period) * time.Hour * 24 * 365
			expiresAt := now.Add(period)
			expiresAtStr := expiresAt.Format(pkg.DateLayoutDefault)
			params.ExpiresAt = expiresAtStr
		}
	}

	params.MerchantID = resMarchant.ID
	params.ProfileID = resMarchant.ProfileID
	params.VoucherID = resVoucher.ID
	params.CompanyID = resVoucher.CompanyID
	params.DiscountType = &resVoucher.DiscountType
	params.DiscountValue = &resVoucher.DiscountValue
	params.Code = &resVoucher.Code
	params.ExpireType = resVoucher.ExpireType
	params.CombinationTypes = &resVoucher.CombinationTypes
	params.ProductType = resVoucher.ProductType
	params.MinPurchase = &resVoucher.MinPurchase
	params.LevelPriority = &resVoucher.LevelPriority
	params.MaxDiscount = &resVoucher.MaxDiscount
	params.Status = constant.StatusActive
	params.Period = &resVoucher.Period
	params.PeriodType = &resVoucher.PeriodType
	params.ValidFrom = func() string {
		if resVoucher.ValidFrom != nil {
			return resVoucher.ValidFrom.Format(pkg.DateLayoutDefault)
		}
		return ""
	}()
	params.ValidTo = func() string {

		if resVoucher.ValidTo != nil {
			return resVoucher.ValidTo.Format(pkg.DateLayoutDefault)
		}

		return ""
	}()
	params.ExpiresAt = func() string {
		if resVoucher.ExpiresAt != nil {
			return resVoucher.ExpiresAt.Format(pkg.DateLayoutDefault)
		}

		return ""
	}()

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.s.repo.Create")
	}

	if resVoucher.DiscountType == "custom" {
		filterFees := map[string]interface{}{
			"object_id": resVoucher.ID,
			"status":    constant.TypeActive,
		}

		fees, err := s.feeRepo.GetAll(ctx, filterFees)

		if err != nil {
			return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.s.feeRepo.GetByFilter")
		}

		for _, fee := range fees {

			paramsFee := internal.CreateFee{
				ProfileID:        res.ProfileID,
				CompanyID:        res.CompanyID,
				ObjectID:         res.ID,
				ObjectType:       &fee.ObjectType,
				FeeType:          &fee.FeeType,
				FeeValue:         &fee.FeeValue,
				PGFeeType:        &fee.PGFeeType,
				PGFeeValue:       &fee.PGFeeValue,
				PlatformFeeType:  &fee.PlatformFeeType,
				PlatformFeeValue: &fee.PlatformFeeValue,
				PaymentMethod:    &fee.PaymentMethod,
				Status:           fee.Status,
			}

			_, err := s.feeRepo.Create(ctx, paramsFee)

			if err != nil {
				return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.s.feeRepo.Update")
			}
		}
	}

	if resVoucher.EnableLimit {
		//update usage limit
		paramsVoucher := internal.CreateVoucher{
			RemainingUsage: &remainingUsage,
		}

		_, err = s.voucherRepo.Update(ctx, resVoucher.ID, paramsVoucher)
		if err != nil {
			return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.s.voucherRepo.Update")
		}
	}

	return res, nil
}

func (s *MerchantVoucherService) Delete(ctx context.Context, identifier internal.Identifier, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *MerchantVoucherService) Update(ctx context.Context, identifier internal.Identifier, id string, params internal.CreateMerchantVoucher, nexusData riot.Nexus) (_ internal.MerchantVoucher, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherService", "Update"))

	if !s.cb.Ready() {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.s.repo.Update")
	}

	return res, nil
}

func (s *MerchantVoucherService) GetByID(ctx context.Context, identifier internal.Identifier, id string, includes []string, nexusData riot.Nexus) (_ internal.MerchantVoucher, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherService", "GetByID"))

	if !s.cb.Ready() {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.MerchantVoucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.s.repo.GetByID")
	}

	return res, nil
}

func (s *MerchantVoucherService) GetAllWithPagination(ctx context.Context, identifier internal.Identifier, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantVoucherPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantVoucherService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.MerchantVoucherPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	if err := identifier.ValidateMarchantID(); err != nil {
		return internal.MerchantVoucherPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantVoucherService.identifier.Validate")
	}

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.MerchantVoucherPagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.MerchantVoucherPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if identifier.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(identifier.MerchantID)
		if err != nil {
			return internal.MerchantVoucherPagin{}, err
		}
		filterMap["merchant_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.MerchantVoucherPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantVoucherService.repo.GetAllWithPagination")
	}

	return res, nil
}
