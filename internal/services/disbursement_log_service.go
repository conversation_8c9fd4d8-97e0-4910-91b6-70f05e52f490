package services

import (
	"context"
	"encoding/json"
	"net/url"
	"strconv"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type DisbursementLogRepository interface {
	Create(ctx context.Context, params internal.CreateDisbursementLog) (internal.DisbursementLog, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateDisbursementLog) (internal.DisbursementLog, error)
	GetByID(ctx context.Context, id string) (internal.DisbursementLog, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.DisbursementLog, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.DisbursementLogPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.DisbursementLog, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateDisbursementLog) (internal.DisbursementLog, error)
}

type DisbursemenLogtService struct {
	repo          DisbursementLogRepository
	taskPublisher *rabbitmq.Publisher
	cb            *circuitbreaker.CircuitBreaker
	util          riot.Util
}

func NewDisbursemenLogtService(util riot.Util, repo DisbursementLogRepository, taskPublisher *rabbitmq.Publisher) *DisbursemenLogtService {
	return &DisbursemenLogtService{
		repo:          repo,
		taskPublisher: taskPublisher,
		util:          util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *DisbursemenLogtService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.DisbursementLogPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursemenLogtService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.DisbursementLogPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	var query *internal.QueryDisbursement

	disbursementID := urlValues.Get("disbursement_id")

	if disbursementID == "" {
		return internal.DisbursementLogPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "disbursement_id is required")
	}

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))
	queryQry, _ := riot.QueryByPrefix(&urlValues, "query")

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.DisbursementLogPagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.DisbursementLogPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if disbursementID != "" {
		objectID, err := primitive.ObjectIDFromHex(disbursementID)
		if err != nil {
			return internal.DisbursementLogPagin{}, err
		}
		filterMap["disbursement_id"] = objectID
	}

	s.util.Logger.Info("filter map",
		zap.Any("filterMap", filterMap),
		zap.Any("paginationQry", paginationQry),
		zap.Any("sortQry", sortQry),
		zap.Any("dateRangeQry", dateRangeQry),
		zap.Any("statusQry", statusQry),
		zap.Any("queryQry", queryQry),
	)

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	if err := json.Unmarshal([]byte(queryQry), &query); err != nil {
		return internal.DisbursementLogPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursemenLogtService.GetAllWithPagination.json.Unmarshal")
	}

	if query != nil {

		if query.DisbursementNo != "" {
			builder.SearchANDFields["disbursement_no"] = query.DisbursementNo
		}

		if query.AccountHolderName != "" {
			builder.SearchANDFields["account_holder_name"] = query.AccountHolderName
		}

		if query.AccountNumber != "" {
			builder.SearchANDFields["account_number"] = query.AccountNumber
		}

		if query.Provider != "" {
			builder.SearchANDFields["provider"] = query.Provider
		}

		if query.BankCode != "" {
			builder.SearchANDFields["bank_code"] = query.BankCode
		}

		if query.Amount != "" {
			//convert string to float64
			amount, err := strconv.ParseFloat(query.Amount, 64)
			if err != nil {
				return internal.DisbursementLogPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursemenLogtService.GetAllWithPagination.ParseFloat")
			}
			builder.SearchANDFields["amount"] = amount
		}

	}

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.DisbursementLogPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenLogtService.repo.GetAllWithPagination")
	}

	return res, nil
}
