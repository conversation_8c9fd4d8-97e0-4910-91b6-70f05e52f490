package services

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type CoinTransactionRepository interface {
	Create(ctx context.Context, params internal.CreateCoinTransaction) (internal.CoinTransaction, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateCoinTransaction) (internal.CoinTransaction, error)
	GetByID(ctx context.Context, id string) (internal.CoinTransaction, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.CoinTransaction, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.CoinTransactionPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.CoinTransaction, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateCoinTransaction) (internal.CoinTransaction, error)
}

type CoinTransactionService struct {
	repo     CoinTransactionRepository
	coinRepo CoinRepository
	txnRepo  TransactionRepository
	cb       *circuitbreaker.CircuitBreaker
	util     riot.Util
}

func NewCoinTransactionService(util riot.Util, repo CoinTransactionRepository, coinRepo CoinRepository, txnRepo TransactionRepository) *CoinTransactionService {
	return &CoinTransactionService{
		repo:     repo,
		coinRepo: coinRepo,
		txnRepo:  txnRepo,
		util:     util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *CoinTransactionService) Create(ctx context.Context, params []internal.CreateCoinTransaction, nexusData riot.Nexus) (_ []internal.CoinTransaction, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionService", "Create"))

	results := []internal.CoinTransaction{}

	if !s.cb.Ready() {
		return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	companyID := ""

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	if profile.ID != "" {
		switch profile.Role {
		case riot.RoleCompany:
			companyID = profile.ID
		case riot.RoleCustomer:
			companyID = profile.ParentID
		}
	} else if webservice.ID != "" {
		companyID = webservice.ID
	}

	mapTotal := make(map[string]float64)
	for _, param := range params {
		if param.Amount == nil || param.Type == nil {
			continue
		}
		if *param.Type == constant.TypeTxnRoundBook {
			key := fmt.Sprintf("%s:%d", param.UserID, *param.Type)
			mapTotal[key] += pkg.ToNegativeFloat(*param.Amount)
		}
	}

	for _, param := range params {

		if err := param.Validate(); err != nil {
			return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionService.Create.params.Validate")
		}

		stateType := *param.Type

		//filter coin
		filterCoin := map[string]interface{}{
			"profile_id": param.UserID,
			"company_id": companyID,
			"status":     constant.TypeActive,
		}

		s.util.Logger.Info("filterCoin", zap.Any("filterCoin", filterCoin))

		coin, err := s.coinRepo.GetByFilter(ctx, filterCoin)
		if err != nil {
			return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionService.coinRepo.GetByFilter")
		}

		if err := coin.ValidateUserNotExist(); err != nil {
			return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionService.ValidateUserNotExist")
		}

		switch stateType {

		case constant.TypeTxnRoundBook:

			key := fmt.Sprintf("%s:%d", param.UserID, stateType)
			totalAmount := mapTotal[key]
			balance := coin.Balance + totalAmount

			if err := coin.ValidateBalance(balance); err != nil {
				return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionService.ValidateBalance")
			}

		case constant.TypeTxnRoundComplete:

			if err := param.ValidateRoundCompleted(); err != nil {
				return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionService.Create.params.ValidateRoundCompleted")
			}

			//filter coin
			filterCoin := map[string]interface{}{
				"profile_id": *param.SourceID,
				"company_id": companyID,
				"status":     constant.TypeActive,
			}

			coin, err := s.coinRepo.GetByFilter(ctx, filterCoin)
			if err != nil {
				return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionService.TypeTxnRoundComplete.coinRepo.GetByFilter")
			}

			if err := coin.ValidateSourceNotExist(); err != nil {
				return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionService.ValidateSourceNotExist")
			}

		case constant.TypeTxnBuyCoin:

			filterTxn := map[string]interface{}{
				"id":     *param.TypeID,
				"status": constant.TypeTransactionCompleted,
			}

			s.util.Logger.Info("filterTxn", zap.Any("filterTxn", filterTxn))

			txn, err := s.txnRepo.GetByFilter(ctx, filterTxn)

			if err != nil {
				return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionService.repo.GetByFilter")
			}

			if err := txn.ValidateNotExist(); err != nil {
				return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionService.ValidateNotExist")
			}
		}
	}

	for _, param := range params {

		stateType := *param.Type

		switch stateType {
		case constant.TypeTxnRoundBook:

			param.SourceID = &param.UserID
			amount := pkg.ToNegativeFloat(*param.Amount)
			param.Amount = &amount

		case constant.TypeTxnRoundCancelByPlayer, constant.TypeTxnRoundCancelByStreamer:

			param.SourceID = &param.UserID

		case constant.TypeTxnBuyCoin, constant.TypeTxnCancelBySystem:

			param.SourceID = nil

		case constant.TypeTxnDisburseCoin:

			//make negative amount
			value := pkg.ToNegativeFloat(*param.Amount)
			param.Amount = &value

		}

		filterCoin := map[string]interface{}{
			"profile_id": param.UserID,
			"company_id": companyID,
			"status":     constant.TypeActive,
		}

		coin, err := s.coinRepo.GetByFilter(ctx, filterCoin)
		if err != nil {
			return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionService.TypeTxnDisburseCoin.coinRepo.GetByFilter")
		}

		if err := coin.ValidateUserNotExist(); err != nil {
			return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinTransactionService.ValidateUserNotExist")
		}

		//filter TypetxnCoin
		filterTypeTxnCoin := map[string]interface{}{
			"profile_id": param.UserID,
			"type":       *param.Type,
			"type_id":    *param.TypeID,
		}

		if param.SourceID != nil && *param.SourceID != "" {
			filterTypeTxnCoin["source_id"] = *param.SourceID
		}

		txnCoin, err := s.repo.GetByFilter(ctx, filterTypeTxnCoin)

		if err != nil {
			return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionService.s.repo.GetByFilter")
		}

		if txnCoin.IsExist() {
			results = append(results, txnCoin)
			continue
		}

		param.CoinID = coin.ID
		param.CompanyID = companyID
		param.ProfileID = param.UserID
		param.Status = constant.StatusActive
		param.Balance = param.Amount
		param.BalanceBefore = &coin.Balance
		description := constant.ReverseTxnConstant(*param.Type)
		param.Description = &description

		res, err := s.repo.Create(ctx, param)
		if err != nil {
			return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionService.s.repo.Create")
		}

		//update coin
		balance := coin.Balance + *param.Amount

		updateParams := internal.CreateCoin{
			Balance: &balance,
		}

		coin, err = s.coinRepo.UpdateByFilter(ctx, filterCoin, updateParams)
		if err != nil {
			return []internal.CoinTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionService.coinRepo.UpdateByFilter")
		}

		results = append(results, res)
	}

	return results, nil
}

func (s *CoinTransactionService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.CoinTransactionPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinTransactionService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.CoinTransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.CoinTransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinTransactionService.repo.GetAllWithPagination")
	}

	return res, nil
}
