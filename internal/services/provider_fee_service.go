package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type ProviderFeeRepository interface {
	Create(ctx context.Context, params internal.CreateProviderFee) (internal.ProviderFee, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateProviderFee) (internal.ProviderFee, error)
	GetByID(ctx context.Context, id string) (internal.ProviderFee, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.ProviderFee, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ProviderFeePagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.ProviderFee, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateProviderFee) (internal.ProviderFee, error)
}

type ProviderFeeService struct {
	repo ProviderFeeRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewProviderFeeService(util riot.Util, repo ProviderFeeRepository) *ProviderFeeService {
	return &ProviderFeeService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *ProviderFeeService) Create(ctx context.Context, params internal.CreateProviderFee, nexusData riot.Nexus) (_ internal.ProviderFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeService", "Create"))

	if !s.cb.Ready() {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.CompanyID = profile.ParentID
	params.ProfileID = profile.ID
	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderFeeService.params.Validate")
	}

	filter := map[string]interface{}{
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateRegistered(); err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderFeeService.res.ValidateRegistered")
	}

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeService.s.repo.Create")
	}

	return res, nil
}

func (s *ProviderFeeService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderFeeService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *ProviderFeeService) Update(ctx context.Context, id string, params internal.CreateProviderFee, nexusData riot.Nexus) (_ internal.ProviderFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeService", "Update"))

	if !s.cb.Ready() {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderFeeService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeService.s.repo.Update")
	}

	return res, nil
}

func (s *ProviderFeeService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.ProviderFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeService", "GetByID"))

	if !s.cb.Ready() {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.ProviderFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderFeeService.s.repo.GetByID")
	}

	return res, nil
}

func (s *ProviderFeeService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.ProviderFeePagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderFeeService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.ProviderFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	groupID := urlValues.Get("group_id")

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.ProviderFeePagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.ProviderFeePagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if groupID != "" {
		objectID, err := primitive.ObjectIDFromHex(groupID)
		if err != nil {
			return internal.ProviderFeePagin{}, err
		}
		filterMap["group_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.ProviderFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderFeeService.repo.GetAllWithPagination")
	}

	return res, nil
}
