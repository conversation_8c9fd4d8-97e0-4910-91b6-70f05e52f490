package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type CoinRepository interface {
	Create(ctx context.Context, params internal.CreateCoin) (internal.Coin, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateCoin) (internal.Coin, error)
	GetByID(ctx context.Context, id string) (internal.Coin, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Coin, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.CoinPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Coin, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateCoin) (internal.Coin, error)
}

type CoinService struct {
	repo                 CoinRepository
	configRepo           ConfigurationRepository
	merchantRepo         MerchantRepository
	coinDisbRepo         CoinDisbursementRepository
	merchantPlatformRepo MerchantPlatformRepository
	taskPublisher        *rabbitmq.Publisher
	cb                   *circuitbreaker.CircuitBreaker
	util                 riot.Util
}

func NewCoinService(util riot.Util, repo CoinRepository, configRepo ConfigurationRepository, merchantRepo MerchantRepository, coinDisbRepo CoinDisbursementRepository, merchantPlatformRepo MerchantPlatformRepository, publisher *rabbitmq.Publisher) *CoinService {
	return &CoinService{
		repo:                 repo,
		configRepo:           configRepo,
		merchantRepo:         merchantRepo,
		coinDisbRepo:         coinDisbRepo,
		merchantPlatformRepo: merchantPlatformRepo,
		taskPublisher:        publisher,
		util:                 util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *CoinService) Create(ctx context.Context, params internal.CreateCoin, nexusData riot.Nexus) (_ internal.Coin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinService", "Create"))

	if !s.cb.Ready() {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.Status = constant.StatusActive
	params.ProfileID = profile.ID
	params.CompanyID = profile.ParentID

	if err = params.Validate(); err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.params.Validate")
	}

	filter := map[string]interface{}{
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.GetByFilter")
	}

	if err := res.ValidateRegistered(); err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.res.ValidateRegistered")
	}

	balance := float64(0)

	params.Balance = &balance

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.Create")
	}

	return res, nil
}

func (s *CoinService) Disburse(ctx context.Context, params internal.CreateCoinDisburse, nexusData riot.Nexus) (_ internal.CoinDisburse, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinService", "Create"))

	if !s.cb.Ready() {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.Status = constant.StatusActive
	params.ProfileID = profile.ID
	params.CompanyID = profile.ParentID

	if err = params.Validate(); err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.params.Validate")
	}

	filterMerchantPlatform := map[string]interface{}{
		"company_id": params.CompanyID,
		"status":     constant.TypeActive,
	}

	mapMerchantPlatform := map[string]internal.MerchantPlatform{}
	merchantPlatforms, err := s.merchantPlatformRepo.GetAll(ctx, filterMerchantPlatform)

	if err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.merchantPlatformRepo.GetByFilter", zap.Error(err))
	}

	for _, merchantPlatform := range merchantPlatforms {
		mapMerchantPlatform[merchantPlatform.Name] = merchantPlatform
	}

	//filter coin
	filterCoin := map[string]interface{}{
		"profile_id": profile.ID,
		"status":     constant.TypeActive,
	}

	resCoin, err := s.repo.GetByFilter(ctx, filterCoin)
	if err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.GetByFilter")
	}

	if err := resCoin.ValidateNotExist(); err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.resCoin.ValidateNotExist")
	}

	if err := resCoin.ValidateDisburseBalance(params.Amount); err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.resCoin.ValidateDisburseBalance")
	}

	filterConfig := map[string]interface{}{
		"name":   constant.TypeFeatureCoin,
		"status": constant.TypeActive,
	}

	resConfig, err := s.configRepo.GetByFilter(ctx, filterConfig)
	if err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.configRepo.GetByFilter")
	}

	if err := resConfig.ValidateNotExist(); err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.resConfig.ValidateNotExist")
	}

	filterMerchant := map[string]interface{}{
		"profile_id": profile.ID,
		"status":     constant.TypeActive,
	}

	resMerchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.merchantRepo.GetByFilter")
	}

	if err := resMerchant.ValidateNotExist(); err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.resMerchant.ValidateNotExist")
	}

	amount := float64(0)
	priceCoin := float64(0)

	if resConfig.FeeType == constant.FeeTypePercentage {
		priceCoin = (params.Amount * resConfig.FeeValue) / 100
	} else {
		priceCoin = resConfig.FeeValue
	}

	amount = params.Amount * priceCoin

	//create coin disburse

	paramDisburse := internal.CreateCoinDisbursement{
		CompanyID:   profile.ParentID,
		ProfileID:   profile.ID,
		CoinID:      resCoin.ID,
		Amount:      &priceCoin,
		Description: &params.Description,
		Metadata:    &params.Metadata,
		Status:      constant.StatusActive,
	}

	coinDisb, err := s.coinDisbRepo.Create(ctx, paramDisburse)

	if err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.coinDisbRepo.Create")
	}

	currentBalance := resCoin.Balance - params.Amount

	//add merchant balance
	payloadTxnBalance := internal.PayloadTxnBalance{
		CompanyID:  resMerchant.CompanyID,
		ProfileID:  resMerchant.ProfileID,
		TypeID:     coinDisb.CoinID,
		TypeObject: constant.TypeObjectDisbCoin,
		Type:       constant.TypeTxnDisburseCoinCompleted,
		Description: constant.ReverseTxnConstant(
			constant.TypeTxnDisburseCoinCompleted),
		SourceID:    resCoin.ID,
		MerchantID:  resMerchant.ID,
		ProductType: constant.TypeFeatureCoin,
		Amount:      amount,
	}

	if err := s.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.taskPublisher.PublishTxnBalance")
	}

	//sub mercenary balance
	payloadTxnBalance = internal.PayloadTxnBalance{
		CompanyID:  resMerchant.CompanyID,
		ProfileID:  mapMerchantPlatform["MERCENARY"].ProfileID,
		TypeID:     coinDisb.ID,
		TypeObject: constant.TypeObjectDisbCoin,
		Type:       constant.TypeTxnDisburseCoin,
		SourceID:   resCoin.ID,
		Description: constant.ReverseTxnConstant(
			constant.TypeTxnDisburseCoin),
		MerchantID:  mapMerchantPlatform["MERCENARY"].MerchantID,
		ProductType: constant.TypeFeatureCoin,
		Amount:      amount,
	}

	if err := s.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.taskPublisher.PublishTxnBalance")
	}

	payloadTxnCoin := internal.PayloadCoinBalance{
		CoinID:      coinDisb.CoinID,
		CompanyID:   coinDisb.CompanyID,
		ProfileID:   coinDisb.ProfileID,
		UserID:      coinDisb.ProfileID,
		Type:        constant.TypeTxnDisburseCoin,
		TypeID:      coinDisb.ID,
		SourceID:    coinDisb.ID,
		Description: constant.ReverseTxnConstant(constant.TypeTxnDisburseCoin),
		Amount:      params.Amount,
	}

	if err := s.taskPublisher.PublishCoinBalance(ctx, payloadTxnCoin); err != nil {
		return internal.CoinDisburse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.taskPublisher.PublishCoinBalance")
	}

	return internal.CoinDisburse{
		ProfileID:      resCoin.ProfileID,
		CompanyID:      resCoin.CompanyID,
		CurrentBalance: &currentBalance,
		Status:         constant.StatusTransactionCompleted,
	}, nil
}

func (s *CoinService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterCoin := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterCoin)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterCoin)
}

func (s *CoinService) Update(ctx context.Context, id string, params internal.CreateCoin, nexusData riot.Nexus) (_ internal.Coin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinService", "Update"))

	if !s.cb.Ready() {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterCoin := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterCoin)

	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterCoin, params)
	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.Update")
	}

	return res, nil
}

func (s *CoinService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Coin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterCoin := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterCoin)
	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.s.repo.GetByID")
	}

	return res, nil
}

func (s *CoinService) GetByProfileID(ctx context.Context, profileId string, nexusData riot.Nexus) (_ internal.Coin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinService", "GetByProfileID"))

	if !s.cb.Ready() {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	filterCoin := map[string]interface{}{
		"profile_id": profileId,
	}

	res, err := s.repo.GetByFilter(ctx, filterCoin)
	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.s.repo.GetByID")
	}

	return res, nil
}

func (s *CoinService) GetBySession(ctx context.Context, nexusData riot.Nexus) (_ internal.Coin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinService", "GetByProfileID"))

	if !s.cb.Ready() {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterCoin := map[string]interface{}{
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterCoin)
	if err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Coin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinService.s.repo.GetByID")
	}

	return res, nil
}

func (s *CoinService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.CoinPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.CoinPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.CoinPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinService.repo.GetAllWithPagination")
	}

	return res, nil
}
