package services

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/riot"

	"github.com/continue-team/riot/pkg/logger"
	"go.uber.org/zap"
)

type WebhookPayload struct {
	Event      string `json:"event"`
	BusinessID string `json:"business_id"`
	Created    string `json:"created"`
	Data       any    `json:"data"`
}

type WebhookService struct {
	util          riot.Util
	taskPublisher *rabbitmq.Publisher
}

func NewWebhookService(util riot.Util, taskPublisher *rabbitmq.Publisher) *WebhookService {
	return &WebhookService{
		util:          util,
		taskPublisher: taskPublisher,
	}
}

// Create processes the webhook request
func (s *WebhookService) HandleWebhook(ctx context.Context, params []byte, provider, webhookType string) (bool, error) {
	// Define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebhookService", "Create"))

	// Check if params is empty
	if len(params) == 0 {
		s.util.Logger.Warn("Empty payload received")
		return false, errors.New("empty payload")
	}

	switch webhookType {
	case "qrcode":
		return s.HandleWebhookQrCode(ctx, params, provider)
	case "disburse":
		return s.HandleWebhookDisburse(ctx, params, provider)
	case "ewallet":
		return s.HandleWebhookEWallet(ctx, params, provider)
	}

	return true, nil
}

func (s *WebhookService) HandleWebhookQrCode(ctx context.Context, params []byte, provider string) (bool, error) {
	// Define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebhookService", "Create"))

	switch provider {
	case "xendit":
		var payload internal.PayloadWebhookQRPayment
		err := json.Unmarshal(params, &payload)
		if err != nil {
			return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebhookService.json.Unmarshal")
		}

		payload.Provider = "xendit"

		//publish to queue
		err = s.taskPublisher.PublishQrPayment(ctx, payload)
		if err != nil {
			return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebhookService.Publish")
		}

		s.util.Logger.Info("WebhookService.HandleWebhookQrCOde.Xendit", zap.Any("Payload", payload))
	}

	return true, nil
}

func (s *WebhookService) HandleWebhookEWallet(ctx context.Context, params []byte, provider string) (bool, error) {
	// Define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebhookService", "Create"))

	switch provider {
	case "xendit":
		var payload internal.PayloadWebhookEWalletPayment
		err := json.Unmarshal(params, &payload)
		if err != nil {
			return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebhookService.json.Unmarshal")
		}

		payload.Provider = "xendit"

		s.util.Logger.Info("WebhookService.HandleWebhookEWallet.Xendit", zap.Any("Payload", payload))

		//publish to queue
		err = s.taskPublisher.PublishEWalletPayment(ctx, payload)
		if err != nil {
			return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebhookService.Publish")
		}

	}

	return true, nil
}

func (s *WebhookService) HandleWebhookDisburse(ctx context.Context, params []byte, provider string) (bool, error) {
	// Define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebhookService", "HandleWebhookDisburse"))

	switch provider {
	case "xendit":
		var webhook internal.WebhookDisbursement
		err := json.Unmarshal(params, &webhook)
		if err != nil {
			return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "HandleWebhookDisburse.json.Unmarshal")
		}

		payload := internal.PayloadWebhookDisbursement{
			Event:    "dist.send",
			Provider: "xendit",
			Data:     webhook,
		}

		//publish to queue
		err = s.taskPublisher.PublishWebhookDisburse(ctx, payload)
		if err != nil {
			return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebhookService.taskPublisher.PublishWebhookDisburse")
		}

		s.util.Logger.Info("WebhookService.HandleWebhookDisburse.Xendit", zap.Any("Payload", payload))
	}

	return true, nil
}
