package services

import (
	"context"
	"net/url"
	"strings"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type PaymentMethodRepository interface {
	Create(ctx context.Context, params internal.CreatePaymentMethod) (internal.PaymentMethod, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreatePaymentMethod) (internal.PaymentMethod, error)
	GetByID(ctx context.Context, id string) (internal.PaymentMethod, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.PaymentMethod, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.PaymentMethodPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.PaymentMethod, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreatePaymentMethod) (internal.PaymentMethod, error)
}

type PaymentMethodService struct {
	repo PaymentMethodRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewPaymentMethodService(util riot.Util, repo PaymentMethodRepository) *PaymentMethodService {
	return &PaymentMethodService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *PaymentMethodService) Create(ctx context.Context, params internal.CreatePaymentMethod, nexusData riot.Nexus) (_ internal.PaymentMethod, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodService", "Create"))

	if !s.cb.Ready() {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.CompanyID = profile.ParentID
	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentMethodService.params.Validate")
	}

	name := strings.ToUpper(strings.ReplaceAll(*params.DisplayName, " ", "_"))
	params.Name = &name

	filter := map[string]interface{}{
		"company_id": profile.ParentID,
		"name":       name,
		"status":     constant.TypeActive,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodService.s.repo.GetByFilter")
	}

	s.util.Logger.Info("PaymentMethodService.s.repo.GetByFilter", zap.Any("res", res))

	if err := res.ValidateRegistered(); err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentMethodService.res.ValidateRegistered")
	}

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodService.s.repo.Create")
	}

	return res, nil
}

func (s *PaymentMethodService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentMethodService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *PaymentMethodService) Update(ctx context.Context, id string, params internal.CreatePaymentMethod, nexusData riot.Nexus) (_ internal.PaymentMethod, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodService", "Update"))

	if !s.cb.Ready() {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentMethodService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodService.s.repo.Update")
	}

	return res, nil
}

func (s *PaymentMethodService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.PaymentMethod, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodService", "GetByID"))

	if !s.cb.Ready() {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":     id,
		"status": constant.TypeActive,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.PaymentMethod{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentMethodService.s.repo.GetByID")
	}

	return res, nil
}

func (s *PaymentMethodService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.PaymentMethodPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentMethodService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.PaymentMethodPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	webservice := riot.GetNexusWebservice(nexusData)

	companyID := ""

	if profile.ID != "" {
		switch profile.Role {
		case riot.RoleCompany:
			companyID = profile.ID
		case riot.RoleCustomer:
			companyID = profile.ParentID
		}
	} else if webservice.ID != "" {
		companyID = webservice.ID
	}

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if companyID != "" {
		objectID, err := primitive.ObjectIDFromHex(companyID)
		if err != nil {
			return internal.PaymentMethodPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.PaymentMethodPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentMethodService.repo.GetAllWithPagination")
	}

	return res, nil
}
