package services

import (
	"context"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type ProviderRequestLogRepository interface {
	Create(ctx context.Context, params internal.CreateProviderRequestLog) (internal.ProviderRequestLog, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateProviderRequestLog) (internal.ProviderRequestLog, error)
	GetByID(ctx context.Context, id string) (internal.ProviderRequestLog, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.ProviderRequestLog, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ProviderRequestLogPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.ProviderRequestLog, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateProviderRequestLog) (internal.ProviderRequestLog, error)
}

type ProviderRequestLogService struct {
	repo ProviderRequestLogRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewProviderRequestLogService(util riot.Util, repo ProviderRequestLogRepository) *ProviderRequestLogService {
	return &ProviderRequestLogService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *ProviderRequestLogService) Create(ctx context.Context, params internal.CreateProviderRequestLog) (internal.ProviderRequestLog, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderRequestLogService", "Create"))

	params.Status = constant.StatusActive

	if err := params.Validate(); err != nil {
		return internal.ProviderRequestLog{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderRequestLogService.params.Validate")
	}

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.ProviderRequestLog{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderRequestLogService.s.repo.Create")
	}

	return res, nil
}
