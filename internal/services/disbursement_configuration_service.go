package services

import (
	"context"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type DisbursementConfigurationRepository interface {
	Create(ctx context.Context, params internal.CreateDisbursementConfiguration) (internal.DisbursementConfiguration, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateDisbursementConfiguration) (internal.DisbursementConfiguration, error)
	GetByID(ctx context.Context, id string) (internal.DisbursementConfiguration, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.DisbursementConfiguration, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.DisbursementConfigurationPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.DisbursementConfiguration, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateDisbursementConfiguration) (internal.DisbursementConfiguration, error)
}

type DisbursemenConfigurationtService struct {
	repo             DisbursementConfigurationRepository
	merchantRepo     MerchantRepository
	merchantBankRepo MerchantBankRepository
	confRepo         ConfigurationRepository
	taskPublisher    *rabbitmq.Publisher
	cb               *circuitbreaker.CircuitBreaker
	util             riot.Util
}

func NewDisbursemenConfigurationtService(util riot.Util, repo DisbursementConfigurationRepository, merchantRepo MerchantRepository, merchantBankRepo MerchantBankRepository, confRepo ConfigurationRepository, taskPublisher *rabbitmq.Publisher) *DisbursemenConfigurationtService {
	return &DisbursemenConfigurationtService{
		repo:             repo,
		merchantRepo:     merchantRepo,
		merchantBankRepo: merchantBankRepo,
		confRepo:         confRepo,
		taskPublisher:    taskPublisher,
		util:             util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *DisbursemenConfigurationtService) Create(ctx context.Context, identifier internal.Identifier, params internal.CreateDisbursementConfiguration, nexusData riot.Nexus) (_ internal.DisbursementConfiguration, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursemenConfigurationtService", "Create"))

	if !s.cb.Ready() {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	var merchantBank *internal.MerchantBank

	if err = identifier.ValidateMarchantID(); err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursemenConfigurationtService.Create.Identifier.Validate")
	}

	params.Status = constant.StatusActive

	filterMerchant := map[string]interface{}{
		"profile_id": profile.ID,
		"id":         identifier.MerchantID,
	}

	s.util.Logger.Debug("DisbursemenConfigurationtService.Create.filterMerchant", zap.Any("filterMerchant", filterMerchant)) //nolint.

	merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.Create.merchantRepo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.Create.merchant.ValidateNotExist")
	}

	if params.MerchantBankID != nil && *params.MerchantBankID != "" {

		filterMerchantBank := map[string]interface{}{
			"id":         params.MerchantBankID,
			"profile_id": profile.ID,
		}

		merchantBank, err := s.merchantBankRepo.GetByFilter(ctx, filterMerchantBank)
		if err != nil {
			return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.Create.merchantBankRepo.GetByFilter")
		}

		if err := merchantBank.ValidateNotExist(); err != nil {
			return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.Create.merchantBank.ValidateNotExist")
		}
	}

	//delete existing disbursement configuration
	err = s.repo.SoftDeleteByFilter(ctx, map[string]interface{}{
		"merchant_id": merchant.ID,
	})

	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.Create.s.repo.SoftDeleteByFilter")
	}

	params.MerchantID = merchant.ID
	params.CompanyID = profile.ParentID
	params.ProfileID = profile.ID

	if err = params.Validate(); err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursemenConfigurationtService.Create.Validate")
	}

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.s.repo.Create")
	}

	res.Merchant = &merchant
	res.MerchantBank = merchantBank

	return res, nil
}

func (s *DisbursemenConfigurationtService) Delete(ctx context.Context, identifier internal.Identifier, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursemenConfigurationtService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterDisbursementConf := map[string]interface{}{
		"merchant_id": identifier.MerchantID,
		"profile_id":  profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterDisbursementConf)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursemenConfigurationtService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterDisbursementConf)
}

func (s *DisbursemenConfigurationtService) Update(ctx context.Context, identifier internal.Identifier, params internal.CreateDisbursementConfiguration, nexusData riot.Nexus) (_ internal.DisbursementConfiguration, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursemenConfigurationtService", "Update"))

	if !s.cb.Ready() {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	merchantBank := internal.MerchantBank{}

	filterDisbursementConf := map[string]interface{}{
		"merchant_id": identifier.MerchantID,
		"profile_id":  profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterDisbursementConf)

	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursemenConfigurationtService.s.repo.GetByFilter")
	}

	merchant, err := s.merchantRepo.GetByID(ctx, res.MerchantID)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.merchantRepo.GetByID")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.merchant.ValidateNotExist")
	}

	if params.MerchantBankID != nil && *params.MerchantBankID != "" {

		filterMerchantBank := map[string]interface{}{
			"id":         *params.MerchantBankID,
			"profile_id": profile.ID,
		}

		merchantBank, err = s.merchantBankRepo.GetByFilter(ctx, filterMerchantBank)

		if err != nil {
			return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.merchantBankRepo.GetByFilter")
		}

	} else {

		filterMerchantBank := map[string]interface{}{
			"id":         res.MerchantBankID,
			"profile_id": profile.ID,
		}

		merchantBank, err = s.merchantBankRepo.GetByFilter(ctx, filterMerchantBank)

		if err != nil {
			return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.merchantBankRepo.GetByFilter")
		}
	}

	res, err = s.repo.UpdateByFilter(ctx, filterDisbursementConf, params)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.s.repo.Update")
	}

	res.Merchant = &merchant
	res.MerchantBank = &merchantBank

	return res, nil
}

func (s *DisbursemenConfigurationtService) GetByMerchantID(ctx context.Context, identifier internal.Identifier, id string, includes []string, nexusData riot.Nexus) (_ internal.DisbursementConfiguration, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursemenConfigurationtService", "GetByID"))

	if !s.cb.Ready() {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"profile_id": profile.ID,
		"id":         identifier.MerchantID,
	}

	merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.merchantRepo.GetByFilter")
	}

	filterMerchantBank := map[string]interface{}{
		"merchant_id": identifier.MerchantID,
		"profile_id":  profile.ID,
		"is_default":  true,
	}

	merchantBank, err := s.merchantBankRepo.GetByFilter(ctx, filterMerchantBank)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.merchantBankRepo.GetByFilter")
	}

	filterDisbursementConf := map[string]interface{}{
		"merchant_id": identifier.MerchantID,
		"profile_id":  profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterDisbursementConf)
	if err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursemenConfigurationtService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.DisbursementConfiguration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursemenConfigurationtService.s.repo.GetByID")
	}

	res.Merchant = &merchant
	res.MerchantBank = &merchantBank

	return res, nil
}
