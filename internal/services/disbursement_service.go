package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/manaslu/internal/xendit"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type DisbursementRepository interface {
	Create(ctx context.Context, params internal.CreateDisbursement) (internal.Disbursement, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateDisbursement) (internal.Disbursement, error)
	GetByID(ctx context.Context, id string) (internal.Disbursement, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Disbursement, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.DisbursementPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Disbursement, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateDisbursement) (internal.Disbursement, error)
}

type DisbursementService struct {
	repo                 DisbursementRepository
	merchantRepo         MerchantRepository
	merchantBankRepo     MerchantBankRepository
	confRepo             ConfigurationRepository
	disbLogRepo          DisbursementLogRepository
	merchantPlatformRepo MerchantPlatformRepository
	taskPublisher        *rabbitmq.Publisher
	cb                   *circuitbreaker.CircuitBreaker
	util                 riot.Util
}

func NewDisbursementService(util riot.Util, repo DisbursementRepository, merchantRepo MerchantRepository, merchantBankRepo MerchantBankRepository, confRepo ConfigurationRepository, disbLogRepo DisbursementLogRepository, merchantPlatformRepo MerchantPlatformRepository, taskPublisher *rabbitmq.Publisher) *DisbursementService {
	return &DisbursementService{
		repo:                 repo,
		merchantRepo:         merchantRepo,
		merchantBankRepo:     merchantBankRepo,
		confRepo:             confRepo,
		disbLogRepo:          disbLogRepo,
		merchantPlatformRepo: merchantPlatformRepo,
		taskPublisher:        taskPublisher,
		util:                 util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *DisbursementService) Create(ctx context.Context, params internal.CreateDisbursement, nexusData riot.Nexus) (_ internal.Disbursement, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementService", "Create"))

	if !s.cb.Ready() {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementService.Create.Validate")
	}

	filterMerchant := map[string]interface{}{
		"profile_id": profile.ID,
		"id":         params.MerchantID,
	}

	s.util.Logger.Debug("DisbursementService.Create.filterMerchant", zap.Any("filterMerchant", filterMerchant)) //nolint.

	merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Create.merchantRepo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Create.merchant.ValidateNotExist")
	}

	//check balance

	if err := merchant.ValidateBalance(*params.Amount); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementService.Create.merchant.ValidateBalance")
	}

	filterMerchantBank := map[string]interface{}{
		"profile_id":  profile.ID,
		"merchant_id": params.MerchantID,
		"id":          params.MerchantBankID,
	}

	merchantBank, err := s.merchantBankRepo.GetByFilter(ctx, filterMerchantBank)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Create.merchantBankRepo.GetByFilter")
	}

	if err := merchantBank.ValidateNotExist(); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Create.merchantBank.ValidateNotExist")
	}

	filterMerchantPlatform := map[string]interface{}{
		"company_id": merchantBank.CompanyID,
		"status":     constant.TypeActive,
	}

	mapMerchantPlatform := map[string]internal.MerchantPlatform{}
	merchantPlatforms, err := s.merchantPlatformRepo.GetAll(ctx, filterMerchantPlatform)

	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.merchantPlatformRepo.GetByFilter")
	}

	for _, merchantPlatform := range merchantPlatforms {
		mapMerchantPlatform[merchantPlatform.Name] = merchantPlatform
	}

	uniqueNo := pkg.GenerateUniqueNo()
	disbNo := fmt.Sprintf("%s-%s", "DISB", uniqueNo)

	description := ""
	if params.Description != nil {
		description = *params.Description
	}

	filterConf := map[string]interface{}{
		"company_id": profile.ParentID,
		"name":       "DISBURSEMENT",
	}

	conf, err := s.confRepo.GetByFilter(ctx, filterConf)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Create.confRepo.GetByFilter")
	}

	if err := conf.ValidateNotExist(); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Create.conf.ValidateNotExist")
	}

	netAmount := 0.0
	platformFee := 0.0
	pgFee := 0.0
	amount := 0.0
	fee := 0.0

	if params.Amount != nil && *params.Amount > 0 {
		amount = *params.Amount
	}

	switch *params.Provider {
	case constant.ProviderXendit:
		xend := xendit.NewXendit(s.util.Conf.Get("XENDIT_API_KEY"), s.util, params.CompanyID, "", disbNo, s.taskPublisher)
		disbFeeStr := s.util.Conf.Get("XENDIT_DISBURSEMENT_FEE")

		disbFee, err := strconv.ParseFloat(disbFeeStr, 64)
		if err != nil {
			return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Create.ParseFloat")
		}

		netAmount = *params.Amount

		if conf.FeeType == constant.FeeTypePercentage {
			fee := pkg.CalculatePercentage(amount, conf.FeeValue)
			platformFee = fee - disbFee
		} else if conf.FeeType == constant.FeeTypeFixed {
			platformFee = conf.FeeValue - disbFee
		}

		platformFee = conf.FeeValue - disbFee
		netAmount = netAmount - (disbFee + platformFee)
		pgFee = disbFee

		fee = conf.FeeValue

		respDisb, err := xend.CreateDisbursement(disbNo, merchantBank.BankDetail.BankCode, merchantBank.BankAccountNumber, merchantBank.BankAccountHolderName, netAmount, description)
		if err != nil {
			return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.Create.xend.CreateQRCode")
		}

		params.CompanyID = merchant.CompanyID
		params.ProfileID = profile.ID
		params.ProviderDisbursementID = &respDisb.ID
		params.DisbursementNo = &disbNo
		params.ExternalID = &disbNo
		params.AccountHolderName = &merchantBank.BankAccountHolderName
		params.BankCode = &merchantBank.BankDetail.BankCode
		params.AccountNumber = &merchantBank.BankAccountNumber
		params.Status = constant.StatusTransactionInProgress
		params.NetAmount = &netAmount
		params.PlatformFee = &platformFee
		params.PGFee = &pgFee
		params.Fee = &fee
		params.Amount = &amount
	}

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.repo.Create")
	}

	payloadTxnBalance := internal.PayloadTxnBalance{
		CompanyID:  merchant.CompanyID,
		ProfileID:  merchant.ProfileID,
		TypeID:     res.ID,
		TypeObject: constant.TypeObjectDisb,
		Type:       constant.TypeTxnDisburseMerhant,
		SourceID:   res.MerchantID,
		Description: constant.ReverseTxnConstant(
			constant.TypeTxnDisburseMerhant),
		MerchantID:  merchant.ID,
		ProductType: constant.TypeFeatureDisb,
		Amount:      res.Amount,
	}

	if err := s.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.taskPublisher.PublishTxnBalance")
	}

	payloadTxnBalance = internal.PayloadTxnBalance{
		CompanyID:  merchant.CompanyID,
		ProfileID:  mapMerchantPlatform["XENDIT"].ProfileID,
		TypeID:     res.ID,
		TypeObject: constant.TypeObjectDisb,
		Type:       constant.TypeTxnDisburseFee,
		SourceID:   res.MerchantID,
		Description: constant.ReverseTxnConstant(
			constant.TypeTxnDisburseFee),
		MerchantID:  mapMerchantPlatform["XENDIT"].MerchantID,
		ProductType: constant.TypeFeatureDisb,
		Amount:      res.NetAmount + res.PGFee,
	}

	if err := s.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.taskPublisher.PublishTxnBalance")
	}

	payloadTxnBalance = internal.PayloadTxnBalance{
		CompanyID:  merchant.CompanyID,
		ProfileID:  mapMerchantPlatform["DISBURSEMENT"].ProfileID,
		TypeID:     res.ID,
		TypeObject: constant.TypeObjectDisb,
		Type:       constant.TypeTxnDisburseFee,
		SourceID:   res.MerchantID,
		Description: constant.ReverseTxnConstant(
			constant.TypeTxnDisburseFee),
		MerchantID:  mapMerchantPlatform["DISBURSEMENT"].MerchantID,
		ProductType: constant.TypeFeatureDisb,
		Amount:      res.PlatformFee,
	}

	if err := s.taskPublisher.PublishTxnBalance(ctx, payloadTxnBalance); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.taskPublisher.PublishTxnBalance")
	}

	//create disb log
	paramsLog := internal.CreateDisbursementLog{
		CompanyID:              res.CompanyID,
		ProfileID:              res.ProfileID,
		MerchantID:             res.MerchantID,
		MerchantBankID:         res.MerchantBankID,
		DisbursementID:         res.ID,
		ProviderDisbursementID: &res.ProviderDisbursementID,
		DisbursementNo:         &res.DisbursementNo,
		Provider:               &res.Provider,
		Status:                 res.Status,
	}

	_, err = s.disbLogRepo.Create(ctx, paramsLog)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.disbursementLogRepo.Create")
	}

	return res, nil
}

func (s *DisbursementService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterDisbursement := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterDisbursement)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterDisbursement)
}

func (s *DisbursementService) Update(ctx context.Context, id string, params internal.CreateDisbursement, nexusData riot.Nexus) (_ internal.Disbursement, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementService", "Update"))

	if !s.cb.Ready() {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterDisbursement := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterDisbursement)

	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterDisbursement, params)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.repo.Update")
	}

	return res, nil
}

func (s *DisbursementService) Calculate(ctx context.Context, params internal.DisbursementCalculated, nexusData riot.Nexus) (_ internal.DisbursementCalculated, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementService", "Calculate"))

	if !s.cb.Ready() {
		return internal.DisbursementCalculated{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	if err := params.Validate(); err != nil {
		return internal.DisbursementCalculated{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementService.Calculate.params.Validate")
	}

	filterConf := map[string]interface{}{
		"company_id": profile.ParentID,
		"name":       "DISBURSEMENT",
	}

	conf, err := s.confRepo.GetByFilter(ctx, filterConf)
	if err != nil {
		return internal.DisbursementCalculated{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Calculate.confRepo.GetByFilter")
	}

	if err := conf.ValidateNotExist(); err != nil {
		return internal.DisbursementCalculated{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Calculate.conf.ValidateNotExist")
	}

	netAmount := 0.0
	platformFee := 0.0
	pgFee := 0.0
	amount := 0.0
	fee := 0.0

	fee = conf.FeeValue

	if params.AmountStr != "" {
		amount, err = strconv.ParseFloat(params.AmountStr, 64)
		if err != nil {
			return internal.DisbursementCalculated{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Calculate.ParseFloat")
		}
	}

	if params.Provider != "" && params.Provider == constant.ProviderXendit {

		disbFeeStr := s.util.Conf.Get("XENDIT_DISBURSEMENT_FEE")

		disbFee, err := strconv.ParseFloat(disbFeeStr, 64)
		if err != nil {
			return internal.DisbursementCalculated{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.Create.ParseFloat")
		}

		netAmount = amount - disbFee

		if conf.FeeType == constant.FeeTypePercentage {
			platformFee = ((amount * conf.FeeValue) / 100) - disbFee
		} else if conf.FeeType == constant.FeeTypeFixed {
			platformFee = conf.FeeValue - disbFee
		}

		pgFee = disbFee
	}

	res := internal.DisbursementCalculated{
		Amount:      amount,
		NetAmount:   netAmount,
		PlatformFee: platformFee,
		PGFee:       pgFee,
		Provider:    params.Provider,
		Fee:         fee,
	}

	return res, nil
}

func (s *DisbursementService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Disbursement, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterDisbursement := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterDisbursement)
	if err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Disbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementService.s.repo.GetByID")
	}

	return res, nil
}

func (s *DisbursementService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.DisbursementPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("DisbursementService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.DisbursementPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	var query *internal.QueryDisbursement

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))
	queryQry, _ := riot.QueryByPrefix(&urlValues, "query")

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.DisbursementPagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.DisbursementPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	if err := json.Unmarshal([]byte(queryQry), &query); err != nil {
		return internal.DisbursementPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementService.GetAllWithPagination.json.Unmarshal")
	}

	if query != nil {

		if query.DisbursementNo != "" {
			builder.SearchANDFields["disbursement_no"] = query.DisbursementNo
		}

		if query.AccountHolderName != "" {
			builder.SearchANDFields["account_holder_name"] = query.AccountHolderName
		}

		if query.AccountNumber != "" {
			builder.SearchANDFields["account_number"] = query.AccountNumber
		}

		if query.Provider != "" {
			builder.SearchANDFields["provider"] = query.Provider
		}

		if query.BankCode != "" {
			builder.SearchANDFields["bank_code"] = query.BankCode
		}

		if query.Amount != "" {
			//convert string to float64
			amount, err := strconv.ParseFloat(query.Amount, 64)
			if err != nil {
				return internal.DisbursementPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "DisbursementService.GetAllWithPagination.ParseFloat")
			}
			builder.SearchANDFields["amount"] = amount
		}

	}

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.DisbursementPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "DisbursementService.repo.GetAllWithPagination")
	}

	return res, nil
}
