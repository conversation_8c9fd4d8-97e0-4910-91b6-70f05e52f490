package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type MerchantBankRepository interface {
	Create(ctx context.Context, params internal.CreateMerchantBank) (internal.MerchantBank, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateMerchantBank) (internal.MerchantBank, error)
	GetByID(ctx context.Context, id string) (internal.MerchantBank, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantBank, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantBankPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantBank, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantBank) (internal.MerchantBank, error)
	CreateBulk(ctx context.Context, params []internal.CreateMerchantBank) ([]internal.MerchantBank, error)
	UpdateManyByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantBank) (internal.MerchantBank, error)
}

type MerchantBankService struct {
	repo          MerchantBankRepository
	merchantRepo  MerchantRepository
	bankRepo      BankRepository
	taskPublisher *rabbitmq.Publisher
	cb            *circuitbreaker.CircuitBreaker
	util          riot.Util
}

func NewMerchantBankService(util riot.Util, repo MerchantBankRepository, merchantRepo MerchantRepository, bankRepo BankRepository, taskPublisher *rabbitmq.Publisher) *MerchantBankService {
	return &MerchantBankService{
		repo:          repo,
		merchantRepo:  merchantRepo,
		bankRepo:      bankRepo,
		util:          util,
		taskPublisher: taskPublisher,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *MerchantBankService) Create(ctx context.Context, identifier internal.Identifier, params internal.CreateMerchantBank, nexusData riot.Nexus) (_ internal.MerchantBank, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankService", "Create"))

	if !s.cb.Ready() {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.params.Validate")
	}

	filterMerchant := map[string]interface{}{
		"profile_id": profile.ID,
		"id":         identifier.MerchantID,
	}

	merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.Create.repo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.Create.merchant.ValidateNotExist")
	}

	//find bank
	filterBank := map[string]interface{}{
		"bank_code": params.BankCode,
	}

	bank, err := s.bankRepo.GetByFilter(ctx, filterBank)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.Create.bankRepo.GetByFilter")
	}

	if err := bank.ValidateNotExist(); err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.Create.bank.ValidateNotExist")
	}

	//check merchant bank
	filterMerchantBank := map[string]interface{}{
		"merchant_id": merchant.ID,
		"bank_id":     bank.ID,
	}

	merchantBank, err := s.repo.GetByFilter(ctx, filterMerchantBank)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.Create.repo.GetByFilter")
	}

	if err := merchantBank.ValidateExist(); err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.Create.merchantBank.ValidateExist")
	}

	//check validate bank
	// xend := xendit.NewXendit(s.util.Conf.Get("XENDIT_API_KEY"), s.util, merchant.CompanyID, "", "", s.taskPublisher)

	// _, err = xend.CheckBankAccount(params.BankCode, *params.BankAccountNumber)
	// if err != nil {
	// 	return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.Create.xend.CreateQRCode")
	// }

	// update merchant default bank

	if params.IsDefault != nil && *params.IsDefault {
		isDefault := false
		_, err := s.repo.UpdateManyByFilter(ctx, map[string]interface{}{
			"merchant_id": merchant.ID,
		}, internal.CreateMerchantBank{
			IsDefault: &isDefault,
		})

		if err != nil {
			return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.Create.repo.UpdateByFilter")
		}
	}

	params.CompanyID = merchant.CompanyID
	params.ProfileID = merchant.ProfileID
	params.MerchantID = merchant.ID
	params.BankID = bank.ID
	params.BankName = &bank.Name
	params.BankDetail = &bank

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.s.repo.Create")
	}

	return res, nil
}

func (s *MerchantBankService) CreateBulk(ctx context.Context, identifier internal.Identifier, params []internal.CreateMerchantBank, nexusData riot.Nexus) ([]internal.MerchantBank, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankService", "CreateBulk"))

	// Prepare payloads
	payloads := []internal.CreateMerchantBank{}
	for _, param := range params {
		if param.Status == "" {
			param.Status = constant.StatusActive
		}

		// Validate parameters
		if err := param.Validate(); err != nil {
			return []internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.CreateBulk.params.Validate")
		}

		payloads = append(payloads, param)
	}

	// Create user themes
	res, err := s.repo.CreateBulk(ctx, payloads)
	if err != nil {
		return []internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.CreateBulk.repo.Create")
	}

	return res, nil
}

func (s *MerchantBankService) Delete(ctx context.Context, identifier internal.Identifier, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchantBank := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchantBank)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchantBank)
}

func (s *MerchantBankService) Update(ctx context.Context, identifier internal.Identifier, id string, params internal.CreateMerchantBank, nexusData riot.Nexus) (_ internal.MerchantBank, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankService", "Update"))

	if !s.cb.Ready() {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchantBank := map[string]interface{}{
		"id":         identifier.MerchantID,
		"profile_id": profile.ID,
	}

	merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchantBank)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.Update.merchantRepo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.Update.merchant.ValidateNotExist")
	}

	if params.BankCode != "" {
		//find bank
		filterBank := map[string]interface{}{
			"bank_code": params.BankCode,
		}

		bank, err := s.bankRepo.GetByFilter(ctx, filterBank)
		if err != nil {
			return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.Update.bankRepo.GetByFilter")
		}

		if err := bank.ValidateNotExist(); err != nil {
			return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.Update.bank.ValidateNotExist")
		}
	}

	if params.IsDefault != nil && *params.IsDefault {

		isDefault := false
		_, err := s.repo.UpdateManyByFilter(ctx, map[string]interface{}{
			"merchant_id": merchant.ID,
		}, internal.CreateMerchantBank{
			IsDefault: &isDefault,
		})

		if err != nil {
			return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.Create.repo.UpdateByFilter")
		}
	}

	// if *params.BankAccountNumber != "" {
	// 	//check validate bank
	// 	xend := xendit.NewXendit(s.util.Conf.Get("XENDIT_API_KEY"), s.util, merchant.CompanyID, "", "", s.taskPublisher)

	// 	_, err = xend.CheckBankAccount(params.BankCode, *params.BankAccountNumber)
	// 	if err != nil {
	// 		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.Create.xend.CreateQRCode")
	// 	}
	// }

	res, err := s.repo.Update(ctx, id, params)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.s.repo.UpdateByFilter")
	}

	return res, nil
}

func (s *MerchantBankService) GetByID(ctx context.Context, identifier internal.Identifier, id string, includes []string, nexusData riot.Nexus) (_ internal.MerchantBank, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankService", "GetByID"))

	if !s.cb.Ready() {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchantBank := map[string]interface{}{
		"id":         identifier.MerchantID,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchantBank)
	if err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.MerchantBank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantBankService.s.repo.GetByID")
	}

	return res, nil
}

func (s *MerchantBankService) GetAllWithPagination(ctx context.Context, identifier internal.Identifier, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantBankPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantBankService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.MerchantBankPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if identifier.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(identifier.MerchantID)
		if err != nil {
			return internal.MerchantBankPagin{}, err
		}
		filterMap["merchant_id"] = objectID
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.MerchantBankPagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.MerchantBankPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantBankService.repo.GetAllWithPagination")
	}

	return res, nil
}
