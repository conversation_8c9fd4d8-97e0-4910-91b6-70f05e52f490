package services

import (
	"context"
	"math"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type BalanceTransactionRepository interface {
	Create(ctx context.Context, params internal.CreateBalanceTransaction) (internal.BalanceTransaction, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateBalanceTransaction) (internal.BalanceTransaction, error)
	GetByID(ctx context.Context, id string) (internal.BalanceTransaction, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.BalanceTransaction, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.BalanceTransactionPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.BalanceTransaction, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateBalanceTransaction) (internal.BalanceTransaction, error)
	GetStatistics(ctx context.Context, filter internal.BalanceTransactionStatsFilter) (internal.BalanceTransactionStatistics, error)
}

type BalanceTransactionService struct {
	repo         BalanceTransactionRepository
	merchantRepo MerchantRepository
	txnRepo      TransactionRepository
	disbRepo     DisbursementRepository
	cb           *circuitbreaker.CircuitBreaker
	util         riot.Util
}

func NewBalanceTransactionService(util riot.Util, repo BalanceTransactionRepository, merchantRepo MerchantRepository, txnRepo TransactionRepository, disbRepo DisbursementRepository) *BalanceTransactionService {
	return &BalanceTransactionService{
		repo:         repo,
		merchantRepo: merchantRepo,
		txnRepo:      txnRepo,
		disbRepo:     disbRepo,
		util:         util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *BalanceTransactionService) Create(ctx context.Context, params []internal.CreateBalanceTransaction, nexusData riot.Nexus) (_ []internal.BalanceTransaction, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionService", "Create"))

	results := []internal.BalanceTransaction{}

	if !s.cb.Ready() {
		return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	companyID := ""

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	if profile.ID != "" {
		switch profile.Role {
		case riot.RoleCompany:
			companyID = profile.ID
		case riot.RoleCustomer:
			companyID = profile.ParentID
		}
	} else if webservice.ID != "" {
		companyID = webservice.ID
	}

	for _, param := range params {

		if err := param.Validate(); err != nil {
			return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "BalanceTransactionService.Create.params.Validate")
		}

		stateType := *param.Type

		switch stateType {

		case constant.TypeTxnCompleted, constant.TypeTxnFeatureFee, constant.TypeTxnComission, constant.TypeTxnSubsidy:

			filterTxn := map[string]interface{}{
				"id":     *param.TypeID,
				"status": constant.TypePaymentCompleted,
			}

			txn, err := s.txnRepo.GetByFilter(ctx, filterTxn)
			if err != nil {
				return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.repo.GetByFilter")
			}

			if err := txn.ValidateNotExist(); err != nil {
				return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.txn.ValidateNotExist")
			}
		case constant.TypeTxnDisburseMerhant, constant.TypeTxnDisburseFeeFail, constant.TypeTxnDisburseFee, constant.TypeTxnDisburseFail:

			filterDisb := map[string]interface{}{
				"id":     *param.TypeID,
				"status": constant.TypeActive,
			}

			disb, err := s.disbRepo.GetByFilter(ctx, filterDisb)
			if err != nil {
				return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.repo.GetByFilter")
			}

			if err := disb.ValidateNotExist(); err != nil {
				return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.disb.ValidateNotExist")
			}

		}
	}

	for _, param := range params {

		stateType := *param.Type

		switch stateType {
		case constant.TypeTxnSubsidy, constant.TypeTxnDisburseMerhant, constant.TypeTxnDisburseFeeFail, constant.TypeTxnDisburseCoin:

			//make negative amount
			value := -math.Abs(*param.Amount)
			param.Amount = &value
		}

		filterMerchant := map[string]interface{}{
			"id":     param.MerchantID,
			"status": constant.TypeActive,
		}

		merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
		if err != nil {
			return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.merchantRepo.GetByFilter")
		}

		if err := merchant.ValidateNotExist(); err != nil {
			return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.merchant.ValidateNotExist")
		}

		//filter Typetxnn
		filterTxnBalance := map[string]interface{}{
			"profile_id": merchant.ProfileID,
			"type":       *param.Type,
			"type_id":    *param.TypeID,
			"status":     constant.TypeActive,
		}

		if param.SourceID != nil && *param.SourceID != "" {
			filterTxnBalance["source_id"] = *param.SourceID
		}

		s.util.Logger.Info("filterTxnBalance", zap.Any("filterTxnBalance", filterTxnBalance))

		txnBalance, err := s.repo.GetByFilter(ctx, filterTxnBalance)

		if err != nil {
			return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.s.repo.GetByFilter")
		}

		if txnBalance.IsExist() {
			results = append(results, txnBalance)
			continue
		}

		param.MerchantID = merchant.ID
		param.CompanyID = companyID
		param.ProfileID = merchant.ProfileID
		param.Status = constant.StatusActive
		param.Balance = param.Amount
		param.BalanceBefore = &merchant.Balance

		res, err := s.repo.Create(ctx, param)
		if err != nil {
			return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.s.repo.Create")
		}
		results = append(results, res)

		//update coin
		balance := merchant.Balance + *param.Amount

		updateParams := internal.CreateMerchant{
			Balance:        &balance,
			SettledBalance: &balance,
		}

		merchant, err = s.merchantRepo.UpdateByFilter(ctx, filterMerchant, updateParams)
		if err != nil {
			return []internal.BalanceTransaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.merchantRepo.UpdateByFilter")
		}

		results = append(results, res)
	}

	return results, nil
}

func (s *BalanceTransactionService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.BalanceTransactionPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.BalanceTransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.BalanceTransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.repo.GetAllWithPagination")
	}

	return res, nil
}

// GetStatistics retrieves aggregated statistics for balance transactions
func (s *BalanceTransactionService) GetStatistics(ctx context.Context, filter internal.BalanceTransactionStatsFilter, nexusData riot.Nexus) (_ internal.BalanceTransactionStatistics, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BalanceTransactionService", "GetStatistics"))

	if !s.cb.Ready() {
		return internal.BalanceTransactionStatistics{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// Get company ID from nexus data
	companyID := ""
	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	if profile.ID != "" {
		switch profile.Role {
		case riot.RoleCompany:
			companyID = profile.ID
		case riot.RoleCustomer:
			companyID = profile.ParentID
		}
	} else if webservice.ID != "" {
		companyID = webservice.ID
	}

	// Set company filter if not already set
	if filter.CompanyID == nil && companyID != "" {
		filter.CompanyID = &companyID
	}

	// Set default date range if not provided
	if filter.StartDate == nil && filter.EndDate == nil {
		now := time.Now()
		switch filter.Period {
		case "daily":
			startDate := now.AddDate(0, 0, -30) // Last 30 days
			filter.StartDate = &startDate
			filter.EndDate = &now
		case "monthly":
			startDate := now.AddDate(-1, 0, 0) // Last 12 months
			filter.StartDate = &startDate
			filter.EndDate = &now
		case "yearly":
			startDate := now.AddDate(-5, 0, 0) // Last 5 years
			filter.StartDate = &startDate
			filter.EndDate = &now
		default:
			// Default to last 30 days
			startDate := now.AddDate(0, 0, -30)
			filter.StartDate = &startDate
			filter.EndDate = &now
		}
	}

	// Call repository method
	stats, err := s.repo.GetStatistics(ctx, filter)
	if err != nil {
		return internal.BalanceTransactionStatistics{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BalanceTransactionService.repo.GetStatistics")
	}

	return stats, nil
}
