package services

import (
	"context"
	"errors"
	"net/url"
	"time"

	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type VoucherRepository interface {
	Create(ctx context.Context, params internal.CreateVoucher) (internal.Voucher, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateVoucher) (internal.Voucher, error)
	GetByID(ctx context.Context, id string) (internal.Voucher, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Voucher, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.VoucherPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Voucher, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateVoucher) (internal.Voucher, error)
}

type VoucherService struct {
	repo    VoucherRepository
	feeRepo FeeRepository
	cb      *circuitbreaker.CircuitBreaker
	util    riot.Util
}

func NewVoucherService(util riot.Util, repo VoucherRepository, feeRepo FeeRepository) *VoucherService {
	return &VoucherService{
		repo:    repo,
		feeRepo: feeRepo,
		util:    util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *VoucherService) Create(ctx context.Context, params internal.CreateVoucher, nexusData riot.Nexus) (_ internal.Voucher, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherService", "Create"))

	if !s.cb.Ready() {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.params.Validate")
	}

	if err = params.ValidateExpireType(); err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.params.ValidateExpireType")
	}

	filter := map[string]interface{}{
		"code": params.Code,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.s.repo.GetByFilter")
	}

	if err := res.ValidateRegistered(); err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.res.ValidateRegistered")
	}

	//check if custom
	if params.DiscountType != nil && *params.DiscountType == "custom" {

		fees := params.Fees
		expectedFee := 0.0
		total := 0.0

		for _, fee := range fees {
			expectedFee = *fee.FeeValue

			total += *fee.PlatformFeeValue
			total += *fee.PGFeeValue
		}

		if total != expectedFee {
			return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, errors.New("total fee is not equal to expected fee"), riot.ErrorCodeInvalidArgument, "VoucherService.res.ValidateRegistered")
		}
	}

	if params.UsageLimit != nil && *params.UsageLimit > 0 {
		params.RemainingUsage = params.UsageLimit
	}

	switch params.ExpireType {
	case "dynamic_period":
		now := time.Now()

		switch *params.PeriodType {
		case "day":
			period := time.Duration(*params.Period) * time.Hour * 24
			expiresAt := now.Add(period)

			expiresAtStr := expiresAt.Format(pkg.DateLayoutDefault)
			params.ExpiresAt = expiresAtStr

		case "week":
			period := time.Duration(*params.Period) * time.Hour * 24 * 7
			expiresAt := now.Add(period)
			expiresAtStr := expiresAt.Format(pkg.DateLayoutDefault)
			params.ExpiresAt = expiresAtStr

		case "month":
			period := time.Duration(*params.Period) * time.Hour * 24 * 30
			expiresAt := now.Add(period)
			expiresAtStr := expiresAt.Format(pkg.DateLayoutDefault)
			params.ExpiresAt = expiresAtStr

		case "year":
			period := time.Duration(*params.Period) * time.Hour * 24 * 365
			expiresAt := now.Add(period)
			expiresAtStr := expiresAt.Format(pkg.DateLayoutDefault)
			params.ExpiresAt = expiresAtStr
		}
	}

	params.CompanyID = profile.ParentID
	resFee := make([]internal.Fee, 0, len(params.Fees))

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.s.repo.Create")
	}

	s.util.Logger.Info("VoucherService.Create.Fees", zap.Any("Params", params.Fees))

	if params.DiscountType != nil && *params.DiscountType == "custom" {

		defaultCurrency := s.util.Conf.Get("DEFAULT_CURRENCY")

		objectType := constant.TypeFeeVoucher
		isDefault := true

		for _, fee := range params.Fees {

			paramsFee := internal.CreateFee{
				CompanyID:        res.CompanyID,
				ObjectID:         res.ID,
				ObjectType:       &objectType,
				FeeType:          fee.FeeType,
				FeeValue:         fee.FeeValue,
				PGFeeType:        fee.PGFeeType,
				PGFeeValue:       fee.PGFeeValue,
				PlatformFeeType:  fee.PlatformFeeType,
				PlatformFeeValue: fee.PlatformFeeValue,
				PaymentMethod:    fee.PaymentMethod,
				IsDefault:        &isDefault,
				Currency:         &defaultCurrency,
				Status:           constant.StatusActive,
			}

			fee, err := s.feeRepo.Create(ctx, paramsFee)

			if err != nil {
				return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.s.feeRepo.Create")
			}

			resFee = append(resFee, fee)
		}
	}

	res.Fees = resFee

	return res, nil
}

func (s *VoucherService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *VoucherService) Update(ctx context.Context, id string, params internal.CreateVoucher, nexusData riot.Nexus) (_ internal.Voucher, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherService", "Update"))

	if !s.cb.Ready() {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterVoucher := map[string]interface{}{
		"id":         id,
		"company_id": profile.ParentID,
	}

	res, err := s.repo.GetByFilter(ctx, filterVoucher)

	if err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.s.repo.GetByFilter")
	}

	if err = params.ValidateUpdate(); err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.params.ValidateUpdate")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterVoucher, params)
	if err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.s.repo.Update")
	}

	return res, nil
}

func (s *VoucherService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Voucher, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterVoucher := map[string]interface{}{
		"id":         id,
		"company_id": profile.ParentID,
	}

	res, err := s.repo.GetByFilter(ctx, filterVoucher)
	if err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.s.repo.GetByID")
	}

	return res, nil
}

func (s *VoucherService) GetByCode(ctx context.Context, code string, includes []string, nexusData riot.Nexus) (_ internal.Voucher, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	parentID := ""

	profile := riot.GetNexusProfile(nexusData)
	websrvice := riot.GetNexusWebservice(nexusData)

	if profile.ParentID != "" {
		parentID = profile.ParentID
	} else {
		parentID = websrvice.ID
	}

	filterVoucher := map[string]interface{}{
		"code":       code,
		"company_id": parentID,
	}

	s.util.Logger.Info("filterVoucher", zap.Any("filterVoucher", filterVoucher))

	res, err := s.repo.GetByFilter(ctx, filterVoucher)
	if err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.s.repo.GetByID")
	}

	if err = res.ValidateExpire(); err != nil {
		return internal.Voucher{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "VoucherService.ValidateExpire")
	}

	return res, nil
}

func (s *VoucherService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.VoucherPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("VoucherService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.VoucherPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.VoucherPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "VoucherService.repo.GetAllWithPagination")
	}

	return res, nil
}
