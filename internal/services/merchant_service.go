package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type MerchantRepository interface {
	Create(ctx context.Context, params internal.CreateMerchant) (internal.Merchant, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateMerchant) (internal.Merchant, error)
	GetByID(ctx context.Context, id string) (internal.Merchant, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Merchant, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Merchant, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchant) (internal.Merchant, error)
}

type MerchantService struct {
	repo          MerchantRepository
	taskPublisher *rabbitmq.Publisher
	cb            *circuitbreaker.CircuitBreaker
	util          riot.Util
}

func NewMerchantService(util riot.Util, repo MerchantRepository, taskPublisher *rabbitmq.Publisher) *MerchantService {
	return &MerchantService{
		repo:          repo,
		util:          util,
		taskPublisher: taskPublisher,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *MerchantService) Create(ctx context.Context, params internal.CreateMerchant, nexusData riot.Nexus) (_ internal.Merchant, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantService", "Create"))

	if !s.cb.Ready() {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.CompanyID = profile.ParentID
	params.ProfileID = profile.ID
	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantService.Create.params.Validate")
	}

	filterMerchant := map[string]interface{}{
		"company_id": profile.ParentID,
		"profile_id": profile.ID,
		"status":     constant.TypeActive,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.Create.s.repo.GetByFilter")
	}

	if err := res.ValidateRegistered(); err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantService.Create.res.ValidateRegistered")
	}

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.s.repo.Create")
	}

	payload := internal.PayloadUpdateMerchant{
		MerchantID: res.ID,
		CompanyID:  res.CompanyID,
		ProfileID:  res.ProfileID,
	}

	if err := s.taskPublisher.PublishUpdateMerchant(ctx, "hecarim.exchange.backend", "hecarim.route.backend", payload); err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.s.taskPublisher.Publish")
	}

	if err := s.taskPublisher.PublishUpdateMerchant(ctx, "fame.exchange.backend", "fame.route.backend", payload); err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.s.taskPublisher.Publish")
	}

	return res, nil
}

func (s *MerchantService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *MerchantService) Update(ctx context.Context, id string, params internal.CreateMerchant, nexusData riot.Nexus) (_ internal.Merchant, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantService", "Update"))

	if !s.cb.Ready() {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
		"status":     constant.TypeActive,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.s.repo.Update")
	}

	return res, nil
}

func (s *MerchantService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Merchant, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantService.s.repo.GetByID")
	}

	return res, nil
}

func (s *MerchantService) GetByProfileID(ctx context.Context, profileID string, includes []string, nexusData riot.Nexus) (_ internal.Merchant, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	filterMerchant := map[string]interface{}{
		"profile_id": profileID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Merchant{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantService.s.repo.GetByID")
	}

	return res, nil
}

func (s *MerchantService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.MerchantPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	groupID := urlValues.Get("group_id")

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.MerchantPagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.MerchantPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if groupID != "" {
		objectID, err := primitive.ObjectIDFromHex(groupID)
		if err != nil {
			return internal.MerchantPagin{}, err
		}
		filterMap["group_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.MerchantPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantService.repo.GetAllWithPagination")
	}

	return res, nil
}
