package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type CoinDisbursementRepository interface {
	Create(ctx context.Context, params internal.CreateCoinDisbursement) (internal.CoinDisbursement, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateCoinDisbursement) (internal.CoinDisbursement, error)
	GetByID(ctx context.Context, id string) (internal.CoinDisbursement, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.CoinDisbursement, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.CoinDisbursementPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.CoinDisbursement, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateCoinDisbursement) (internal.CoinDisbursement, error)
}

type CoinDisbursementService struct {
	repo CoinDisbursementRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewCoinDisbursementService(util riot.Util, repo CoinDisbursementRepository) *CoinDisbursementService {
	return &CoinDisbursementService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *CoinDisbursementService) Create(ctx context.Context, params internal.CreateCoinDisbursement, nexusData riot.Nexus) (_ internal.CoinDisbursement, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementService", "Create"))

	if !s.cb.Ready() {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinDisbursementService.params.Validate")
	}

	filter := map[string]interface{}{
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementService.s.repo.GetByFilter")
	}

	if err := res.ValidateRegistered(); err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinDisbursementService.res.ValidateRegistered")
	}

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementService.s.repo.Create")
	}

	return res, nil
}

func (s *CoinDisbursementService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinDisbursementService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *CoinDisbursementService) Update(ctx context.Context, id string, params internal.CreateCoinDisbursement, nexusData riot.Nexus) (_ internal.CoinDisbursement, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementService", "Update"))

	if !s.cb.Ready() {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinDisbursementService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementService.s.repo.Update")
	}

	return res, nil
}

func (s *CoinDisbursementService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.CoinDisbursement, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementService", "GetByID"))

	if !s.cb.Ready() {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.CoinDisbursement{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "CoinDisbursementService.s.repo.GetByID")
	}

	return res, nil
}

func (s *CoinDisbursementService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.CoinDisbursementPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("CoinDisbursementService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.CoinDisbursementPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.CoinDisbursementPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "CoinDisbursementService.repo.GetAllWithPagination")
	}

	return res, nil
}
