package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type BankRepository interface {
	Create(ctx context.Context, params internal.CreateBank) (internal.Bank, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateBank) (internal.Bank, error)
	GetByID(ctx context.Context, id string) (internal.Bank, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Bank, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.BankPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Bank, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateBank) (internal.Bank, error)
	CreateBulk(ctx context.Context, params []internal.CreateBank) ([]internal.Bank, error)
}

type BankService struct {
	repo BankRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewBankService(util riot.Util, repo BankRepository) *BankService {
	return &BankService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *BankService) Create(ctx context.Context, params internal.CreateBank, nexusData riot.Nexus) (_ internal.Bank, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BankService", "Create"))

	if !s.cb.Ready() {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "BankService.params.Validate")
	}

	filter := map[string]interface{}{
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BankService.s.repo.GetByFilter")
	}

	if err := res.ValidateRegistered(); err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "BankService.res.ValidateRegistered")
	}

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BankService.s.repo.Create")
	}

	return res, nil
}

func (s *BankService) CreateBulk(ctx context.Context, params []internal.CreateBank, nexusData riot.Nexus) ([]internal.Bank, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BankService", "CreateBulk"))

	// Prepare payloads
	payloads := []internal.CreateBank{}
	for _, param := range params {
		if param.Status == "" {
			param.Status = constant.StatusActive
		}

		// Validate parameters
		if err := param.Validate(); err != nil {
			return []internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "BankService.CreateBulk.params.Validate")
		}

		payloads = append(payloads, param)
	}

	// Create user themes
	res, err := s.repo.CreateBulk(ctx, payloads)
	if err != nil {
		return []internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BankService.CreateBulk.repo.Create")
	}

	return res, nil
}

func (s *BankService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BankService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BankService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "BankService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *BankService) Update(ctx context.Context, id string, params internal.CreateBank, nexusData riot.Nexus) (_ internal.Bank, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BankService", "Update"))

	if !s.cb.Ready() {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BankService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "BankService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BankService.s.repo.Update")
	}

	return res, nil
}

func (s *BankService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Bank, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BankService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BankService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Bank{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "BankService.s.repo.GetByID")
	}

	return res, nil
}

func (s *BankService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.BankPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("BankService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.BankPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.BankPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "BankService.repo.GetAllWithPagination")
	}

	return res, nil
}
