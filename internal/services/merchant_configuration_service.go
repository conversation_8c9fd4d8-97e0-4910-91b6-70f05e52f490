package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type MerchantConfigurationService struct {
	marchantRepo        MerchantRepository
	merchantFeeRepo     MerchantPlatformFeeRepository
	merchantVoucherRepo MerchantVoucherRepository
	configRepo          ConfigurationRepository
	feeRepo             FeeRepository
	cb                  *circuitbreaker.CircuitBreaker
	util                riot.Util
}

func NewMerchantConfigurationService(util riot.Util, marchantRepo MerchantRepository, merchantFeeRepo MerchantPlatformFeeRepository, merchantVoucherRepo MerchantVoucherRepository, configRepo ConfigurationRepository, feeRepo FeeRepository) *MerchantConfigurationService {
	return &MerchantConfigurationService{
		marchantRepo:        marchantRepo,
		merchantFeeRepo:     merchantFeeRepo,
		merchantVoucherRepo: merchantVoucherRepo,
		configRepo:          configRepo,
		feeRepo:             feeRepo,
		util:                util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *MerchantConfigurationService) GetAllWithPagination(ctx context.Context, identifier internal.Identifier, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantConfigurationPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantConfigurationService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	useVoucher := false
	isDiscount := false
	voucherID := ""
	voucherCode := ""
	feeDiscountType := "percentage"
	feeDiscountValue := 0.0
	var expiresAt *time.Time

	if err := identifier.ValidateMarchantID(); err != nil {
		return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantConfigurationService.GetAllWithPagination.identifier.Validate")
	}

	//filter merchant
	filterMerchant := map[string]interface{}{
		"id":         identifier.MerchantID,
		"company_id": profile.ParentID,
	}

	merchant, err := s.marchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantConfigurationService.GetAllWithPagination.marchantRepo.GetByFilter", zap.Error(err))
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantConfigurationService.GetAllWithPagination.merchant.ValidateNotExist", zap.Error(err))
	}

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.MerchantConfigurationPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.configRepo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantConfigurationService.repo.GetAllWithPagination")
	}

	var merchantConfigs []internal.MerchantConfiguration

	if len(res.Records) > 0 {

		// convert to internal type
		for _, record := range res.Records {

			if record.Name == constant.TypeFeatureTip {
				//find merchant platform fee
				filterMerchantPf := map[string]interface{}{
					"merchant_id":  identifier.MerchantID,
					"company_id":   profile.ParentID,
					"product_type": constant.TypeFeatureTip,
				}

				merchantPf, err := s.merchantFeeRepo.GetByFilter(ctx, filterMerchantPf)
				if err != nil {
					return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.Create")
				}

				if err := merchantPf.ValidateNotExist(); err != nil {
					return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.Create.merchantPf.ValidateNotExist")
				}

				s.util.Logger.Info("MerchantConfigurationService.GetAllWithPagination", zap.Any("merchantPf", merchantPf))

				builder := mongorm.NewBuilder()
				filterMap, ok := builder.Filter.(map[string]interface{})
				if !ok {
					filterMap = make(map[string]interface{})
				}

				if identifier.MerchantID != "" {
					objectID, err := primitive.ObjectIDFromHex(identifier.MerchantID)
					if err != nil {
						return internal.MerchantConfigurationPagin{}, err
					}
					filterMap["merchant_id"] = objectID
				}

				filterMap["product_type"] = constant.TypeFeatureTip
				filterMap["status"] = constant.TypeActive

				builder.SortField = "created_at"
				builder.SortOrder = -1

				merchantVoucher, err := s.merchantVoucherRepo.GetAllWithPagination(ctx, *builder)
				if err != nil {
					return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantConfigurationService.GetAllWithPagination.merchantVoucherRepo.GetAllWithPagination")
				}

				if merchantVoucher.TotalRecords > 0 {

					now := time.Now()

					for _, voucher := range merchantVoucher.Records {

						s.util.Logger.Info("MerchantConfigurationService.GetAllWithPagination.voucher.ExpiresAt.Before(now)", zap.Any("Info", voucher.ExpiresAt.Before(now)))

						if voucher.ExpiresAt.After(now) {
							useVoucher = true
							voucherID = voucher.ID
							expiresAt = voucher.ExpiresAt
							voucherCode = voucher.Code
							// params.Fees = voucher.Fees
							break
						} else {
							//deleted
							s.util.Logger.Info("MerchantConfigurationService.GetAllWithPagination.voucher.ExpiresAt.Before(now) false")
							//soft delete
							err = s.merchantVoucherRepo.SoftDelete(ctx, voucher.ID)
							if err != nil {
								return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantConfigurationService.merchantVoucherRepo.SoftDelete")
							}
						}
					}

				}

				filterFee := map[string]interface{}{}

				if useVoucher {
					filterFee["object_type"] = constant.TypeFeeVoucher
					filterFee["object_id"] = voucherID
				} else {
					filterFee["object_type"] = constant.TypeFeeMerchant
					filterFee["object_id"] = merchantPf.ID
				}

				fee, err := s.feeRepo.GetByFilter(ctx, filterFee)
				if err != nil {
					return internal.MerchantConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.Create.feeRepo.GetByFilter")
				}

				feeDiscountType = fee.FeeType
				feeDiscountValue = fee.FeeValue

				if feeDiscountValue != record.FeeValue {
					isDiscount = true
				}

				merchantConfigs = append(merchantConfigs, internal.MerchantConfiguration{
					ID:               record.ID,
					MerchantID:       identifier.MerchantID,
					CompanyID:        profile.ParentID,
					Name:             record.Name,
					DisplayName:      record.DisplayName,
					Description:      record.Description,
					Slug:             record.Slug,
					FeeType:          record.FeeType,
					FeeValue:         record.FeeValue,
					FeeDiscountType:  feeDiscountType,
					FeeDiscountValue: feeDiscountValue,
					Currency:         record.Currency,
					Status:           record.Status,
					IsDiscount:       isDiscount,
					IsVoucher:        useVoucher,
					VoucherCode:      voucherCode,
					Metadata:         record.Metadata,
					CreatedAt:        record.CreatedAt,
					UpdatedAt:        record.UpdatedAt,
					ExpiresAt:        expiresAt,
				})
			} else {
				merchantConfigs = append(merchantConfigs, internal.MerchantConfiguration{
					ID:               record.ID,
					MerchantID:       identifier.MerchantID,
					CompanyID:        profile.ParentID,
					Name:             record.Name,
					DisplayName:      record.DisplayName,
					Description:      record.Description,
					Slug:             record.Slug,
					FeeType:          record.FeeType,
					FeeValue:         record.FeeValue,
					FeeDiscountType:  record.FeeType,
					FeeDiscountValue: record.FeeValue,
					Currency:         record.Currency,
					Status:           record.Status,
					IsDiscount:       isDiscount,
					VoucherCode:      voucherCode,
					IsVoucher:        useVoucher,
					Metadata:         record.Metadata,
					CreatedAt:        record.CreatedAt,
					UpdatedAt:        record.UpdatedAt,
					ExpiresAt:        expiresAt,
				})
			}

		}
	}
	return internal.MerchantConfigurationPagin{
		Limit:        res.Limit,
		Page:         res.Page,
		Sort:         res.Sort,
		TotalRecords: res.TotalRecords,
		TotalPages:   res.TotalPages,
		Records:      merchantConfigs,
	}, nil
}
