package services

import (
	"context"
	"net/url"
	"strings"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type MerchantPlatformRepository interface {
	Create(ctx context.Context, params internal.CreateMerchantPlatform) (internal.MerchantPlatform, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateMerchantPlatform) (internal.MerchantPlatform, error)
	GetByID(ctx context.Context, id string) (internal.MerchantPlatform, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.MerchantPlatform, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.MerchantPlatformPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.MerchantPlatform, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateMerchantPlatform) (internal.MerchantPlatform, error)
}

type MerchantPlatformService struct {
	repo MerchantPlatformRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewMerchantPlatformService(util riot.Util, repo MerchantPlatformRepository) *MerchantPlatformService {
	return &MerchantPlatformService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *MerchantPlatformService) Create(ctx context.Context, params internal.CreateMerchantPlatform, nexusData riot.Nexus) (_ internal.MerchantPlatform, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformService", "Create"))

	if !s.cb.Ready() {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.CompanyID = profile.ParentID
	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformService.params.Validate")
	}

	filterMerchant := map[string]interface{}{
		"company_id": profile.ParentID,
		"id":         params.MerchantID,
	}

	merchant, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformService.s.repo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformService.s.repo.GetByFilter")
	}

	params.MerchantID = merchant.ID
	params.CompanyID = merchant.CompanyID
	params.ProfileID = profile.ID

	name := strings.ToUpper(strings.ReplaceAll(*params.DisplayName, " ", "_"))
	params.Name = &name

	filter := map[string]interface{}{
		"company_id": profile.ParentID,
		"name":       name,
		"status":     constant.TypeActive,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformService.s.repo.GetByFilter")
	}

	s.util.Logger.Info("MerchantPlatformService.s.repo.GetByFilter", zap.Any("res", res))

	if err := res.ValidateRegistered(); err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformService.res.ValidateRegistered")
	}

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformService.s.repo.Create")
	}

	return res, nil
}

func (s *MerchantPlatformService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *MerchantPlatformService) Update(ctx context.Context, id string, params internal.CreateMerchantPlatform, nexusData riot.Nexus) (_ internal.MerchantPlatform, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformService", "Update"))

	if !s.cb.Ready() {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformService.s.repo.Update")
	}

	return res, nil
}

func (s *MerchantPlatformService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.MerchantPlatform, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformService", "GetByID"))

	if !s.cb.Ready() {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.MerchantPlatform{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MerchantPlatformService.s.repo.GetByID")
	}

	return res, nil
}

func (s *MerchantPlatformService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.MerchantPlatformPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("MerchantPlatformService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.MerchantPlatformPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.MerchantPlatformPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "MerchantPlatformService.repo.GetAllWithPagination")
	}

	return res, nil
}
