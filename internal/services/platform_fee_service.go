package services

import (
	"context"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type PlatformFeeRepository interface {
	Create(ctx context.Context, params internal.CreatePlatformFee) (internal.PlatformFee, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreatePlatformFee) (internal.PlatformFee, error)
	GetByID(ctx context.Context, id string) (internal.PlatformFee, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.PlatformFee, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.PlatformFeePagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.PlatformFee, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreatePlatformFee) (internal.PlatformFee, error)
}

type FeeRepository interface {
	Create(ctx context.Context, params internal.CreateFee) (internal.Fee, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateFee) (internal.Fee, error)
	GetByID(ctx context.Context, id string) (internal.Fee, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Fee, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.FeePagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Fee, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateFee) (internal.Fee, error)
}

type PlatformFeeService struct {
	repo    PlatformFeeRepository
	feeRepo FeeRepository
	cb      *circuitbreaker.CircuitBreaker
	util    riot.Util
}

func NewPlatformFeeService(util riot.Util, repo PlatformFeeRepository, feeRepo FeeRepository) *PlatformFeeService {
	return &PlatformFeeService{
		repo:    repo,
		feeRepo: feeRepo,
		util:    util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *PlatformFeeService) Create(ctx context.Context, params internal.CreatePlatformFee, nexusData riot.Nexus) (_ internal.PlatformFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeService", "Create"))

	if !s.cb.Ready() {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	isDefault := true
	profile := riot.GetNexusProfile(nexusData)

	switch profile.Role {
	case riot.RoleCompany:
		params.CompanyID = profile.ID
	case riot.RoleCustomer:
		params.CompanyID = profile.ParentID
	}

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeService.params.Validate")
	}

	resFee := make([]internal.Fee, 0, len(params.Fees))
	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeService.s.repo.Create")
	}

	for _, fee := range params.Fees {

		platformFeeType := constant.TypeFeePlatform

		feeParams := internal.CreateFee{
			CompanyID:     params.CompanyID,
			PaymentMethod: fee.PaymentMethod,
			ObjectType:    &platformFeeType,
			ObjectID:      res.ID,
			IsDefault:     &isDefault,
			Status:        constant.StatusActive,
		}

		if err = feeParams.Validate(); err != nil {
			return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeService.feeParams.Validate")
		}

		paymentMethod := *feeParams.PaymentMethod

		if strings.EqualFold(paymentMethod, constant.PaymentMethodQRCode) {

			persentage := "percentage"

			feePctStr := s.util.Conf.Get("DEFAULT_QR_PLATFORM_FEE")

			feePct, err := strconv.ParseFloat(feePctStr, 64)
			if err != nil {
				return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeService.feeParams.feePct.ParseFloat")
			}

			paymentPctStr := s.util.Conf.Get("DEFAULT_QR_PAYMENT")

			paymentPct, err := strconv.ParseFloat(paymentPctStr, 64)
			if err != nil {
				return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeService.feeParams.paymentPct.ParseFloat")
			}

			defaultCurrency := s.util.Conf.Get("DEFAULT_CURRENCY")

			feeType := persentage
			feeValue := feePct
			pgType := persentage
			pgValue := paymentPct
			platformType := persentage
			platformValue := feePct - paymentPct

			feeParams.FeeType = &feeType
			feeParams.FeeValue = &feeValue
			feeParams.PGFeeType = &pgType
			feeParams.PGFeeValue = &pgValue
			feeParams.PlatformFeeType = &platformType
			feeParams.PlatformFeeValue = &platformValue
			feeParams.Currency = &defaultCurrency

		}

		fee, err := s.feeRepo.Create(ctx, feeParams)
		if err != nil {
			return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeService.s.feeRepo.Create")
		}

		resFee = append(resFee, fee)
	}

	res.Fees = resFee

	return res, nil
}

func (s *PlatformFeeService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *PlatformFeeService) Update(ctx context.Context, id string, params internal.CreatePlatformFee, nexusData riot.Nexus) (_ internal.PlatformFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeService", "Update"))

	if !s.cb.Ready() {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeService.s.repo.Update")
	}

	return res, nil
}

func (s *PlatformFeeService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.PlatformFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeService", "GetByID"))

	if !s.cb.Ready() {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.PlatformFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PlatformFeeService.s.repo.GetByID")
	}

	return res, nil
}

func (s *PlatformFeeService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.PlatformFeePagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PlatformFeeService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.PlatformFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	groupID := urlValues.Get("group_id")

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.PlatformFeePagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.PlatformFeePagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if groupID != "" {
		objectID, err := primitive.ObjectIDFromHex(groupID)
		if err != nil {
			return internal.PlatformFeePagin{}, err
		}
		filterMap["group_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.PlatformFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PlatformFeeService.repo.GetAllWithPagination")
	}

	return res, nil
}
