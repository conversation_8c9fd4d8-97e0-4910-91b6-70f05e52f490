package services

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/rabbitmq"

	"github.com/continue-team/manaslu/internal/xendit"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type PaymentRepository interface {
	Create(ctx context.Context, params internal.CreatePayment) (internal.Payment, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreatePayment) (internal.Payment, error)
	GetByID(ctx context.Context, id string) (internal.Payment, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Payment, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.PaymentPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Payment, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreatePayment) (internal.Payment, error)
	GetByFilterWithOption(ctx context.Context, filter map[string]interface{}) (internal.Payment, error)
}

type PaymentService struct {
	repo                PaymentRepository
	merchantRepo        MerchantRepository
	merchantPfRepo      MerchantPlatformFeeRepository
	merchantVoucherRepo MerchantVoucherRepository
	transactionRepo     TransactionRepository
	voucherRepo         VoucherRepository
	featureFeeRepo      FeatureFeeRepository
	feeRepo             FeeRepository
	paymentMethodRepo   PaymentMethodRepository
	taskPublisher       *rabbitmq.Publisher
	cb                  *circuitbreaker.CircuitBreaker
	util                riot.Util
}

func NewPaymentService(util riot.Util, repo PaymentRepository, merchantRepo MerchantRepository, merchantPfRepo MerchantPlatformFeeRepository, transactionRepo TransactionRepository, merchantVoucherRepo MerchantVoucherRepository, voucherRepo VoucherRepository, featureFeeRepo FeatureFeeRepository, feeRepo FeeRepository, paymentMethodRepo PaymentMethodRepository, publisher *rabbitmq.Publisher) *PaymentService {
	return &PaymentService{
		repo:                repo,
		merchantRepo:        merchantRepo,
		merchantPfRepo:      merchantPfRepo,
		merchantVoucherRepo: merchantVoucherRepo,
		transactionRepo:     transactionRepo,
		voucherRepo:         voucherRepo,
		featureFeeRepo:      featureFeeRepo,
		feeRepo:             feeRepo,
		paymentMethodRepo:   paymentMethodRepo,
		taskPublisher:       publisher,
		util:                util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *PaymentService) Create(ctx context.Context, params internal.CreatePayment, nexusData riot.Nexus) (_ internal.Payment, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentService", "Create"))

	profile := riot.GetNexusProfile(nexusData)
	webservice := riot.GetNexusWebservice(nexusData)

	if profile.ID != "" {
		switch profile.Role {
		case riot.RoleCompany:
			params.CompanyID = profile.ID
		case riot.RoleCustomer:
			params.CompanyID = profile.ParentID
		}
	} else if webservice.ID != "" {
		params.CompanyID = webservice.ID
	}

	if err = params.Validate(); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.params.Validate")
	}

	//find transaction
	filterTransaction := map[string]interface{}{
		"id": params.TransactionID,
	}

	transaction, err := s.transactionRepo.GetByFilter(ctx, filterTransaction)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.Create.transactionRepo.GetByFilter")
	}

	if err := transaction.ValidateNotExist(); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.Create.transactionRepo.GetByFilter")
	}

	//find mechant
	filterMerchant := map[string]interface{}{
		"id":     transaction.MerchantID,
		"status": constant.TypeActive,
	}

	merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.Create.merchantRepo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.Create.merchant.ValidateNotExist")
	}

	uniqueNo := pkg.GenerateUniqueNo()
	paymentNo := fmt.Sprintf("%s-%s", "PAY", uniqueNo)
	externalID := pkg.GenerateExternalID(transaction.TransactionNo)

	params.UniqueNo = &uniqueNo
	params.PaymentNo = &paymentNo

	if params.ExternalID != nil && *params.ExternalID != "" {
		externalID = *params.ExternalID
	} else {
		params.ExternalID = &externalID
	}

	paymentMethod := ""

	if params.PaymentMethod != nil && *params.PaymentMethod != "" {
		paymentMethod = strings.ToUpper(*params.PaymentMethod)
	}

	switch paymentMethod {
	case constant.PaymentMethodQRCode:

		switch transaction.ProductType {
		case constant.TypeFeatureTip:
			return s.CreatePayTip(ctx, paymentMethod, params, merchant, transaction, nexusData)
		case constant.TypeFeatureVoucherGame, constant.TypeFeatureCoin, constant.TypeFeatureSubcription:
			return s.CreatePayCharge(ctx, paymentMethod, params, merchant, transaction, nexusData)
		}

	case constant.PaymentMethodEwallet, constant.PaymentMethodCreditCard:
		return s.CreatePayCharge(ctx, paymentMethod, params, merchant, transaction, nexusData)

	}

	return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, errors.New("payment method not found"), riot.ErrorCodeUnknown, "PaymentService.Create.payment method not found")
}

func (s *PaymentService) CreatePayTip(ctx context.Context, paymentMethodType string, params internal.CreatePayment, merchant internal.Merchant, transaction internal.Transaction, nexusData riot.Nexus) (_ internal.Payment, err error) {
	// define Logger
	s.util.Logger.Info("PaymentService.CreatePayTip", zap.Any("paymentMethodType", paymentMethodType), zap.Any("params", params), zap.Any("merchant", merchant), zap.Any("transaction", transaction))

	voucherID := ""
	useVoucher := false

	updateTransaction := internal.CreateTransaction{
		Status: constant.StatusTransactionUnpaid,
	}

	params.Status = constant.StatusTransactionUnpaid

	//find merchant platform fee
	filterMerchantPf := map[string]interface{}{
		"merchant_id":  transaction.MerchantID,
		"company_id":   params.CompanyID,
		"product_type": strings.ToUpper(transaction.ProductType),
	}

	merchantPf, err := s.merchantPfRepo.GetByFilter(ctx, filterMerchantPf)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayTip.merchantPfRepo.GetByFilter")
	}

	if err := merchantPf.ValidateNotExist(); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.CreatePayTip.merchantPfRepo.ValidateNotExist")
	}

	builder := mongorm.NewBuilder()
	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if transaction.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(transaction.MerchantID)
		if err != nil {
			return internal.Payment{}, err
		}
		filterMap["merchant_id"] = objectID
	}

	filterMap["product_type"] = strings.ToUpper(transaction.ProductType)
	filterMap["status"] = constant.TypeActive

	builder.SortField = "created_at"
	builder.SortOrder = -1

	merchantVoucher, err := s.merchantVoucherRepo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.CreatePayTip.merchantVoucherRepo.GetAllWithPagination")
	}

	s.util.Logger.Info("PaymentService.CreatePayTip", zap.Any("merchantVoucher", merchantVoucher))

	if merchantVoucher.TotalRecords > 0 {

		now := time.Now()

		for _, voucher := range merchantVoucher.Records {

			s.util.Logger.Info("PaymentService.CreatePayTip.voucher.ExpiresAt.Before(now)", zap.Any("Info", voucher.ExpiresAt.Before(now)))

			if voucher.ExpiresAt.After(now) {
				useVoucher = true
				params.VoucherID = voucher.ID
				params.UseVoucher = &useVoucher
				params.VoucherCode = &voucher.Code

				updateTransaction.VoucherID = &voucher.ID
				updateTransaction.UseVoucher = &useVoucher
				updateTransaction.VoucherCode = &voucher.Code

				voucherID = voucher.ID
				// params.Fees = voucher.Fees
				break
			} else {
				//deleted
				s.util.Logger.Info("PaymentService.CreatePayTip.voucher.ExpiresAt.Before(now) false")
				//soft delete
				err = s.merchantVoucherRepo.SoftDelete(ctx, voucher.ID)
				if err != nil {
					return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayTip.repo.GetByFilter")
				}
			}
		}

	}

	switch paymentMethodType {

	case constant.PaymentMethodQRCode:

		xend := xendit.NewXendit(s.util.Conf.Get("XENDIT_API_KEY"), s.util, params.CompanyID, transaction.ID, *params.ExternalID, s.taskPublisher)
		expiresAt := time.Now().UTC().Add(30 * time.Minute)
		params.ExpiresAt = &expiresAt

		resp, err := xend.CreateQRCode(*params.ExternalID, transaction.TotalPayment, expiresAt.Format(time.RFC3339))

		if err != nil {
			return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayTip.xend.CreateQRCode")
		}

		params.QRString = &resp.QRString
		params.ProviderPaymentID = &resp.ID
		params.ProviderBusinessID = &resp.BusinessID
		params.PaymentChannel = &resp.ChannelCode
		params.PaymentType = &resp.Type

		filterFee := map[string]interface{}{}

		if useVoucher {
			filterFee["object_type"] = constant.TypeFeeVoucher
			filterFee["object_id"] = voucherID
		} else {
			filterFee["object_type"] = constant.TypeFeeMerchant
			filterFee["object_id"] = merchantPf.ID
		}

		fee, err := s.feeRepo.GetByFilter(ctx, filterFee)
		if err != nil {
			return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayTip.feeRepo.GetByFilter")
		}

		updateTransaction.Fee = &internal.CreateFee{
			CompanyID:         fee.CompanyID,
			ProfileID:         fee.ProfileID,
			ObjectID:          fee.ObjectID,
			ObjectType:        &fee.ObjectType,
			PaymentMethod:     &fee.PaymentMethod,
			FeeType:           &fee.FeeType,
			FeeValue:          &fee.FeeValue,
			PGFeeType:         &fee.PGFeeType,
			PGFeeValue:        &fee.PGFeeValue,
			ProviderFeeType:   &fee.ProviderFeeType,
			ProviderFeeValue:  &fee.ProviderFeeValue,
			PlatformFeeType:   &fee.PlatformFeeType,
			PlatformFeeValue:  &fee.PlatformFeeValue,
			ReferralFeeType:   &fee.ReferralFeeType,
			ReferralFeeValue:  &fee.ReferralFeeValue,
			MarkeringFeeType:  &fee.MarkeringFeeType,
			MarkeringFeeValue: &fee.MarkeringFeeValue,
			IsDefault:         &fee.IsDefault,
			Currency:          &fee.Currency,
			Status:            fee.Status,
		}

	}

	metadata := map[string]interface{}{
		"buyer_name":   transaction.BuyerName,
		"buyer_email":  transaction.BuyerEmail,
		"product_type": transaction.ProductType,
	}

	params.Amount = &transaction.TotalPayment
	params.CompanyID = merchant.CompanyID
	params.ProfileID = merchant.ProfileID
	params.ReferenceNo = &transaction.ReferenceNo
	params.TransactionNo = &transaction.TransactionNo
	params.MerchantID = transaction.MerchantID
	params.ReferrerID = transaction.ReferrerID
	params.VoucherID = transaction.VoucherID
	params.VoucherCode = &transaction.VoucherCode
	params.Metadata = &metadata

	s.util.Logger.Info("PaymentService.CreatePayTip", zap.Any("params", params))

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayTip.repo.Create")
	}

	if _, err := s.transactionRepo.Update(ctx, transaction.ID, updateTransaction); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayTip.transactionRepo.UpdateByFilter")
	}

	return res, nil
}

func (s *PaymentService) CreatePayCharge(ctx context.Context, paymentMethodType string, params internal.CreatePayment, merchant internal.Merchant, transaction internal.Transaction, nexusData riot.Nexus) (_ internal.Payment, err error) {

	s.util.Logger.Info("PaymentService.CreatePayCharge", zap.Any("paymentMethodType", paymentMethodType), zap.Any("params", params), zap.Any("merchant", merchant), zap.Any("transaction", transaction))

	pgFee, pgRealFee, pGPlatformFee, pgExtraFee, extraFee := float64(0), float64(0), float64(0), float64(0), float64(0)
	amountCharge := float64(0)

	s.util.Logger.Info("PaymentService.CreatePayCharge", zap.Any("pgExtraFee", pgExtraFee), zap.Any("extraFee", extraFee))

	expiresAt := time.Now().UTC().Add(30 * time.Minute)
	params.ExpiresAt = &expiresAt

	var respCard *xendit.CreditCardChargeResponse

	updateTransaction := internal.CreateTransaction{
		Status: constant.StatusTransactionUnpaid,
	}

	params.Status = constant.StatusTransactionUnpaid

	if err = params.Validate(); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.CreatePayCharge.Validate")
	}

	paymentMethod := internal.PaymentMethod{}
	paymentMethodEwallet := constant.PaymentMethodEwallet

	filterPaymentMethod := map[string]interface{}{
		"company_id": transaction.CompanyID,
		"status":     constant.TypeActive,
	}

	mapPaymentMethods := map[string]internal.PaymentMethod{}
	paymentMethods, err := s.paymentMethodRepo.GetAll(ctx, filterPaymentMethod)

	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.CreatePayCharge.paymentMethodRepo.GetAll")
	}

	for _, pm := range paymentMethods {
		mapPaymentMethods[pm.ChannelCode] = pm
	}

	if err = params.ValidatePayCharge(); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.CreatePayCharge.ValidateEwallet")
	}

	paymentMethod = mapPaymentMethods[strings.ToUpper(*params.ChannelCode)]

	s.util.Logger.Info("PaymentService.CreatePayCharge.Wallet", zap.Any("paymentMethod", paymentMethod))

	metadata := map[string]interface{}{
		"buyer_name":   transaction.BuyerName,
		"buyer_email":  transaction.BuyerEmail,
		"product_type": transaction.ProductType,
	}

	switch paymentMethodType {

	case constant.PaymentMethodQRCode:

		xend := xendit.NewXendit(s.util.Conf.Get("XENDIT_API_KEY"), s.util, params.CompanyID, transaction.ID, *params.ExternalID, s.taskPublisher)

		if paymentMethod.FeeType == constant.FeeTypePercentage {
			pgFee = pkg.CalculatePercentage(transaction.Amount, paymentMethod.FeeValue)
		} else if paymentMethod.FeeType == constant.FeeTypeFixed {
			pgFee = paymentMethod.FeeValue
		}

		if paymentMethod.PGFeeType == constant.FeeTypePercentage {
			pgRealFee = pkg.CalculatePercentage(transaction.Amount, paymentMethod.PGFeeValue)
		} else if paymentMethod.PGFeeType == constant.FeeTypeFixed {
			pgRealFee = paymentMethod.PGFeeValue
		}

		pGPlatformFee = pgFee - pgRealFee
		amountCharge = transaction.TotalPayment + pgFee

		resp, err := xend.CreateQRCode(*params.ExternalID, amountCharge, expiresAt.Format(time.RFC3339))

		if err != nil {
			return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayCharge.xend.CreateQRCode")
		}

		params.QRString = &resp.QRString
		params.ChargeAmount = &amountCharge
		params.PGFee = &pgFee
		params.PGRealFee = &pgRealFee
		params.PGPlatformFee = &pGPlatformFee
		params.ProviderBusinessID = &resp.BusinessID
		params.ProviderPaymentID = &resp.ID
		params.PaymentChannel = &resp.ChannelCode
		params.PaymentMethod = &paymentMethodType
		params.PaymentType = &paymentMethodType
		params.Currency = &resp.Currency

	case constant.PaymentMethodEwallet:
		paramsEwallet := xendit.EwalletRequest{
			ReferenceID:    *params.ExternalID,
			Currency:       "IDR",
			Amount:         transaction.Amount,
			CheckoutMethod: paymentMethod.CheckoutMethod,
			Metadata:       metadata,
		}

		switch *params.ChannelCode {
		case constant.ChannelCodeOVO:

			if err = params.ValidateEwalletOVO(); err != nil {
				return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.CreatePayCharge.ValidateEwalletOVO")
			}

			paramsEwallet.ChannelCode = constant.ChannelCodeOVO
			paramsEwallet.ChannelProps = xendit.EwalletChannelProperties{
				MobileNumber:       *params.MobileNumber,
				SuccessRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.SuccessRedirectURL, transaction.ReferenceNo),
				// FailureRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.FailureRedirectURL, transaction.ReferenceNo),
			}

		case constant.ChannelCodeShopeePay:

			paramsEwallet.ChannelCode = constant.ChannelCodeShopeePay
			paramsEwallet.ChannelProps = xendit.EwalletChannelProperties{
				SuccessRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.SuccessRedirectURL, transaction.ReferenceNo),
				// FailureRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.FailureRedirectURL, transaction.ReferenceNo),
			}

		case constant.ChannelCodeDana:

			paramsEwallet.ChannelCode = constant.ChannelCodeDana
			paramsEwallet.ChannelProps = xendit.EwalletChannelProperties{
				SuccessRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.SuccessRedirectURL, transaction.ReferenceNo),
				// FailureRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.FailureRedirectURL, transaction.ReferenceNo),
			}

		case constant.ChannelCodeLinkAja:

			paramsEwallet.ChannelCode = constant.ChannelCodeLinkAja
			paramsEwallet.ChannelProps = xendit.EwalletChannelProperties{
				SuccessRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.SuccessRedirectURL, transaction.ReferenceNo),
				// FailureRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.FailureRedirectURL, transaction.ReferenceNo),
			}

		case constant.ChannelCodeAstraPay:

			paramsEwallet.ChannelCode = constant.ChannelCodeAstraPay
			paramsEwallet.ChannelProps = xendit.EwalletChannelProperties{
				SuccessRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.SuccessRedirectURL, transaction.ReferenceNo),
				FailureRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.FailureRedirectURL, transaction.ReferenceNo),
			}

		case constant.ChannelCodeJenius:

			if err = params.ValidateEwalletJenius(); err != nil {
				return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.CreatePayCharge.ValidateEwalletJenius")
			}

			paramsEwallet.ChannelCode = constant.ChannelCodeJenius

			paramsEwallet.ChannelProps = xendit.EwalletChannelProperties{
				CashTag:            *params.CashTag,
				SuccessRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.SuccessRedirectURL, transaction.ReferenceNo),
				// FailureRedirectURL: fmt.Sprintf("%s/%s", paymentMethod.FailureRedirectURL, transaction.ReferenceNo),
			}

		default:
			return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.CreatePayCharge.ValidateEwallet")
		}

		if paymentMethod.FeeType == constant.FeeTypePercentage {
			pgFee = pkg.CalculatePercentage(transaction.Amount, paymentMethod.FeeValue)
		} else if paymentMethod.FeeType == constant.FeeTypeFixed {
			pgFee = paymentMethod.FeeValue
		}

		if paymentMethod.PGFeeType == constant.FeeTypePercentage {
			pgRealFee = pkg.CalculatePercentage(transaction.Amount, paymentMethod.PGFeeValue)
		} else if paymentMethod.PGFeeType == constant.FeeTypeFixed {
			pgRealFee = paymentMethod.PGFeeValue
		}

		pGPlatformFee = pgFee - pgRealFee

		s.util.Logger.Info("PaymentService.CreatePayCharge.Wallet", zap.Any("pgFee", pgFee))

		amountCharge = transaction.Amount + pgFee
		params.ChargeAmount = &amountCharge
		params.PGFee = &pgFee
		params.PGRealFee = &pgRealFee
		params.PGPlatformFee = &pGPlatformFee

		paramsEwallet.Amount = amountCharge

		xend := xendit.NewXendit(s.util.Conf.Get("XENDIT_API_KEY"), s.util, transaction.CompanyID, transaction.ID, *params.ExternalID, s.taskPublisher)

		s.util.Logger.Info("PaymentService.Create.Wallet", zap.Any("paramsEwallet", paramsEwallet))

		resp, err := xend.CreateEWallet(paramsEwallet)
		if err != nil {
			return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayCharge.xend.RequestEWalletPayment")
		}

		s.util.Logger.Info("PaymentService.CreatePayCharge.Wallet", zap.Any("resp", resp))

		if resp.Actions != nil {
			desktopWebCheckoutURL := resp.Actions.DesktopWebCheckoutURL
			mobileWebCheckoutURL := resp.Actions.MobileWebCheckoutURL
			mobileDeeplinkCheckout := resp.Actions.MobileDeeplinkCheckout
			qrCheckoutString := resp.Actions.QRCheckoutString

			params.DesktopWebCheckoutURL = &desktopWebCheckoutURL
			params.MobileWebCheckoutURL = &mobileWebCheckoutURL
			params.MobileDeepLinkCheckoutURL = &mobileDeeplinkCheckout
			params.QRString = &qrCheckoutString
		}

		params.EWalletType = &paymentMethod.Name
		params.ChannelCode = &resp.ChannelCode
		params.CheckoutMethod = &resp.CheckoutMethod
		params.SuccessRedirectURL = &paymentMethod.SuccessRedirectURL
		params.ProviderBusinessID = &resp.BusinessID
		params.ProviderPaymentID = &resp.ID
		params.PaymentChannel = &resp.ChannelCode
		params.PaymentMethod = &paymentMethodEwallet
		params.PaymentType = &paymentMethodEwallet
		params.Currency = &resp.Currency
		params.IsRedirectRequired = &resp.IsRedirectRequired

		metadata["xendit"] = resp

	case constant.PaymentMethodCreditCard:

		if err := params.ValidateCreditCard(); err != nil {
			return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.CreatePayCharge.ValidateCreditCard")
		}

		s.util.Logger.Info("PaymentService.CreatePayCharge.CreditCard.PaymentMethod", zap.Any("Info", paymentMethod))

		if paymentMethod.FeeType == constant.FeeTypePercentage {
			pgFee = pkg.CalculatePercentage(transaction.Amount, paymentMethod.FeeValue)
		} else if paymentMethod.FeeType == constant.FeeTypeFixed {
			pgFee = paymentMethod.FeeValue
		}

		if paymentMethod.PGFeeType == constant.FeeTypePercentage {
			pgRealFee = pkg.CalculatePercentage(transaction.Amount, paymentMethod.PGFeeValue)
		} else if paymentMethod.PGFeeType == constant.FeeTypeFixed {
			pgRealFee = paymentMethod.PGFeeValue
		}

		amountCharge = transaction.Amount + pgFee

		if paymentMethod.ExtraFeeType != "" {

			if paymentMethod.ExtraFeeType == constant.FeeTypePercentage {
				extraFee = pkg.CalculatePercentage(transaction.Amount, paymentMethod.ExtraFeeValue)
			} else if paymentMethod.ExtraFeeType == constant.FeeTypeFixed {
				extraFee = paymentMethod.ExtraFeeValue
			}

			amountCharge = amountCharge + extraFee
			pgFee = pgFee + extraFee
		}

		if paymentMethod.PGExtraFeeType != "" {

			if paymentMethod.PGExtraFeeType == constant.FeeTypePercentage {
				pgExtraFee = pkg.CalculatePercentage(transaction.Amount, paymentMethod.PGExtraFeeValue)
			} else if paymentMethod.PGExtraFeeType == constant.FeeTypeFixed {
				pgExtraFee = paymentMethod.PGExtraFeeValue
			}

			pgRealFee = pgRealFee + pgExtraFee
		}

		pGPlatformFee = pgFee - pgRealFee

		params.ChargeAmount = &amountCharge
		params.PGFee = &pgFee
		params.PGRealFee = &pgRealFee
		params.PGPlatformFee = &pGPlatformFee

		paramsCreditCard := xendit.CreditCardChargeRequest{
			Amount:     amountCharge,
			ExternalID: *params.ExternalID,
			TokenID:    *params.TokenID,
		}

		if params.CardCvn != nil && *params.CardCvn != "" {
			paramsCreditCard.CardCvn = *params.CardCvn
		}

		xend := xendit.NewXendit(s.util.Conf.Get("XENDIT_API_KEY"), s.util, transaction.CompanyID, transaction.ID, *params.ExternalID, s.taskPublisher)

		s.util.Logger.Info("PaymentService.CreatePayCharge.CreditCard", zap.Any("paramsCreditCard", paramsCreditCard))

		respCard, err = xend.CreateCreditCardCharge(paramsCreditCard)
		if err != nil {
			return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayCharge.xend.RequestEWalletPayment")
		}

		s.util.Logger.Info("PaymentService.CreatePayCharge.CreditCard", zap.Any("resp", respCard))

		metadata["xendit"] = respCard

	}

	params.Amount = &transaction.TotalPayment
	params.CompanyID = merchant.CompanyID
	params.ProfileID = merchant.ProfileID
	params.ReferenceNo = &transaction.ReferenceNo
	params.TransactionNo = &transaction.TransactionNo
	params.MerchantID = transaction.MerchantID
	params.ReferrerID = transaction.ReferrerID
	params.VoucherID = transaction.VoucherID
	params.VoucherCode = &transaction.VoucherCode
	params.ReferrerID = transaction.ReferrerID
	params.Metadata = &metadata
	params.BuyerEmail = &transaction.BuyerEmail

	s.util.Logger.Info("PaymentService.CreatePayCharge", zap.Any("params", params))

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayCharge.repo.Create")
	}

	if _, err := s.transactionRepo.Update(ctx, transaction.ID, updateTransaction); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayCharge.transactionRepo.UpdateByFilter")
	}

	//publish to mercenary
	payloadPaymentState := internal.PayloadPaymentState{
		CompanyID:      transaction.CompanyID,
		ProfileID:      transaction.ProfileID,
		MerchantID:     transaction.MerchantID,
		TransactionID:  transaction.ID,
		PaymentID:      res.ID,
		ReferenceNo:    transaction.ReferenceNo,
		BuyerID:        transaction.BuyerID,
		BuyerEmail:     transaction.BuyerEmail,
		Amount:         res.Amount,
		ChargeAmount:   res.ChargeAmount,
		PaymentMethod:  res.PaymentMethod,
		PaymentChannel: res.ChannelCode,
		ProductType:    transaction.ProductType,
		Provider:       res.Provider,
		State:          "created",
		Status:         constant.StatusActive,
	}

	if err := s.taskPublisher.PublishPaymentMercenaryState(ctx, payloadPaymentState); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayCharge.taskPublisher.PublishPaymentState")
	}

	if respCard != nil {

		payloadCardPayment := internal.PayloadWebhookCardPayment{
			Event:         "creditcard.charge",
			TransactionID: transaction.ID,
			BusinessID:    respCard.BusinessID,
			Provider:      res.Provider,
			Created:       res.CreatedAt,
			Data: struct {
				ID                    string                 `json:"id"`
				Status                string                 `json:"status"`
				AuthorizedAmount      float64                `json:"authorized_amount"`
				CaptureAmount         float64                `json:"capture_amount"`
				Currency              string                 `json:"currency"`
				Metadata              map[string]interface{} `json:"metadata"`
				CreditCardTokenID     string                 `json:"credit_card_token_id"`
				BusinessID            string                 `json:"business_id"`
				MerchantID            string                 `json:"merchant_id"`
				MerchantReferenceCode string                 `json:"merchant_reference_code"`
				ExternalID            string                 `json:"external_id"`
				ECI                   string                 `json:"eci"`
				ChargeType            string                 `json:"charge_type"`
				MaskedCardNumber      string                 `json:"masked_card_number"`
				CardBrand             string                 `json:"card_brand"`
				CardType              string                 `json:"card_type"`
				Descriptor            string                 `json:"descriptor"`
				AuthorizationID       string                 `json:"authorization_id"`
				BankReconciliationID  string                 `json:"bank_reconciliation_id"`
				IssuingBankName       string                 `json:"issuing_bank_name"`
				CVNCode               string                 `json:"cvn_code"`
				ApprovalCode          string                 `json:"approval_code"`
				Created               string                 `json:"created"`
				CardFingerprint       string                 `json:"card_fingerprint"`
			}{
				ID: respCard.ID,
				Status: func() string {
					if respCard.Status == "CAPTURED" {
						return "SUCCEEDED"
					}
					return respCard.Status
				}(),
				AuthorizedAmount:      respCard.AuthorizedAmount,
				CaptureAmount:         respCard.CaptureAmount,
				Currency:              respCard.Currency,
				Metadata:              respCard.Metadata,
				CreditCardTokenID:     respCard.CreditCardTokenID,
				BusinessID:            respCard.BusinessID,
				MerchantID:            respCard.MerchantID,
				MerchantReferenceCode: respCard.MerchantReferenceCode,
				ExternalID:            respCard.ExternalID,
				ECI:                   respCard.ECI,
				ChargeType:            respCard.ChargeType,
				MaskedCardNumber:      respCard.MaskedCardNumber,
				CardBrand:             respCard.CardBrand,
				CardType:              respCard.CardType,
				Descriptor:            respCard.Descriptor,
				AuthorizationID:       respCard.AuthorizationID,
				BankReconciliationID:  respCard.BankReconciliationID,
				IssuingBankName:       respCard.IssuingBankName,
				CVNCode:               respCard.CVNCode,
				ApprovalCode:          respCard.ApprovalCode,
				Created:               respCard.Created,
				CardFingerprint:       respCard.CardFingerprint,
			},
		}

		if err := s.taskPublisher.PublishCardPayment(ctx, payloadCardPayment); err != nil {
			return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.CreatePayCharge.taskPublisher.PublishCardPayment")
		}
	}

	return res, nil

}

func (s *PaymentService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *PaymentService) Update(ctx context.Context, id string, params internal.CreatePayment, nexusData riot.Nexus) (_ internal.Payment, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentService", "Update"))

	if !s.cb.Ready() {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.s.repo.Update")
	}

	return res, nil
}

func (s *PaymentService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Payment, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Payment{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.s.repo.GetByID")
	}

	return res, nil
}

func (s *PaymentService) GetQris(ctx context.Context, referenceNo string, includes []string, nexusData riot.Nexus) (_ internal.QrisResponse, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentService", "GetByID"))

	if !s.cb.Ready() {
		return internal.QrisResponse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	filterPayment := map[string]interface{}{
		"reference_no": referenceNo,
	}

	res, err := s.repo.GetByFilter(ctx, filterPayment)
	if err != nil {
		return internal.QrisResponse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.QrisResponse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.s.repo.GetByID")
	}

	expiresAt := *res.ExpiresAt
	now := time.Now().UTC()
	fmt.Println("now", now, "expiresAt", expiresAt)
	timeRemaining := int64(expiresAt.Sub(now).Seconds())

	// Ensure `time_remaining` is never negative
	if timeRemaining < 0 {
		timeRemaining = 0
	}

	// Ensure `time_remaining` is never negative
	if timeRemaining < 0 {
		timeRemaining = 0
	}

	if timeRemaining == 0 {
		return internal.QrisResponse{
			Status: constant.StatusTransactionExpired,
		}, nil
	}

	resp := internal.QrisResponse{
		TransactionID: res.TransactionID,
		ReferenceNo:   res.ReferenceNo,
		ExternalID:    res.ExternalID,
		Amount:        res.Amount,
		QRString:      res.QRString,
		Currency:      res.Currency,
		Status:        res.Status,
		ExpiresAt:     res.ExpiresAt,
		TimeRemaining: timeRemaining,
	}

	if res.Status == constant.StatusTransactionPaid {
		return internal.QrisResponse{
			Status: constant.StatusTransactionPaid,
		}, nil
	}

	return resp, nil
}

func (s *PaymentService) GetPaymentReference(ctx context.Context, referenceNo string, includes []string, nexusData riot.Nexus) (_ internal.PaymentResponse, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentService", "GetPaymentRefference"))

	if !s.cb.Ready() {
		return internal.PaymentResponse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	filterPayment := map[string]interface{}{
		"reference_no": referenceNo,
	}

	resp, err := s.repo.GetByFilterWithOption(ctx, filterPayment)
	if err != nil {
		return internal.PaymentResponse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.GetPaymentRefference")
	}

	if err := resp.ValidateNotExist(); err != nil {
		return internal.PaymentResponse{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "PaymentService.GetPaymentRefference")
	}

	// Check if transaction is already paid or completed - no need to check expiry
	if resp.Status == constant.StatusTransactionPaid || resp.Status == constant.StatusTransactionCompleted {
		return internal.PaymentResponse{
			ID:                        resp.ID,
			QRString:                  resp.QRString,
			ReferenceNo:               resp.ReferenceNo,
			Amount:                    resp.Amount,
			ChargeAmount:              resp.ChargeAmount,
			IsRedirectRequired:        resp.IsRedirectRequired,
			DesktopWebCheckoutURL:     resp.DesktopWebCheckoutURL,
			MobileWebCheckoutURL:      resp.MobileWebCheckoutURL,
			MobileDeepLinkCheckoutURL: resp.MobileDeepLinkCheckoutURL,
			EWalletType:               resp.EWalletType,
			PaymentType:               resp.PaymentType,
			PaymentMethod:             resp.PaymentMethod,
			PaymentChannel:            resp.PaymentChannel,
			ExternalID:                resp.ExternalID,
			TimeRemaining:             0, // Always 0 for completed transactions, regardless of ExpiresAt
			Status:                    resp.Status,
			ExpiresAt:                 resp.ExpiresAt, // Keep original ExpiresAt for reference
			TransactionID:             resp.TransactionID,
			Currency:                  resp.Currency,
		}, nil
	}

	// For pending transactions, check expiry
	var timeRemaining int64 = 0

	if resp.ExpiresAt != nil {
		expiresAt := *resp.ExpiresAt
		now := time.Now().UTC()
		fmt.Println("now", now, "expiresAt", expiresAt)
		timeRemaining = int64(expiresAt.Sub(now).Seconds())

		// If expired (time is up), return expired status
		if timeRemaining <= 0 {
			return internal.PaymentResponse{
				Status: constant.StatusTransactionExpired,
			}, nil
		}
	} else {
		// If no expiry time is set, we can either:
		// Option 1: Set a default time remaining (e.g., -1 to indicate no expiry)
		// Option 2: Continue without expiry check
		timeRemaining = -1 // -1 indicates no expiry limit
	}

	// Return pending transaction with time remaining
	return internal.PaymentResponse{
		ID:                        resp.ID,
		QRString:                  resp.QRString,
		ReferenceNo:               resp.ReferenceNo,
		Amount:                    resp.Amount,
		ChargeAmount:              resp.ChargeAmount,
		IsRedirectRequired:        resp.IsRedirectRequired,
		DesktopWebCheckoutURL:     resp.DesktopWebCheckoutURL,
		MobileWebCheckoutURL:      resp.MobileWebCheckoutURL,
		MobileDeepLinkCheckoutURL: resp.MobileDeepLinkCheckoutURL,
		EWalletType:               resp.EWalletType,
		PaymentType:               resp.PaymentType,
		PaymentMethod:             resp.PaymentMethod,
		PaymentChannel:            resp.PaymentChannel,
		ExternalID:                resp.ExternalID,
		TimeRemaining:             timeRemaining,
		Status:                    resp.Status,
		ExpiresAt:                 resp.ExpiresAt,
		TransactionID:             resp.TransactionID,
		Currency:                  resp.Currency,
	}, nil
}

func (s *PaymentService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.PaymentPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("PaymentService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.PaymentPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.PaymentPagin{}, err
		}
		filterMap["profile_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.PaymentPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.PaymentPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "PaymentService.repo.GetAllWithPagination")
	}

	return res, nil
}
