package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type ConfigurationRepository interface {
	Create(ctx context.Context, params internal.CreateConfiguration) (internal.Configuration, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateConfiguration) (internal.Configuration, error)
	GetByID(ctx context.Context, id string) (internal.Configuration, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Configuration, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ConfigurationPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Configuration, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateConfiguration) (internal.Configuration, error)
}

type ConfigurationService struct {
	repo ConfigurationRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewConfigurationService(util riot.Util, repo ConfigurationRepository) *ConfigurationService {
	return &ConfigurationService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *ConfigurationService) Create(ctx context.Context, params internal.CreateConfiguration, nexusData riot.Nexus) (_ internal.Configuration, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationService", "Create"))

	if !s.cb.Ready() {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	switch profile.Role {
	case riot.RoleCompany:
		params.CompanyID = profile.ID
	case riot.RoleCustomer:
		params.CompanyID = profile.ParentID
	}

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ConfigurationService.params.Validate")
	}

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationService.s.repo.Create")
	}

	return res, nil
}

func (s *ConfigurationService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterConfiguration := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterConfiguration)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ConfigurationService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterConfiguration)
}

func (s *ConfigurationService) Update(ctx context.Context, id string, params internal.CreateConfiguration, nexusData riot.Nexus) (_ internal.Configuration, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationService", "Update"))

	if !s.cb.Ready() {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterConfiguration := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterConfiguration)

	if err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ConfigurationService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterConfiguration, params)
	if err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationService.s.repo.Update")
	}

	return res, nil
}

func (s *ConfigurationService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Configuration, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterConfiguration := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterConfiguration)
	if err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Configuration{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ConfigurationService.s.repo.GetByID")
	}

	return res, nil
}

func (s *ConfigurationService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.ConfigurationPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ConfigurationService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.ConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	switch profile.Role {
	case riot.RoleCompany:
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.ConfigurationPagin{}, err
		}
		filterMap["company_id"] = objectID
	case riot.RoleCustomer:
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.ConfigurationPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.ConfigurationPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ConfigurationService.repo.GetAllWithPagination")
	}

	return res, nil
}
