package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/request"
	"github.com/continue-team/manaslu/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/continue-team/riot/pkg/util"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type TransactionRepository interface {
	Create(ctx context.Context, params internal.CreateTransaction) (internal.Transaction, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateTransaction) (internal.Transaction, error)
	GetByID(ctx context.Context, id string) (internal.Transaction, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Transaction, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.TransactionPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Transaction, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateTransaction) (internal.Transaction, error)
}

type TransactionService struct {
	repo                TransactionRepository
	merchantRepo        MerchantRepository
	merchantPfRepo      MerchantPlatformFeeRepository
	merchantVoucherRepo MerchantVoucherRepository
	voucherRepo         VoucherRepository
	featureFeeRepo      FeatureFeeRepository
	feeRepo             FeeRepository
	cb                  *circuitbreaker.CircuitBreaker
	util                riot.Util
}

func NewTransactionService(util riot.Util, repo TransactionRepository, merchantRepo MerchantRepository, merchantPfRepo MerchantPlatformFeeRepository, merchantVoucherRepo MerchantVoucherRepository, voucherRepo VoucherRepository, featureFeeRepo FeatureFeeRepository, feeRepo FeeRepository) *TransactionService {
	return &TransactionService{
		repo:                repo,
		merchantRepo:        merchantRepo,
		merchantPfRepo:      merchantPfRepo,
		merchantVoucherRepo: merchantVoucherRepo,
		voucherRepo:         voucherRepo,
		featureFeeRepo:      featureFeeRepo,
		feeRepo:             feeRepo,
		util:                util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *TransactionService) Create(ctx context.Context, params internal.CreateTransaction, nexusData riot.Nexus) (_ internal.Transaction, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionService", "Create"))

	if !s.cb.Ready() {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	webservice := riot.GetNexusWebservice(nexusData)

	if profile.ID != "" {
		switch profile.Role {
		case riot.RoleCompany:
			params.CompanyID = profile.ID
		case riot.RoleCustomer:
			params.CompanyID = profile.ParentID
		}
	} else if webservice.ID != "" {
		params.CompanyID = webservice.ID
	}

	params.Status = constant.StatusTransactionNew

	if err = params.Validate(); err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.Create.params.Validate")
	}

	//find mechant
	filterMerchant := map[string]interface{}{
		"id":     params.MerchantID,
		"status": constant.TypeActive,
	}

	s.util.Logger.Info("TransactionService.Create.Info", zap.Any("filterMerchant", filterMerchant))

	merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.Create.merchantRepo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.Create.merchant.ValidateNotExist")
	}

	s.util.Logger.Info("TransactionService.Create.Info", zap.Any("merchant", merchant))

	//if feature tip
	if params.ProductType != nil && *params.ProductType != "" && strings.ToUpper(*params.ProductType) == constant.TypeFeatureTip {
		return s.CreateTrxTip(ctx, params, nexusData)
	}

	amount := *params.Amount
	netAmount := &amount

	uniqueNo := pkg.GenerateUniqueNo()
	transcationNo := fmt.Sprintf("%s-%s", "TR", uniqueNo)

	params.CompanyID = merchant.CompanyID
	params.ProfileID = merchant.ProfileID
	params.MerchantID = merchant.ID
	params.UniqueNo = &uniqueNo

	if params.ReferrerID == nil {
		params.ReferrerID = &merchant.ReferrerID
	}

	params.TransactionNo = &transcationNo
	params.MarketingID = &merchant.MarketingID
	params.TotalPayment = params.Amount
	params.NetAmount = netAmount

	s.util.Logger.Info("TransactionService.Create", zap.Any("params", params))

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.s.repo.Create")
	}

	return res, nil
}

func (s *TransactionService) CreateTrxTip(ctx context.Context, params internal.CreateTransaction, nexusData riot.Nexus) (_ internal.Transaction, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionService", "Create"))

	if !s.cb.Ready() {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	webservice := riot.GetNexusWebservice(nexusData)
	useVoucher := false

	if profile.ID != "" {
		switch profile.Role {
		case riot.RoleCompany:
			params.CompanyID = profile.ID
		case riot.RoleCustomer:
			params.CompanyID = profile.ParentID
		}
	} else if webservice.ID != "" {
		params.CompanyID = webservice.ID
	}

	params.Status = constant.StatusTransactionNew

	if err = params.Validate(); err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.params.Validate")
	}

	//find mechant
	filterMerchant := map[string]interface{}{
		"id":         params.MerchantID,
		"company_id": params.CompanyID,
		"status":     constant.TypeActive,
	}

	s.util.Logger.Info("TransactionService.CreateTrxTip.Create.Info", zap.Any("filterMerchant", filterMerchant))

	merchant, err := s.merchantRepo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.CreateTrxTip.merchantRepo.GetByFilter")
	}

	if err := merchant.ValidateNotExist(); err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.CreateTrxTip.merchant.ValidateNotExist")
	}

	s.util.Logger.Info("TransactionService.CreateTrxTip.Info", zap.Any("merchant", merchant))

	//find merchant platform fee
	filterMerchantPf := map[string]interface{}{
		"merchant_id":  params.MerchantID,
		"company_id":   params.CompanyID,
		"product_type": strings.ToUpper(*params.ProductType),
	}

	merchantPf, err := s.merchantPfRepo.GetByFilter(ctx, filterMerchantPf)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.CreateTrxTip.merchantPfRepo.GetByFilter")
	}

	if err := merchantPf.ValidateNotExist(); err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.CreateTrxTip.merchantPf.ValidateNotExist")
	}

	s.util.Logger.Info("TransactionService.CreateTrxTip.Info", zap.Any("merchantPf", merchantPf))

	// params.Fees = merchantPf.Fees

	builder := mongorm.NewBuilder()
	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if params.MerchantID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.MerchantID)
		if err != nil {
			return internal.Transaction{}, err
		}
		filterMap["merchant_id"] = objectID
	}

	filterMap["product_type"] = strings.ToUpper(*params.ProductType)
	filterMap["status"] = constant.TypeActive

	builder.SortField = "created_at"
	builder.SortOrder = -1

	merchantVoucher, err := s.merchantVoucherRepo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.CreateTrxTip.merchantVoucherRepo.GetAllWithPagination")
	}

	s.util.Logger.Info("TransactionService.CreateTrxTip.Info", zap.Any("merchantVoucher", merchantVoucher))

	if merchantVoucher.TotalRecords > 0 {

		now := time.Now()

		for _, voucher := range merchantVoucher.Records {

			s.util.Logger.Info("TransactionService.CreateTrxTip.voucher.ExpiresAt.Before(now)", zap.Any("Info", voucher.ExpiresAt.Before(now)))

			if voucher.ExpiresAt.After(now) {
				useVoucher = true
				params.VoucherID = &voucher.ID
				params.UseVoucher = &useVoucher
				params.VoucherCode = &voucher.Code
				// params.Fees = voucher.Fees
				break
			} else {
				//deleted
				s.util.Logger.Info("TransactionService.CreateTrxTip.voucher.ExpiresAt.Before(now) false")
				//soft delete
				err = s.merchantVoucherRepo.SoftDelete(ctx, voucher.ID)
				if err != nil {
					return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.CreateTrxTip.merchantVoucherRepo.SoftDelete")
				}
			}
		}

	}

	filterFeatureFee := map[string]interface{}{
		"company_id": params.CompanyID,
		"status":     constant.TypeActive,
	}

	//find feature fees
	featureFees, err := s.featureFeeRepo.GetAll(ctx, filterFeatureFee)

	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.CreateTrxTip.featureFeeRepo.GetAll")
	}

	s.util.Logger.Info("TransactionService.CreateTrxTip.Info", zap.Any("featureFees", featureFees))

	totalFeatureFee := 0.0
	if len(featureFees) > 0 {
		if params.Features != nil && len(*params.Features) > 0 {
			for _, feature := range *params.Features {
				for _, featureFee := range featureFees {
					if feature == featureFee.Name {

						if featureFee.MinTransaction > 0 {

							if *params.Amount <= featureFee.MinTransaction {
								break
							}

						}

						if featureFee.MaxTransaction > 0 {

							if *params.Amount >= featureFee.MaxTransaction {
								break
							}
						}

						if featureFee.FeeType == "fixed" {
							totalFeatureFee += featureFee.FeeValue
						} else if featureFee.FeeType == "percentage" {
							totalFeatureFee += featureFee.FeeValue * float64(*params.Amount)
						}

					}
				}
			}
		}
	}

	amount := *params.Amount
	netAmount := &amount
	if totalFeatureFee > 0 {
		*netAmount -= totalFeatureFee
	}

	uniqueNo := util.GenerateUniqueNo()
	transcationNo := fmt.Sprintf("%s-%s", "TR", uniqueNo)

	params.CompanyID = merchant.CompanyID
	params.ProfileID = merchant.ProfileID
	params.MerchantID = merchant.ID
	params.UniqueNo = &uniqueNo

	params.TransactionNo = &transcationNo
	params.ReferrerID = &merchant.ReferrerID
	params.MarketingID = &merchant.MarketingID
	params.TotalFeatureFee = &totalFeatureFee
	params.TotalPayment = params.Amount
	params.NetAmount = netAmount

	s.util.Logger.Info("TransactionService.CreateTrxTip.Info", zap.Any("params", params))

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.CreateTrxTip.repo.Create")
	}

	return res, nil
}

func (s *TransactionService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *TransactionService) Update(ctx context.Context, id string, params internal.CreateTransaction, nexusData riot.Nexus) (_ internal.Transaction, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionService", "Update"))

	if !s.cb.Ready() {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.s.repo.Update")
	}

	return res, nil
}

func (s *TransactionService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Transaction, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Transaction{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.s.repo.GetByID")
	}

	return res, nil
}

func (s *TransactionService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.TransactionPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("TransactionService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	var query *internal.QueryTransaction

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))
	queryQry, _ := riot.QueryByPrefix(&urlValues, "query")
	asQry, _ := url.QueryUnescape(urlValues.Get("as"))
	productType, _ := url.QueryUnescape(urlValues.Get("product_type"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if productType != "" {
		filterMap["product_type"] = strings.ToUpper(productType)
	}

	if profile.ID != "" {

		if asQry == "buyer" {

			objectID, err := primitive.ObjectIDFromHex(profile.ID)
			if err != nil {
				return internal.TransactionPagin{}, err
			}
			filterMap["buyer_id"] = objectID

		} else {

			objectID, err := primitive.ObjectIDFromHex(profile.ID)
			if err != nil {
				return internal.TransactionPagin{}, err
			}
			filterMap["profile_id"] = objectID
		}

	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.TransactionPagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if err := json.Unmarshal([]byte(queryQry), &query); err != nil {
		return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MemberTipService.GetAllWithPagination.json.Unmarshal")
	}

	if query != nil {

		if query.TransactionNo != "" {
			builder.SearchANDFields["transaction_no"] = query.TransactionNo
		}

		if query.BuyerEmail != "" {
			builder.SearchANDFields["buyer_email"] = query.BuyerEmail
		}

		if query.BuyerName != "" {
			builder.SearchANDFields["buyer_name"] = query.BuyerName
		}

		if query.Amount != "" {
			//convert string to float64
			amount, err := strconv.ParseFloat(query.Amount, 64)
			if err != nil {
				return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MemberTipService.GetAllWithPagination.ParseFloat")
			}
			builder.SearchANDFields["amount"] = amount
		}

		if query.NetAmount != "" {
			//convert string to float64
			netAmount, err := strconv.ParseFloat(query.NetAmount, 64)
			if err != nil {
				return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MemberTipService.GetAllWithPagination.ParseFloat")
			}
			builder.SearchANDFields["net_amount"] = netAmount
		}

	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.repo.GetAllWithPagination")
	}

	identifierRequest := request.Identifier{
		AccessToken: urlValues.Get("Authorization"),
	}

	newRequest := request.NewRequest(s.util, s.util.Conf.Get("GATEWAY_URL"))

	// Use index to modify the actual transaction records
	for i := range res.Records {

		if asQry == "buyer" {
			if res.Records[i].ProfileID != "" {

				resp, err := newRequest.GetProfileByID(ctx, identifierRequest, res.Records[i].ProfileID)

				if err != nil {
					return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.repo.GetAllWithPagination.newRequest.GetProfileByID")
				}

				if resp.Code != http.StatusOK {
					messages := resp.Messages.([]interface{})
					if err = res.ErrorMessage(messages[0].(map[string]interface{})["message"].(string)); err != nil {
						return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "TransactionService.repo.GetAllWithPagination.res.ErrorMessage")
					}
				}

				// Directly assign to the transaction record
				res.Records[i].SellerEmail = resp.Data.Email
				res.Records[i].SellerName = resp.Data.LoginID

			} else {

				if res.Records[i].BuyerID != "" {

					resp, err := newRequest.GetProfileByID(ctx, identifierRequest, res.Records[i].BuyerID)

					if err != nil {
						return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "TransactionService.repo.GetAllWithPagination.newRequest.GetProfileByID")
					}

					if resp.Code != http.StatusOK {
						messages := resp.Messages.([]interface{})
						if err = res.ErrorMessage(messages[0].(map[string]interface{})["message"].(string)); err != nil {
							return internal.TransactionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "MemberTipService.Create.newRequest.CreateProfile")
						}
					}

					// Directly assign to the transaction record
					res.Records[i].BuyerEmail = resp.Data.Email
					res.Records[i].BuyerName = resp.Data.LoginID

				}
			}
		}

	}
	return res, nil
}
