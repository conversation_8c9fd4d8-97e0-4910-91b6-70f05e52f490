package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type FeatureFeeRepository interface {
	Create(ctx context.Context, params internal.CreateFeatureFee) (internal.FeatureFee, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateFeatureFee) (internal.FeatureFee, error)
	GetByID(ctx context.Context, id string) (internal.FeatureFee, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.FeatureFee, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.FeatureFeePagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.FeatureFee, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateFeatureFee) (internal.FeatureFee, error)
}

type FeatureFeeService struct {
	repo FeatureFeeRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewFeatureFeeService(util riot.Util, repo FeatureFeeRepository) *FeatureFeeService {
	return &FeatureFeeService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *FeatureFeeService) Create(ctx context.Context, params internal.CreateFeatureFee, nexusData riot.Nexus) (_ internal.FeatureFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeService", "Create"))

	if !s.cb.Ready() {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	switch profile.Role {
	case riot.RoleCompany:
		params.CompanyID = profile.ID
	case riot.RoleCustomer:
		params.CompanyID = profile.ParentID
	}

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "FeatureFeeService.params.Validate")
	}

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeService.s.repo.Create")
	}

	return res, nil
}

func (s *FeatureFeeService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	companyID := ""

	switch profile.Role {
	case riot.RoleCompany:
		companyID = profile.ID
	case riot.RoleCustomer:
		companyID = profile.ParentID
	}

	filterMerchant := map[string]interface{}{
		"id":         id,
		"company_id": companyID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "FeatureFeeService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *FeatureFeeService) Update(ctx context.Context, id string, params internal.CreateFeatureFee, nexusData riot.Nexus) (_ internal.FeatureFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeService", "Update"))

	if !s.cb.Ready() {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	switch profile.Role {
	case riot.RoleCompany:
		params.CompanyID = profile.ID
	case riot.RoleCustomer:
		params.CompanyID = profile.ParentID
	}

	filterMerchant := map[string]interface{}{
		"id":         id,
		"company_id": params.CompanyID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "FeatureFeeService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeService.s.repo.Update")
	}

	return res, nil
}

func (s *FeatureFeeService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.FeatureFee, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeService", "GetByID"))

	if !s.cb.Ready() {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	companyID := ""

	switch profile.Role {
	case riot.RoleCompany:
		companyID = profile.ID
	case riot.RoleCustomer:
		companyID = profile.ParentID
	}

	filterMerchant := map[string]interface{}{
		"id":         id,
		"company_id": companyID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.FeatureFee{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "FeatureFeeService.s.repo.GetByID")
	}

	return res, nil
}

func (s *FeatureFeeService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.FeatureFeePagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("FeatureFeeService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.FeatureFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)
	groupID := urlValues.Get("group_id")

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(map[string]interface{})
	if !ok {
		filterMap = make(map[string]interface{})
	}

	if profile.ID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ID)
		if err != nil {
			return internal.FeatureFeePagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if profile.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(profile.ParentID)
		if err != nil {
			return internal.FeatureFeePagin{}, err
		}
		filterMap["company_id"] = objectID
	}

	if groupID != "" {
		objectID, err := primitive.ObjectIDFromHex(groupID)
		if err != nil {
			return internal.FeatureFeePagin{}, err
		}
		filterMap["group_id"] = objectID
	}

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.FeatureFeePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "FeatureFeeService.repo.GetAllWithPagination")
	}

	return res, nil
}
