package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/manaslu/internal"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type ProviderRepository interface {
	Create(ctx context.Context, params internal.CreateProvider) (internal.Provider, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateProvider) (internal.Provider, error)
	GetByID(ctx context.Context, id string) (internal.Provider, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Provider, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ProviderPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Provider, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateProvider) (internal.Provider, error)
}

type ProviderService struct {
	repo ProviderRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewProviderService(util riot.Util, repo ProviderRepository) *ProviderService {
	return &ProviderService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *ProviderService) Create(ctx context.Context, params internal.CreateProvider, nexusData riot.Nexus) (_ internal.Provider, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderService", "Create"))

	if !s.cb.Ready() {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	params.Status = constant.StatusActive

	if err = params.Validate(); err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderService.params.Validate")
	}

	filter := map[string]interface{}{
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderService.s.repo.GetByFilter")
	}

	if err := res.ValidateRegistered(); err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderService.res.ValidateRegistered")
	}

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderService.s.repo.Create")
	}

	return res, nil
}

func (s *ProviderService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderService.s.repo.GetByFilter")
	}

	return s.repo.SoftDeleteByFilter(ctx, filterMerchant)
}

func (s *ProviderService) Update(ctx context.Context, id string, params internal.CreateProvider, nexusData riot.Nexus) (_ internal.Provider, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderService", "Update"))

	if !s.cb.Ready() {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)

	if err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderService.s.repo.GetByFilter")
	}

	res, err = s.repo.UpdateByFilter(ctx, filterMerchant, params)
	if err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderService.s.repo.Update")
	}

	return res, nil
}

func (s *ProviderService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Provider, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filterMerchant := map[string]interface{}{
		"id":         id,
		"profile_id": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filterMerchant)
	if err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Provider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ProviderService.s.repo.GetByID")
	}

	return res, nil
}

func (s *ProviderService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.ProviderPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ProviderService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.ProviderPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.ProviderPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ProviderService.repo.GetAllWithPagination")
	}

	return res, nil
}
