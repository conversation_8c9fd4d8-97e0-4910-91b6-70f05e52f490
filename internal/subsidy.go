package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Subsidy struct {
	ID            string            `json:"_id"`
	CompanyID     string            `json:"company_id"`
	ProfileID     string            `json:"profile_id"`
	MerchantID    string            `json:"merchant_id"`
	TransactionID string            `json:"transaction_id"`
	VoucherID     string            `json:"voucher_id"`
	PGFee         float64           `json:"pg_fee"`
	FeatureFee    float64           `json:"feature_fee"`
	Amount        float64           `json:"amount"`
	Description   string            `json:"description"`
	Metadata      map[string]string `json:"metadata"`
	Status        string            `json:"status"`
	CreatedAt     *time.Time        `json:"created_at"`
	UpdatedAt     *time.Time        `json:"updated_at"`
}

type SubsidyPagin struct {
	Limit        int       `json:"limit"`
	Page         int       `json:"page"`
	Sort         string    `json:"sort"`
	TotalRecords int       `json:"total_records"`
	TotalPages   int       `json:"total_pages"`
	Records      []Subsidy `json:"records"`
}

func (b Subsidy) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Subsidy) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b Subsidy) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Subsidy) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateSubsidy struct {
	CompanyID     string             `json:"company_id,omitempty"`
	ProfileID     string             `json:"profile_id,omitempty"`
	MerchantID    string             `json:"merchant_id,omitempty"`
	TransactionID string             `json:"transaction_id,omitempty"`
	VoucherID     *string            `json:"voucher_id,omitempty"`
	PGFee         *float64           `json:"pg_fee,omitempty"`
	FeatureFee    *float64           `json:"feature_fee,omitempty"`
	Amount        *float64           `json:"amount"`
	Type          *string            `json:"type"`
	Description   *string            `json:"description,omitempty"`
	Metadata      *map[string]string `json:"metadata,omitempty"`
	Status        string             `json:"status,omitempty"`
}

func (ct CreateSubsidy) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.Type, validation.In("commission", "Subsidy", "refund")),
		validation.Field(&ct.Amount, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type PayloadSubsidy struct {
	CompanyID     string  `json:"company_id,omitempty"`
	ProfileID     string  `json:"profile_id,omitempty"`
	TransactionID string  `json:"transaction_id"`
	MerchantID    string  `json:"merchant_id"`
	VoucherID     string  `json:"voucher_id,omitempty"`
	PGFee         float64 `json:"pg_fee"`
	FeatureFee    float64 `json:"feature_fee"`
	Amount        float64 `json:"amount"`
	Description   string  `json:"description,omitempty"`
}
