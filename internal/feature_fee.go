package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type FeatureFee struct {
	ID             string     `json:"_id"`
	CompanyID      string     `json:"company_id"`
	Name           string     `json:"name"`
	Description    string     `json:"description"`
	FeeType        string     `json:"fee_type"`
	FeeValue       float64    `json:"fee_value"`
	MinTransaction float64    `json:"min_transaction"`
	MaxTransaction float64    `json:"max_transaction"`
	Currency       string     `json:"currency"`
	Status         string     `json:"status"`
	CreatedAt      *time.Time `json:"created_at"`
	UpdatedAt      *time.Time `json:"updated_at"`
}

type FeatureFeePagin struct {
	Limit        int          `json:"limit"`
	Page         int          `json:"page"`
	Sort         string       `json:"sort"`
	TotalRecords int          `json:"total_records"`
	TotalPages   int          `json:"total_pages"`
	Records      []FeatureFee `json:"records"`
}

func (b FeatureFee) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b FeatureFee) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *FeatureFee) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateFeatureFee struct {
	CompanyID      string   `json:"company_id,omitempty"`
	Name           string   `json:"name"`
	Description    *string  `json:"description,omitempty"`
	FeeType        *string  `json:"fee_type"`
	FeeValue       *float64 `json:"fee_value"`
	MinTransaction *float64 `json:"min_transaction"`
	MaxTransaction *float64 `json:"max_transaction"`
	Currency       *string  `json:"currency"`
	Status         string   `json:"status,omitempty"`
}

func (ct CreateFeatureFee) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.CompanyID, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
