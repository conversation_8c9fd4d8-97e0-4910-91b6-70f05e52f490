package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Transaction struct {
	ID                 string                 `json:"_id"`
	CompanyID          string                 `json:"company_id"`
	ProfileID          string                 `json:"profile_id"`
	BuyerID            string                 `json:"buyer_id"`
	MerchantID         string                 `json:"merchant_id"`
	ReferrerID         string                 `json:"referrer_id"`
	MarketingID        string                 `json:"marketing_id"`
	VoucherID          string                 `json:"voucher_id"`
	TransactionID      string                 `json:"transaction_id"`
	TransactionNo      string                 `json:"transaction_no"`
	UniqueNo           string                 `json:"unique_no"`
	BuyerName          string                 `json:"buyer_name,omitempty"`
	BuyerEmail         string                 `json:"buyer_email,omitempty"`
	SellerName         string                 `json:"seller_name,omitempty"`
	SellerEmail        string                 `json:"seller_email,omitempty"`
	ReferenceNo        string                 `json:"reference_no"`
	Description        string                 `json:"description"`
	Amount             float64                `json:"amount"`
	NetAmount          float64                `json:"net_amount"`
	TotalPayment       float64                `json:"total_payment"`
	PlatformFeeType    string                 `json:"platform_fee_type"`
	PlatformFeeValue   float64                `json:"platform_fee_value"`
	TotalReferrerFee   float64                `json:"total_referrer_fee"`
	TotalMarketingFee  float64                `json:"total_marketing_fee"`
	TotalPlatformFee   float64                `json:"total_platform_fee"`
	TotalFeatureFee    float64                `json:"total_feature_fee"`
	TotalSubsidyFee    float64                `json:"total_subsidy_fee"`
	TotalPaymentFee    float64                `json:"total_payment_fee"`
	TotalDiscount      float64                `json:"total_discount"`
	TotalFee           float64                `json:"total_fee"`
	ThirdPartyFee      float64                `json:"third_party_fee"`
	PGFee              float64                `json:"pg_fee"`
	PGRealFee          float64                `json:"pg_real_fee"`
	PGPlatformFee      float64                `json:"pg_platform_fee"`
	DescriptionSubsidy string                 `json:"subsidy_description"`
	PaymentMethod      string                 `json:"payment_method"`
	PaymentType        string                 `json:"payment_type"`
	PaymentChannel     string                 `json:"payment_channel"`
	Fee                *Fee                   `json:"fee,omitempty"`
	Tax                float64                `json:"tax"`
	Features           []string               `json:"features"`
	UseVoucher         bool                   `json:"use_voucher"`
	VoucherCode        string                 `json:"voucher_code"`
	ProductType        string                 `json:"product_type"`
	Metadata           map[string]interface{} `json:"metadata"`
	Status             string                 `json:"status"`
	CreatedAt          *time.Time             `json:"created_at"`
	UpdatedAt          *time.Time             `json:"updated_at"`
}

type TransactionPagin struct {
	Limit        int           `json:"limit"`
	Page         int           `json:"page"`
	Sort         string        `json:"sort"`
	TotalRecords int           `json:"total_records"`
	TotalPages   int           `json:"total_pages"`
	Records      []Transaction `json:"records"`
}

func (u *TransactionPagin) ErrorMessage(message string) error {
	if message != "" {
		return validation.Errors{
			"": riot.NewErrorf(riot.ErrorCodeInvalidArgument, message),
		}
	}

	return nil
}

func (b Transaction) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.TransactionNo)),
		}
	}
	return nil
}

func (b Transaction) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b Transaction) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Transaction) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateTransaction struct {
	CompanyID          string                  `json:"company_id,omitempty"`
	ProfileID          string                  `json:"profile_id,omitempty"`
	BuyerID            string                  `json:"buyer_id,omitempty"`
	MerchantID         string                  `json:"merchant_id,omitempty"`
	ReferrerID         *string                 `json:"referrer_id,omitempty"`
	MarketingID        *string                 `json:"marketing_id,omitempty"`
	VoucherID          *string                 `json:"voucher_id,omitempty"`
	UniqueNo           *string                 `json:"unique_no,omitempty"`
	BuyerName          *string                 `json:"buyer_name,omitempty"`
	BuyerEmail         *string                 `json:"buyer_email,omitempty"`
	TransactionNo      *string                 `json:"transaction_no,omitempty"`
	ReferenceNo        *string                 `json:"reference_no,omitempty"`
	Description        *string                 `json:"description,omitempty"`
	Amount             *float64                `json:"amount,omitempty"`
	NetAmount          *float64                `json:"net_amount,omitempty"`
	TotalPayment       *float64                `json:"total_payment,omitempty"`
	PlatformFeeType    *string                 `json:"platform_fee_type,omitempty"`
	PlatformFeeValue   *float64                `json:"platform_fee_value,omitempty"`
	TotalReferrerFee   *float64                `json:"total_referrer_fee,omitempty"`
	TotalMarketingFee  *float64                `json:"total_marketing_fee,omitempty"`
	TotalPlatformFee   *float64                `json:"total_platform_fee,omitempty"`
	TotalFeatureFee    *float64                `json:"total_feature_fee,omitempty"`
	TotalSubsidyFee    *float64                `json:"total_subsidy_fee,omitempty"`
	TotalPaymentFee    *float64                `json:"total_payment_fee,omitempty"`
	TotalDiscount      *float64                `json:"total_discount,omitempty"`
	TotalFee           *float64                `json:"total_fee,omitempty"`
	ThirdPartyFee      *float64                `json:"third_party_fee,omitempty"`
	PGFee              *float64                `json:"pg_fee,omitempty"`
	PGRealFee          *float64                `json:"pg_real_fee,omitempty"`
	PGPlatformFee      *float64                `json:"pg_platform_fee,omitempty"`
	DescriptionSubsidy *string                 `json:"subsidy_description,omitempty"`
	PaymentMethod      *string                 `json:"payment_method,omitempty"`
	PaymentType        *string                 `json:"payment_type,omitempty"`
	PaymentChannel     *string                 `json:"payment_channel,omitempty"`
	Fee                *CreateFee              `json:"fee,omitempty"`
	Tax                *float64                `json:"tax,omitempty"`
	Features           *[]string               `json:"features,omitempty"`
	UseVoucher         *bool                   `json:"use_voucher,omitempty"`
	VoucherCode        *string                 `json:"voucher_code,omitempty"`
	ProductType        *string                 `json:"product_type,omitempty"`
	Metadata           *map[string]interface{} `json:"metadata,omitempty"`
	Status             string                  `json:"status,omitempty"`
}

func (ct CreateTransaction) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.ReferenceNo, validation.Required),
		validation.Field(&ct.MerchantID, validation.Required),
		validation.Field(&ct.ProductType, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type QueryTransaction struct {
	TransactionNo string `json:"transaction_no,omitempty"`
	BuyerName     string `json:"buyer_name,omitempty"`
	BuyerEmail    string `json:"buyer_email,omitempty"`
	Amount        string `json:"amount,omitempty"`
	NetAmount     string `json:"net_amount,omitempty"`
	Status        string `json:"status,omitempty"`
}
