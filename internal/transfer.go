package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Transfer struct {
	ID             string            `json:"_id"`
	CompanyID      string            `json:"company_id"`
	ProfileID      string            `json:"profile_id"`
	MerchantID     string            `json:"merchant_id"`
	DownlineID     string            `json:"downline_id"`
	TransactionID  string            `json:"transaction_id"`
	DisbursementID string            `json:"disbursement_id"`
	Amount         float64           `json:"amount"`
	Description    string            `json:"description"`
	Type           string            `json:"type"`
	ProductType    string            `json:"product_type"`
	Metadata       map[string]string `json:"metadata"`
	Operation      string            `json:"operation"`
	Status         string            `json:"status"`
	CreatedAt      *time.Time        `json:"created_at"`
	UpdatedAt      *time.Time        `json:"updated_at"`
}

type TransferPagin struct {
	Limit        int        `json:"limit"`
	Page         int        `json:"page"`
	Sort         string     `json:"sort"`
	TotalRecords int        `json:"total_records"`
	TotalPages   int        `json:"total_pages"`
	Records      []Transfer `json:"records"`
}

func (b Transfer) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Transfer) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b Transfer) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Transfer) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateTransfer struct {
	CompanyID      string             `json:"company_id,omitempty"`
	ProfileID      string             `json:"profile_id,omitempty"`
	MerchantID     string             `json:"merchant_id,omitempty"`
	DownlineID     string             `json:"downline_id,omitempty"`
	TransactionID  string             `json:"transaction_id,omitempty"`
	DisbursementID string             `json:"disbursement_id,omitempty"`
	Amount         *float64           `json:"amount"`
	Type           *string            `json:"type"`
	ProductType    *string            `json:"product_type,omitempty"`
	Description    *string            `json:"description,omitempty"`
	Metadata       *map[string]string `json:"metadata,omitempty"`
	Operation      *string            `json:"operation,omitempty"`
	Status         string             `json:"status,omitempty"`
}

func (ct CreateTransfer) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.Type, validation.In("commission", "transfer", "refund")),
		validation.Field(&ct.Amount, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type PayloadTxnBalance struct {
	MerchantID    string                 `json:"merchant_id,omitempty"`
	CompanyID     string                 `json:"company_id,omitempty"`
	ProfileID     string                 `json:"profile_id,omitempty"`
	Type          int                    `json:"type,omitempty"`
	TypeObject    string                 `json:"type_object,omitempty"`
	TypeID        string                 `json:"type_id,omitempty"`
	SourceID      string                 `json:"source_id,omitempty"`
	Balance       float64                `json:"balance,omitempty"`
	BalanceBefore float64                `json:"balance_before,omitempty"`
	Amount        float64                `json:"amount,omitempty"`
	Description   string                 `json:"description,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	ProductType   string                 `json:"product_type,omitempty"`
}

type PayloadTransfer struct {
	CompanyID      string  `json:"company_id,omitempty"`
	ProfileID      string  `json:"profile_id,omitempty"`
	DownlineID     string  `json:"downline_id,omitempty"`
	TransactionID  string  `json:"transaction_id"`
	DisbursementID string  `json:"disbursement_id"`
	MerchantID     string  `json:"merchant_id"`
	Type           string  `json:"type"`
	ProductType    string  `json:"product_type"`
	Description    string  `json:"description"`
	Amount         float64 `json:"amount"`
	Operation      string  `json:"operation"` //add / subtract
}

type PayloadTipCompleted struct {
	CompanyID        string     `json:"company_id,omitempty"`
	ProfileID        string     `json:"profile_id,omitempty"`
	TransactionID    string     `json:"transaction_id"`
	MerchantID       string     `json:"merchant_id"`
	ReferenceNo      string     `json:"reference_no"`
	Amount           float64    `json:"amount"`
	NetAmount        float64    `json:"net_amount"`
	TotalPlatformFee float64    `json:"total_platform_fee"`
	TotalFeatureFee  float64    `json:"total_feature_fee"`
	PaymentMethod    string     `json:"payment_method"`
	PaymentChannel   string     `json:"payment_channel"`
	PaidAt           *time.Time `json:"paid_at"`
}

type PayloadDigitalPayCompleted struct {
	CompanyID        string                 `json:"company_id,omitempty"`
	ProfileID        string                 `json:"profile_id,omitempty"`
	TransactionID    string                 `json:"transaction_id"`
	MerchantID       string                 `json:"merchant_id"`
	ReferenceNo      string                 `json:"reference_no"`
	BuyerID          string                 `json:"buyer_id"`
	BuyerName        string                 `json:"buyer_name"`
	BuyerEmail       string                 `json:"buyer_email"`
	Amount           float64                `json:"amount"`
	NetAmount        float64                `json:"net_amount"`
	TotalPlatformFee float64                `json:"total_platform_fee"`
	TotalFeatureFee  float64                `json:"total_feature_fee"`
	PaymentMethod    string                 `json:"payment_method"`
	PaymentChannel   string                 `json:"payment_channel"`
	Metadata         map[string]interface{} `json:"metadata"`
	PaidAt           *time.Time             `json:"paid_at"`
}

type PayloadPaymentState struct {
	CompanyID      string     `json:"company_id"`
	ProfileID      string     `json:"profile_id"`
	TransactionID  string     `json:"transaction_id"`
	PaymentID      string     `json:"payment_id"`
	MerchantID     string     `json:"merchant_id"`
	ReferenceNo    string     `json:"reference_no"`
	BuyerID        string     `json:"buyer_id"`
	BuyerEmail     string     `json:"buyer_email"`
	Amount         float64    `json:"amount"`
	ChargeAmount   float64    `json:"charge_amount"`
	PaymentMethod  string     `json:"payment_method"`
	PaymentChannel string     `json:"payment_channel"`
	ChannelCode    string     `json:"channel_code"`
	Provider       string     `json:"provider"`
	ProductType    string     `json:"product_type"`
	State          string     `json:"state"`
	PaidAt         *time.Time `json:"paid_at,omitempty"`
	FailedAt       *time.Time `json:"failed_at,omitempty"`
	Status         string     `json:"status"`
}
