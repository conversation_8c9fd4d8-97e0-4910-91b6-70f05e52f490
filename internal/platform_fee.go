package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type PlatformFee struct {
	ID          string            `json:"_id"`
	CompanyID   string            `json:"company_id"`
	Fees        []Fee             `json:"fees"`
	Metadata    map[string]string `json:"metadata"`
	Provider    string            `json:"provider"`
	ProductType string            `json:"product_type"`
	Status      string            `json:"status"`
	CreatedAt   *time.Time        `json:"created_at"`
	UpdatedAt   *time.Time        `json:"updated_at"`
}

type PlatformFeePagin struct {
	Limit        int           `json:"limit"`
	Page         int           `json:"page"`
	Sort         string        `json:"sort"`
	TotalRecords int           `json:"total_records"`
	TotalPages   int           `json:"total_pages"`
	Records      []PlatformFee `json:"records"`
}

func (b PlatformFee) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b PlatformFee) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *PlatformFee) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreatePlatformFee struct {
	CompanyID   string             `json:"company_id,omitempty"`
	Fees        []CreateFee        `json:"fees,omitempty"`
	Metadata    *map[string]string `json:"medata,omitempty"`
	Provider    *string            `json:"provider,omitempty"`
	ProductType string             `json:"product_type,omitempty"`
	Status      string             `json:"status,omitempty"`
}

func (ct CreatePlatformFee) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.CompanyID, validation.Required),
		validation.Field(&ct.Provider, validation.Required),
		validation.Field(&ct.ProductType, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type PayloadPlatformFee struct {
	CompanyID     string `json:"company_id,omitempty"`
	TransactionID string `json:"transaction_id"`
	MerchantID    string `json:"merchant_id"`
}
