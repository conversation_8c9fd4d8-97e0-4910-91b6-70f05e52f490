package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type DisbursementCalculated struct {
	AmountStr   string  `json:"amount_str"`
	Amount      float64 `json:"amount"`
	NetAmount   float64 `json:"net_amount"`
	Fee         float64 `json:"fee"`
	PlatformFee float64 `json:"platform_fee"`
	PGFee       float64 `json:"pg_fee"`
	Provider    string  `json:"provider"`
}

func (ct DisbursementCalculated) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.AmountStr, validation.Required),
		validation.Field(&ct.Provider, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type Disbursement struct {
	ID                     string     `json:"_id"`
	CompanyID              string     `json:"company_id"`
	ProfileID              string     `json:"profile_id"`
	MerchantID             string     `json:"merchant_id"`
	MerchantBankID         string     `json:"merchant_bank_id"`
	ExternalID             string     `json:"external_id"`
	DisbursementNo         string     `json:"disbursement_no"`
	ProviderDisbursementID string     `json:"provider_disbursement_id"`
	Amount                 float64    `json:"amount"`
	NetAmount              float64    `json:"net_amount"`
	Fee                    float64    `json:"fee"`
	PlatformFee            float64    `json:"platform_fee"`
	PGFee                  float64    `json:"pg_fee"`
	BankCode               string     `json:"bank_code"`
	AccountHolderName      string     `json:"account_holder_name"`
	AccountNumber          string     `json:"account_number"`
	Description            string     `json:"description"`
	Provider               string     `json:"provider"`
	DisbursedAt            *time.Time `json:"disbursed_at,omitempty"`
	FailedAt               *time.Time `json:"failed_at,omitempty"`
	IsInstant              bool       `json:"is_instant"`
	IsAuto                 bool       `json:"is_auto"`
	ErrorMessage           string     `json:"error_message"`
	ProviderStatus         string     `json:"provider_status"`
	Status                 string     `json:"status"`
	CreatedAt              *time.Time `json:"created_at"`
	UpdatedAt              *time.Time `json:"updated_at"`
}

type DisbursementPagin struct {
	Limit        int            `json:"limit"`
	Page         int            `json:"page"`
	Sort         string         `json:"sort"`
	TotalRecords int            `json:"total_records"`
	TotalPages   int            `json:"total_pages"`
	Records      []Disbursement `json:"records"`
}

func (b Disbursement) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.ID)),
		}
	}
	return nil
}

func (b Disbursement) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b Disbursement) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Disbursement) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreateDisbursement struct {
	CompanyID              string     `json:"company_id,omitempty"`
	ProfileID              string     `json:"profile_id,omitempty"`
	MerchantID             string     `json:"merchant_id,omitempty"`
	MerchantBankID         string     `json:"merchant_bank_id"`
	ProviderDisbursementID *string    `json:"provider_disbursement_id"`
	ExternalID             *string    `json:"external_id,omitempty"`
	DisbursementNo         *string    `json:"disbursement_no,omitempty"`
	Amount                 *float64   `json:"amount,omitempty"`
	NetAmount              *float64   `json:"net_amount,omitempty"`
	PlatformFee            *float64   `json:"platform_fee,omitempty"`
	PGFee                  *float64   `json:"pg_fee,omitempty"`
	Fee                    *float64   `json:"fee,omitempty"`
	BankCode               *string    `json:"bank_code,omitempty"`
	AccountHolderName      *string    `json:"account_holder_name,omitempty"`
	AccountNumber          *string    `json:"account_number,omitempty"`
	Description            *string    `json:"description,omitempty"`
	Provider               *string    `json:"provider,omitempty"`
	DisbursedAt            *time.Time `json:"disbursed_at,omitempty"`
	FailedAt               *time.Time `json:"failed_at,omitempty"`
	IsInstant              *bool      `json:"is_instant,omitempty"`
	IsAuto                 *bool      `json:"is_auto,omitempty"`
	Status                 string     `json:"status,omitempty"`
	ErrorMessage           *string    `json:"error_message,omitempty"`
	ProviderStatus         *string    `json:"provider_status,omitempty"`
}

func (ct CreateDisbursement) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.MerchantID, validation.Required),
		validation.Field(&ct.Amount, validation.Required),
		validation.Field(&ct.MerchantBankID, validation.Required),
		validation.Field(&ct.Provider, validation.Required),
		validation.Field(&ct.Description, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type PayloadAutoDisburse struct {
	CompanyID      string  `json:"company_id"`
	ProfileID      string  `json:"profile_id"`
	MerchantID     string  `json:"merchant_id"`
	MerchantBankID string  `json:"merchant_bank_id"`
	Amount         float64 `json:"amount"`
	AccountNumber  string  `json:"account_number"`
	Provider       string  `json:"provider"`
	Description    string  `json:"description"`
}

type QueryDisbursement struct {
	DisbursementNo    string `json:"disbursement_no,omitempty"`
	Amount            string `json:"amount,omitempty"`
	BankCode          string `json:"bank_code,omitempty"`
	AccountHolderName string `json:"account_holder_name,omitempty"`
	AccountNumber     string `json:"account_number,omitempty"`
	Provider          string `json:"provider,omitempty"`
	Status            string `json:"status,omitempty"`
}
