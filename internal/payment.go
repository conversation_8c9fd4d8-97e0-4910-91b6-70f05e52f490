package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Payment struct {
	ID                        string                 `json:"_id"`
	CompanyID                 string                 `json:"company_id"`
	ProfileID                 string                 `json:"profile_id"`
	MerchantID                string                 `json:"merchant_id"`
	BuyerID                   string                 `json:"buyer_id"`
	ReferrerID                string                 `json:"referrer_id"`
	MarketingID               string                 `json:"marketing_id"`
	VoucherID                 string                 `json:"voucher_id"`
	TransactionID             string                 `json:"transaction_id"`
	TransactionNo             string                 `json:"transaction_no"`
	BuyerEmail                string                 `json:"buyer_email"`
	PaymentNo                 string                 `json:"payment_no"`
	UniqueNo                  string                 `json:"unique_no"`
	ReferenceNo               string                 `json:"reference_no"`
	ExternalID                string                 `json:"external_id"`
	ProviderInvoiceID         string                 `json:"provider_invoice_id"`
	ProviderPaymentID         string                 `json:"provider_payment_id"`
	ProviderBusinessID        string                 `json:"provider_business_id"`
	PaymentType               string                 `json:"payment_type"`
	PaymentMethod             string                 `json:"payment_method"`
	PaymentChannel            string                 `json:"payment_channel"`
	QRString                  string                 `json:"qr_string"`
	VirtualAccount            string                 `json:"virtual_account"`
	EWalletType               string                 `json:"ewallet_type"`
	CheckoutMethod            string                 `json:"checkout_method"`
	ChannelCode               string                 `json:"channel_code"`
	SuccessRedirectURL        string                 `json:"success_redirect_url"`
	FailureRedirectURL        string                 `json:"failure_redirect_url"`
	IsRedirectRequired        bool                   `json:"is_redirect_required"`
	MobileNumber              string                 `json:"mobile_number"`
	CashTag                   string                 `json:"cash_tag"`
	DesktopWebCheckoutURL     string                 `json:"desktop_web_checkout_url"`
	MobileWebCheckoutURL      string                 `json:"mobile_web_checkout_url"`
	MobileDeepLinkCheckoutURL string                 `json:"mobile_deep_link_checkout_url"`
	Description               string                 `json:"description"`
	Amount                    float64                `json:"amount"`
	ChargeAmount              float64                `json:"charge_amount"`
	PGFee                     float64                `json:"pg_fee"`
	PGRealFee                 float64                `json:"pg_real_fee"`
	PGPlatformFee             float64                `json:"pg_platform_fee"`
	Fees                      []FeeDetail            `json:"fees"`
	Notes                     string                 `json:"notes"`
	Metadata                  map[string]interface{} `json:"metadata"`
	Currency                  string                 `json:"currency"`
	UseVoucher                bool                   `json:"use_voucher"`
	VoucherCode               string                 `json:"voucher_code"`
	Provider                  string                 `json:"provider"`
	Status                    string                 `json:"status"`
	TimeRemaining             int64                  `json:"time_remaining,omitempty"`
	ExpiresAt                 *time.Time             `json:"expires_at"`
	PaidAt                    *time.Time             `json:"paid_at"`
	FailedAt                  *time.Time             `json:"failed_at"`
	OverdueAt                 *time.Time             `json:"overdue_at"`
	CreatedAt                 *time.Time             `json:"created_at"`
	UpdatedAt                 *time.Time             `json:"updated_at"`
}

type PaymentPagin struct {
	Limit        int       `json:"limit"`
	Page         int       `json:"page"`
	Sort         string    `json:"sort"`
	TotalRecords int       `json:"total_records"`
	TotalPages   int       `json:"total_pages"`
	Records      []Payment `json:"records"`
}

func (b Payment) ValidateExist() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", b.PaymentNo)),
		}
	}
	return nil
}

func (b Payment) ValidateRegistered() error {
	if b.ID != "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already registered.", b.ProfileID)),
		}
	}
	return nil
}

func (b Payment) ValidateNotExist() error {
	if b.ID == "" {
		return validation.Errors{
			util.GetStructName(b): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", b.ID)),
		}
	}
	return nil
}

func (b *Payment) ResponseData() interface{} {
	if b.ID == "" {
		return map[string]interface{}{}
	} else {
		return b
	}
}

type CreatePayment struct {
	CompanyID                 string                  `json:"company_id,omitempty"`
	ProfileID                 string                  `json:"profile_id,omitempty"`
	MerchantID                string                  `json:"merchant_id,omitempty"`
	BuyerID                   string                  `json:"buyer_id,omitempty"`
	ReferrerID                string                  `json:"referrer_id,omitempty"`
	MarketingID               string                  `json:"marketing_id,omitempty"`
	VoucherID                 string                  `json:"voucher_id,omitempty"`
	TransactionID             string                  `json:"transaction_id,omitempty"`
	TransactionNo             *string                 `json:"transaction_no,omitempty"`
	BuyerEmail                *string                 `json:"buyer_email,omitempty"`
	UniqueNo                  *string                 `json:"unique_no,omitempty"`
	PaymentNo                 *string                 `json:"payment_no,omitempty"`
	ReferenceNo               *string                 `json:"reference_no,omitempty"`
	ExternalID                *string                 `json:"external_id,omitempty"`
	ProviderInvoiceID         *string                 `json:"provider_invoice_id,omitempty"`
	ProviderPaymentID         *string                 `json:"provider_payment_id,omitempty"`
	ProviderBusinessID        *string                 `json:"provider_business_id,omitempty"`
	PaymentType               *string                 `json:"payment_type,omitempty"`
	CardCvn                   *string                 `json:"card_cvn,omitempty"`
	PaymentMethod             *string                 `json:"payment_method,omitempty"`
	PaymentChannel            *string                 `json:"payment_channel,omitempty"`
	QRString                  *string                 `json:"qr_string,omitempty"`
	VirtualAccount            *string                 `json:"virtual_account,omitempty"`
	TokenID                   *string                 `json:"token_id,omitempty"`
	EWalletType               *string                 `json:"ewallet_type,omitempty"`
	CheckoutMethod            *string                 `json:"checkout_method,omitempty"`
	ChannelCode               *string                 `json:"channel_code,omitempty"`
	SuccessRedirectURL        *string                 `json:"success_redirect_url,omitempty"`
	FailureRedirectURL        *string                 `json:"failure_redirect_url,omitempty"`
	IsRedirectRequired        *bool                   `json:"is_redirect_required,omitempty"`
	MobileNumber              *string                 `json:"mobile_number,omitempty"`
	CashTag                   *string                 `json:"cash_tag,omitempty"`
	DesktopWebCheckoutURL     *string                 `json:"desktop_web_checkout_url,omitempty"`
	MobileWebCheckoutURL      *string                 `json:"mobile_web_checkout_url,omitempty"`
	MobileDeepLinkCheckoutURL *string                 `json:"mobile_deep_link_checkout_url,omitempty"`
	Description               *string                 `json:"description,omitempty"`
	Amount                    *float64                `json:"amount,omitempty"`
	ChargeAmount              *float64                `json:"charge_amount,omitempty"`
	PGFee                     *float64                `json:"pg_fee,omitempty"`
	PGRealFee                 *float64                `json:"pg_real_fee,omitempty"`
	PGPlatformFee             *float64                `json:"pg_platform_fee,omitempty"`
	Fees                      []FeeDetail             `json:"fees,omitempty"`
	Notes                     *string                 `json:"notes,omitempty"`
	Metadata                  *map[string]interface{} `json:"metadata,omitempty"`
	Currency                  *string                 `json:"currency,omitempty"`
	UseVoucher                *bool                   `json:"use_voucher,omitempty"`
	VoucherCode               *string                 `json:"voucher_code,omitempty"`
	Provider                  *string                 `json:"provider,omitempty"`
	Status                    string                  `json:"status,omitempty"`
	ExpiresAt                 *time.Time              `json:"expires_at,omitempty"`
	PaidAt                    *time.Time              `json:"paid_at,omitempty"`
	FailedAt                  *time.Time              `json:"failed_at,omitempty"`
	OverdueAt                 *time.Time              `json:"overdue_at,omitempty"`
}

func (ct CreatePayment) Validate() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.TransactionID, validation.Required),
		validation.Field(&ct.PaymentMethod, validation.Required, validation.In("qr", "va", "ewallet", "credit_card")),
		validation.Field(&ct.Provider, validation.Required, validation.In("xendit")),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (ct CreatePayment) ValidatePayCharge() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.TransactionID, validation.Required),
		validation.Field(&ct.PaymentMethod, validation.Required, validation.In("qr", "va", "ewallet", "credit_card")),
		validation.Field(&ct.Provider, validation.Required, validation.In("xendit")),
		validation.Field(&ct.ChannelCode, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (ct CreatePayment) ValidateEwalletOVO() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.TransactionID, validation.Required),
		validation.Field(&ct.PaymentMethod, validation.Required, validation.In("qr", "va", "ewallet", "credit_card")),
		validation.Field(&ct.Provider, validation.Required, validation.In("xendit")),
		validation.Field(&ct.ChannelCode, validation.Required),
		validation.Field(&ct.MobileNumber, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (ct CreatePayment) ValidateEwalletJenius() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.TransactionID, validation.Required),
		validation.Field(&ct.PaymentMethod, validation.Required, validation.In("qr", "va", "ewallet", "credit_card")),
		validation.Field(&ct.Provider, validation.Required, validation.In("xendit")),
		validation.Field(&ct.ChannelCode, validation.Required),
		validation.Field(&ct.CashTag, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (ct CreatePayment) ValidateCreditCard() error {
	if err := validation.ValidateStruct(&ct,
		validation.Field(&ct.TransactionID, validation.Required),
		validation.Field(&ct.PaymentMethod, validation.Required, validation.In("qr", "va", "ewallet", "credit_card")),
		validation.Field(&ct.Provider, validation.Required, validation.In("xendit")),
		validation.Field(&ct.ChannelCode, validation.Required),
		validation.Field(&ct.TokenID, validation.Required),
		validation.Field(&ct.ExternalID, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type QrisResponse struct {
	ID            string     `json:"_id,omitempty"`
	TransactionID string     `json:"transaction_id,omitempty"`
	ReferenceNo   string     `json:"reference_no,omitempty"`
	ExternalID    string     `json:"external_id,omitempty"`
	QRString      string     `json:"qr_string,omitempty"`
	Amount        float64    `json:"amount,omitempty"`
	Currency      string     `json:"currency,omitempty"`
	ExpiresAt     *time.Time `json:"expires_at,omitempty"`
	Status        string     `json:"status,omitempty"`
	TimeRemaining int64      `json:"time_remaining,omitempty"`
}

type QueryPayment struct {
	Name           string `json:"name,omitempty"`
	TipNo          string `json:"tip_no,omitempty"`
	Email          string `json:"email,omitempty"`
	PaymentMethod  string `json:"payment_method,omitempty"`
	PaymentGateway string `json:"payment_gateway,omitempty"`
	PaymentStatus  string `json:"payment_status,omitempty"`
	CreatedAt      string `json:"created_at,omitempty"`
}

type PaymentResponse struct {
	ID                        string            `json:"_id,omitempty"`
	CompanyID                 string            `json:"company_id,omitempty"`
	ProfileID                 string            `json:"profile_id,omitempty"`
	MerchantID                string            `json:"merchant_id,omitempty"`
	BuyerID                   string            `json:"buyer_id,omitempty"`
	ReferrerID                string            `json:"referrer_id,omitempty"`
	MarketingID               string            `json:"marketing_id,omitempty"`
	VoucherID                 string            `json:"voucher_id,omitempty"`
	TransactionID             string            `json:"transaction_id,omitempty"`
	TransactionNo             string            `json:"transaction_no,omitempty"`
	BuyerEmail                string            `json:"buyer_email,omitempty"`
	PaymentNo                 string            `json:"payment_no,omitempty"`
	UniqueNo                  string            `json:"unique_no,omitempty"`
	ReferenceNo               string            `json:"reference_no,omitempty"`
	ExternalID                string            `json:"external_id,omitempty"`
	ProviderInvoiceID         string            `json:"provider_invoice_id,omitempty"`
	ProviderPaymentID         string            `json:"provider_payment_id,omitempty"`
	ProviderBusinessID        string            `json:"provider_business_id,omitempty"`
	PaymentType               string            `json:"payment_type,omitempty"`
	PaymentMethod             string            `json:"payment_method,omitempty"`
	PaymentChannel            string            `json:"payment_channel,omitempty"`
	QRString                  string            `json:"qr_string,omitempty"`
	VirtualAccount            string            `json:"virtual_account,omitempty"`
	EWalletType               string            `json:"ewallet_type,omitempty"`
	CheckoutMethod            string            `json:"checkout_method,omitempty"`
	ChannelCode               string            `json:"channel_code,omitempty"`
	SuccessRedirectURL        string            `json:"success_redirect_url,omitempty"`
	FailureRedirectURL        string            `json:"failure_redirect_url,omitempty"`
	IsRedirectRequired        bool              `json:"is_redirect_required,omitempty"`
	MobileNumber              string            `json:"mobile_number,omitempty"`
	TokenID                   string            `json:"token_id,omitempty"`
	CashTag                   string            `json:"cash_tag,omitempty"`
	DesktopWebCheckoutURL     string            `json:"desktop_web_checkout_url,omitempty"`
	MobileWebCheckoutURL      string            `json:"mobile_web_checkout_url,omitempty"`
	MobileDeepLinkCheckoutURL string            `json:"mobile_deep_link_checkout_url,omitempty"`
	Description               string            `json:"description,omitempty"`
	Amount                    float64           `json:"amount,omitempty"`
	ChargeAmount              float64           `json:"charge_amount,omitempty"`
	PGFee                     float64           `json:"pg_fee,omitempty"`
	PGRealFee                 float64           `json:"pg_real_fee,omitempty"`
	PGPlatformFee             float64           `json:"pg_platform_fee,omitempty"`
	Fees                      []FeeDetail       `json:"fees,omitempty"`
	Notes                     string            `json:"notes,omitempty"`
	Metadata                  map[string]string `json:"metadata,omitempty"`
	Currency                  string            `json:"currency,omitempty"`
	UseVoucher                bool              `json:"use_voucher,omitempty"`
	VoucherCode               string            `json:"voucher_code,omitempty"`
	Provider                  string            `json:"provider,omitempty"`
	Status                    string            `json:"status,omitempty"`
	TimeRemaining             int64             `json:"time_remaining,omitempty"`
	ExpiresAt                 *time.Time        `json:"expires_at,omitempty"`
	PaidAt                    *time.Time        `json:"paid_at,omitempty"`
	FailedAt                  *time.Time        `json:"failed_at,omitempty"`
	OverdueAt                 *time.Time        `json:"overdue_at,omitempty"`
	CreatedAt                 *time.Time        `json:"created_at,omitempty"`
	UpdatedAt                 *time.Time        `json:"updated_at,omitempty"`
}
