package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MerchantBank struct {
	mongorm.Model         `bson:",inline"`
	CompanyID             primitive.ObjectID `bson:"company_id,omitempty"`
	ProfileID             primitive.ObjectID `bson:"profile_id,omitempty"`
	MerchantID            primitive.ObjectID `bson:"merchant_id,omitempty"`
	BankID                primitive.ObjectID `bson:"bank_id,omitempty"`
	BankName              *string            `bson:"bank_name,omitempty"`
	BankBranch            *string            `bson:"bank_branch,omitempty"`
	BankAccountNumber     *string            `bson:"bank_account_number,omitempty"`
	BankAccountHolderName *string            `bson:"bank_account_holder_name,omitempty"`
	BankDetail            *Bank              `bson:"bank_detail,omitempty"`
	IsDefault             *bool              `bson:"is_default,omitempty"`
	Metadata              *map[string]string `bson:"metadata,omitempty"`
	Status                uint8              `bson:"status,omitempty"`
}

const MerchantBankCollection = "merchant_banks"
