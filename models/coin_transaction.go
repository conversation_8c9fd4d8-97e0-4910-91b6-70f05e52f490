package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CoinTransaction struct {
	mongorm.Model `bson:",inline"`
	CoinID        primitive.ObjectID  `bson:"coin_id,omitempty"`
	CompanyID     primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID     primitive.ObjectID  `bson:"profile_id,omitempty"`
	Type          *int                `bson:"type,omitempty"`
	TypeID        *primitive.ObjectID `bson:"type_id,omitempty"`
	SourceID      *primitive.ObjectID `bson:"source_id,omitempty"`
	Balance       *float64            `bson:"balance,omitempty"`
	BalanceBefore *float64            `bson:"balance_before,omitempty"`
	Description   *string             `bson:"description,omitempty"`
	Metadata      *map[string]string  `bson:"metadata,omitempty"`
	Status        uint8               `bson:"status,omitempty"`
}

const CoinTransactionCollection = "coin_transactions"
