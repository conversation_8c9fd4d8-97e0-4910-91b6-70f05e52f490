package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MerchantPlatformFee struct {
	mongorm.Model          `bson:",inline"`
	CompanyID              primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID              primitive.ObjectID  `bson:"profile_id,omitempty"`
	MerchantID             primitive.ObjectID  `bson:"merchant_id,omitempty"`
	ReferrerID             *primitive.ObjectID `bson:"referrer_id"`
	MarketingID            *primitive.ObjectID `bson:"marketing_id"`
	Fees                   []FeeDetail         `bson:"fees,omitempty"`
	Metadata               *map[string]string  `bson:"metadata,omitempty"`
	Provider               *string             `bson:"provider,omitempty"`
	HasMarketingFee        *bool               `bson:"has_marketing_fee,omitempty"`
	HasReferralFee         *bool               `bson:"has_referral_fee,omitempty"`
	HasCashbackProviderFee *bool               `bson:"has_cashback_provider_fee,omitempty"`
	ProductType            string              `bson:"product_type,omitempty"`
	Status                 uint8               `bson:"status,omitempty"`
}

type MerchantPlatformFeeUpdate struct {
	mongorm.Model          `bson:",inline"`
	CompanyID              primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID              primitive.ObjectID  `bson:"profile_id,omitempty"`
	MerchantID             primitive.ObjectID  `bson:"merchant_id,omitempty"`
	ReferrerID             *primitive.ObjectID `bson:"referrer_id,omitempty"`
	MarketingID            *primitive.ObjectID `bson:"marketing_id,omitempty"`
	Fees                   []FeeDetail         `bson:"fees,omitempty"`
	Metadata               *map[string]string  `bson:"metadata,omitempty"`
	Provider               *string             `bson:"provider,omitempty"`
	HasMarketingFee        *bool               `bson:"has_marketing_fee,omitempty"`
	HasReferralFee         *bool               `bson:"has_referral_fee,omitempty"`
	HasCashbackProviderFee *bool               `bson:"has_cashback_provider_fee,omitempty"`
	ProductType            string              `bson:"product_type,omitempty"`
	Status                 uint8               `bson:"status,omitempty"`
}

const MerchantPlatformFeeCollection = "merchant_platform_fees"
