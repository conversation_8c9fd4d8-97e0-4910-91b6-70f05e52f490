package models

import (
	"github.com/continue-team/riot/mongorm"
)

type Provider struct {
	mongorm.Model `bson:",inline"`
	Slug          *string            `bson:"slug,omitempty"`
	Name          *string            `bson:"name,omitempty"`
	Description   *string            `bson:"description,omitempty"`
	Secret        *map[string]string `bson:"secret,omitempty"`
	Status        uint8              `bson:"status,omitempty"`
}

const ProviderCollection = "providers"
