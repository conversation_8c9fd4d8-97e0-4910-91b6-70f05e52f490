package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Coin struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID `bson:"company_id,omitempty"`
	ProfileID     primitive.ObjectID `bson:"profile_id,omitempty"`
	LoginID       *string            `bson:"login_id,omitempty"`
	Balance       *float64           `bson:"balance,omitempty"`
	Description   *string            `bson:"description,omitempty"`
	Metadata      *map[string]string `bson:"metadata,omitempty"`
	Status        uint8              `bson:"status,omitempty"`
}

const CoinCollection = "coins"
