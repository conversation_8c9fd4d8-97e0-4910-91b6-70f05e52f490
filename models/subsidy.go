package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Subsidy struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID     primitive.ObjectID  `bson:"profile_id,omitempty"`
	MerchantID    primitive.ObjectID  `bson:"merchant_id,omitempty"`
	TransactionID primitive.ObjectID  `bson:"transaction_id,omitempty"`
	VoucherID     *primitive.ObjectID `bson:"voucher_id,omitempty"`
	PGFee         *float64            `bson:"pg_fee,omitempty"`
	FeatureFee    *float64            `bson:"feature_fee,omitempty"`
	Amount        *float64            `bson:"amount,omitempty"`
	Description   *string             `bson:"description,omitempty"`
	Metadata      *map[string]string  `bson:"metadata,omitempty"`
	Status        uint8               `bson:"status,omitempty"`
}

const SubsidyCollection = "subsidies"
