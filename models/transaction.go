package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Transaction struct {
	mongorm.Model      `bson:",inline"`
	CompanyID          primitive.ObjectID      `bson:"company_id,omitempty"`
	ProfileID          primitive.ObjectID      `bson:"profile_id,omitempty"`
	BuyerID            *primitive.ObjectID     `bson:"buyer_id,omitempty"`
	MerchantID         primitive.ObjectID      `bson:"merchant_id,omitempty"`
	ReferrerID         *primitive.ObjectID     `bson:"referrer_id,omitempty"`
	MarketingID        *primitive.ObjectID     `bson:"marketing_id,omitempty"`
	VoucherID          *primitive.ObjectID     `bson:"voucher_id,omitempty"`
	UniqueNo           *string                 `bson:"unique_no,omitempty"`
	BuyerName          *string                 `bson:"buyer_name,omitempty"`
	BuyerEmail         *string                 `bson:"buyer_email,omitempty"`
	SellerName         *string                 `bson:"seller_name,omitempty"`
	SellerEmail        *string                 `bson:"seller_email,omitempty"`
	TransactionNo      *string                 `bson:"transaction_no,omitempty"`
	ReferenceNo        *string                 `bson:"reference_no,omitempty"` //TIP-XXXXX-XXXXX, SUB-XXXXXXX-XSSS
	Description        *string                 `bson:"description,omitempty"`
	Amount             *float64                `bson:"amount,omitempty"`
	NetAmount          *float64                `bson:"net_amount,omitempty"` //NetAmount = Amount - (Tax + TotalFeatureFee)
	TotalPayment       *float64                `bson:"total_payment,omitempty"`
	PlatformFeeType    *string                 `bson:"platform_fee_type,omitempty"`
	PlatformFeeValue   *float64                `bson:"platform_fee_value,omitempty"`
	TotalReferrerFee   *float64                `bson:"total_referrer_fee,omitempty"`
	TotalMarketingFee  *float64                `bson:"total_marketing_fee,omitempty"`
	TotalPlatformFee   *float64                `bson:"total_platform_fee,omitempty"`
	TotalFeatureFee    *float64                `bson:"total_feature_fee,omitempty"`
	TotalSubsidyFee    *float64                `bson:"total_subsidy_fee,omitempty"`
	TotalPaymentFee    *float64                `bson:"total_payment_fee,omitempty"`
	TotalDiscount      *float64                `bson:"total_discount,omitempty"`
	TotalFee           *float64                `bson:"total_fee,omitempty"`
	ThirdPartyFee      *float64                `bson:"third_party_fee,omitempty"`
	PGFee              *float64                `bson:"pg_fee,omitempty"`
	PGRealFee          *float64                `bson:"pg_real_fee,omitempty"`
	PGPlatformFee      *float64                `bson:"pg_platform_fee,omitempty"`
	DescriptionSubsidy *string                 `bson:"subsidy_description,omitempty"`
	PaymentMethod      *string                 `bson:"payment_method,omitempty"`
	PaymentType        *string                 `bson:"payment_type,omitempty"`
	PaymentChannel     *string                 `bson:"payment_channel,omitempty"`
	Fee                *Fee                    `bson:"fee,omitempty"`
	Tax                *float64                `bson:"tax,omitempty"`
	Features           *[]string               `bson:"features,omitempty"`
	UseVoucher         *bool                   `bson:"use_voucher,omitempty"`
	VoucherCode        *string                 `bson:"voucher_code,omitempty"`
	ProductType        *string                 `bson:"product_type,omitempty"` //tip, subscription, etc
	Metadata           *map[string]interface{} `bson:"metadata,omitempty"`
	Status             uint8                   `bson:"status,omitempty"` //0: pending, 1: success, 2: failed
}

const TransactionCollection = "transactions"
