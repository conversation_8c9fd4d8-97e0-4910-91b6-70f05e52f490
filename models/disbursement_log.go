package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Disbursement represents a financial transaction where funds are sent to a specified bank account.
type DisbursementLog struct {
	mongorm.Model          `bson:",inline"`
	CompanyID              primitive.ObjectID `bson:"company_id,omitempty"`
	ProfileID              primitive.ObjectID `bson:"profile_id,omitempty"`
	MerchantID             primitive.ObjectID `bson:"merchant_id,omitempty"`
	MerchantBankID         primitive.ObjectID `bson:"merchant_bank_id,omitempty"`
	DisbursementID         primitive.ObjectID `bson:"disbursement_id,omitempty"`
	ExternalID             *string            `bson:"external_id,omitempty"`
	DisbursementNo         *string            `bson:"disbursement_no,omitempty"`
	ProviderDisbursementID *string            `bson:"provider_disbursement_id"`
	Provider               *string            `bson:"provider,omitempty"`
	Status                 uint8              `bson:"status,omitempty"`        // Status of the disbursement (e.g., "PENDING", "COMPLETED", "FAILED")
	ErrorMessage           *string            `bson:"error_message,omitempty"` // Optional error message if the disbursement failed
	ProviderStatus         *string            `bson:"provider_status,omitempty"`
}

const DisbursementLogCollection = "disbursement_logs"
