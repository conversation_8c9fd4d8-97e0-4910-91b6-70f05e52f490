package models

import (
	"time"

	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Voucher struct {
	mongorm.Model  `bson:",inline"`
	CompanyID      primitive.ObjectID `bson:"company_id,omitempty"`
	Code           *string            `bson:"code,omitempty"`
	Fees           []FeeDetail        `bson:"fees,omitempty"`
	DiscountType   *string            `bson:"discount_type,omitempty"` // percentage, fixed, custom
	DiscountValue  *float64           `bson:"discount_value,omitempty"`
	MinPurchase    *float64           `bson:"min_purchase,omitempty"`
	MaxDiscount    *float64           `bson:"max_discount,omitempty"`
	UsageLimit     *int               `bson:"usage_limit,omitempty"`
	EnableLimit    *bool              `bson:"enable_limit,omitempty"`
	RemainingUsage *int               `bson:"remaining_usage,omitempty"`
	ExpiresAt      *time.Time         `bson:"expires_at,omitempty"`
	Description    *string            `bson:"description,omitempty"`

	// Expiration Type (Defines how the expiration is determined)
	ExpireType string `bson:"expire_type,omitempty"`
	// Possible values:
	// "fixed_range" - Expires within a fixed date range (ValidFrom - ValidTo)
	// "dynamic_period" - Expires a set period after being claimed (Period & PeriodType)
	// "rolling_window" - Valid for a period after claim but has an absolute expiry date

	ValidFrom *time.Time `bson:"valid_from,omitempty"`
	ValidTo   *time.Time `bson:"valid_to,omitempty"`

	// Defines the duration for "dynamic_period" expiration type
	Period     *int    `bson:"period,omitempty"`      // Integer value
	PeriodType *string `bson:"period_type,omitempty"` // "day" or "month"

	LevelPriority *int    `bson:"level_priority,omitempty"`
	Type          *string `bson:"type,omitempty"`
	// Possible values:
	// "event" - Voucher for a specific event within a date range
	// "registration" - Voucher granted upon registration with a dynamic expiration
	// "promo_campaign" - Voucher used for promotional campaigns
	// "subscription_bonus" - Reward voucher for subscription or loyalty programs

	Combined         *bool              `bson:"combined,omitempty"`
	CombinationTypes *[]string          `bson:"combination_types,omitempty"` // ["voucher", "cashback", "discount"]
	Metadata         *map[string]string `bson:"metadata,omitempty"`
	Status           uint8              `bson:"status,omitempty"`
	ProductType      string             `bson:"product_type,omitempty"`
}

const VoucherCollection = "vouchers"
