package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MerchantCommissionFee struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID     primitive.ObjectID  `bson:"profile_id,omitempty"`
	MerchantID    primitive.ObjectID  `bson:"merchant_id,omitempty"`
	DownlineID    primitive.ObjectID  `bson:"downline_id,omitempty"`
	ReffID        *primitive.ObjectID `bson:"reff_id,omitempty"`
	Fees          []FeeDetail         `bson:"fees,omitempty"`
	Metadata      *map[string]string  `bson:"metadata,omitempty"`
	Provider      *string             `bson:"provider,omitempty"`
	Status        uint8               `bson:"status,omitempty"`
}

const MerchantCommissionCollection = "merchant_commission_fees"
