package models

import (
	"github.com/continue-team/riot/mongorm"
)

type Bank struct {
	mongorm.Model `bson:",inline"`
	Slug          *string            `bson:"slug,omitempty"`
	Name          *string            `bson:"name,omitempty"`
	BankCode      *string            `bson:"bank_code,omitempty"`
	Description   *string            `bson:"description,omitempty"`
	Metadata      *map[string]string `bson:"metadata,omitempty"`
	CanDisburse   *bool              `bson:"can_disburse,omitempty"`
	Status        uint8              `bson:"status,omitempty"`
}

const BankCollection = "banks"
