package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BalanceTransaction struct {
	mongorm.Model `bson:",inline"`
	MerchantID    primitive.ObjectID      `bson:"merchant_id,omitempty"`
	CompanyID     primitive.ObjectID      `bson:"company_id,omitempty"`
	ProfileID     primitive.ObjectID      `bson:"profile_id,omitempty"`
	Type          *int                    `bson:"type,omitempty"`
	TypeObject    *string                 `bson:"type_object,omitempty"`
	TypeID        *primitive.ObjectID     `bson:"type_id,omitempty"`
	SourceID      *primitive.ObjectID     `bson:"source_id,omitempty"`
	Balance       *float64                `bson:"balance,omitempty"`
	BalanceBefore *float64                `bson:"balance_before,omitempty"`
	Description   *string                 `bson:"description,omitempty"`
	ProductType   *string                 `bson:"product_type,omitempty"`
	Metadata      *map[string]interface{} `bson:"metadata,omitempty"`
	Status        uint8                   `bson:"status,omitempty"`
}

const BalanceTransactionCollection = "balance_transactions"
