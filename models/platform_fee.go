package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PlatformFee struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID `bson:"company_id,omitempty"`
	Fees          []FeeDetail        `bson:"fees,omitempty"`
	Metadata      *map[string]string `bson:"metadata,omitempty"`
	Provider      *string            `bson:"provider,omitempty"`
	ProductType   string             `bson:"product_type,omitempty"`
	Status        uint8              `bson:"status,omitempty"`
}

const PlatformFeeCollection = "platform_fees"
