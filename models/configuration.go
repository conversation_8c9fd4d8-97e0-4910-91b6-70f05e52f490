package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Configuration struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID `bson:"company_id,omitempty"`
	Slug          string             `bson:"slug,omitempty"`
	Name          *string            `bson:"name,omitempty"`
	DisplayName   *string            `bson:"display_name,omitempty"`
	Description   *string            `bson:"description,omitempty"`
	FeeType       *string            `bson:"fee_type,omitempty"`
	FeeValue      *float64           `bson:"fee_value,omitempty"`
	Currency      *string            `bson:"currency,omitempty"`
	Metadata      *map[string]string `bson:"metadata,omitempty"`
	Status        uint8              `bson:"status,omitempty"`
}

const ConfigurationCollection = "configurations"
