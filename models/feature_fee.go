package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FeatureFee struct {
	mongorm.Model  `bson:",inline"`
	CompanyID      primitive.ObjectID `bson:"company_id,omitempty"`
	Name           string             `bson:"name,omitempty"`
	Description    *string            `bson:"description,omitempty"`
	FeeType        *string            `bson:"fee_type,omitempty"`
	FeeValue       *float64           `bson:"fee_value,omitempty"`
	MinTransaction *float64           `bson:"min_transaction,omitempty"` // Fee applies if transaction >= this value
	MaxTransaction *float64           `bson:"max_transaction,omitempty"` // Fee applies if transaction <= this value
	Currency       *string            `bson:"currency,omitempty"`
	Status         uint8              `bson:"status,omitempty"`
}

const FeatureFeeCollection = "feature_fees"
