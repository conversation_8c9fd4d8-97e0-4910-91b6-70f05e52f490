package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Merchant struct {
	mongorm.Model     `bson:",inline"`
	CompanyID         primitive.ObjectID    `bson:"company_id,omitempty"`
	ProfileID         primitive.ObjectID    `bson:"profile_id,omitempty"`
	Slug              *string               `bson:"slug,omitempty"`
	Name              *string               `bson:"name,omitempty"`
	LoginID           *string               `bson:"login_id,omitempty"`
	Email             *string               `bson:"email,omitempty"`
	ProfilePictureURL *string               `bson:"profile_picture_url,omitempty"`
	SubAccounts       *[]primitive.ObjectID `bson:"subaccounts,omitempty"`
	Description       *string               `bson:"description,omitempty"`
	Balance           *float64              `bson:"balance,omitempty"`
	SettledBalance    *float64              `bson:"settled_balance,omitempty"`
	TotalDisburse     *float64              `bson:"total_disburse,omitempty"`
	ReferrerID        *primitive.ObjectID   `bson:"referrer_id"`
	MarketingID       *primitive.ObjectID   `bson:"marketing_id"`
	VoucherID         *primitive.ObjectID   `bson:"voucher_id"`
	Metadata          *map[string]string    `bson:"metadata,omitempty"`
	Status            uint8                 `bson:"status,omitempty"`
}

type MerchantUpdate struct {
	mongorm.Model     `bson:",inline"`
	CompanyID         primitive.ObjectID    `bson:"company_id,omitempty"`
	ProfileID         primitive.ObjectID    `bson:"profile_id,omitempty"`
	Slug              *string               `bson:"slug,omitempty"`
	Name              *string               `bson:"name,omitempty"`
	LoginID           *string               `bson:"login_id,omitempty"`
	Email             *string               `bson:"email,omitempty"`
	ProfilePictureURL *string               `bson:"profile_picture_url,omitempty"`
	SubAccounts       *[]primitive.ObjectID `bson:"subaccounts,omitempty"`
	Description       *string               `bson:"description,omitempty"`
	Balance           *float64              `bson:"balance,omitempty"`
	SettledBalance    *float64              `bson:"settled_balance,omitempty"`
	TotalDisburse     *float64              `bson:"total_disburse,omitempty"`
	ReferrerID        *primitive.ObjectID   `bson:"referrer_id,omitempty"`
	MarketingID       *primitive.ObjectID   `bson:"marketing_id,omitempty"`
	VoucherID         *primitive.ObjectID   `bson:"voucher_id,omitempty"`
	Metadata          *map[string]string    `bson:"metadata,omitempty"`
	Status            uint8                 `bson:"status,omitempty"`
}

const MerchantCollection = "merchants"
