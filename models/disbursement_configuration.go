package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Disbursement represents a financial transaction where funds are sent to a specified bank account.
type DisbursementConfiguration struct {
	mongorm.Model  `bson:",inline"`
	CompanyID      primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID      primitive.ObjectID  `bson:"profile_id,omitempty"`
	MerchantID     primitive.ObjectID  `bson:"merchant_id,omitempty"`
	MerchantBankID *primitive.ObjectID `bson:"merchant_bank_id,omitempty"`
	AutoDisburse   *bool               `bson:"auto_disburse,omitempty"`
	TotalAmount    *float64            `bson:"total_amount,omitempty"`
	Status         uint8               `bson:"status,omitempty"`
}

const DisbursementConfigurationCollection = "disbursement_configurations"
