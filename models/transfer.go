package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Transfer struct {
	mongorm.Model  `bson:",inline"`
	CompanyID      primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID      primitive.ObjectID  `bson:"profile_id,omitempty"`
	MerchantID     primitive.ObjectID  `bson:"merchant_id,omitempty"`
	TransactionID  *primitive.ObjectID `bson:"transaction_id,omitempty"`
	DisbursementID *primitive.ObjectID `bson:"disbursement_id,omitempty"`
	DownlineID     *primitive.ObjectID `bson:"downline_id,omitempty"`
	Amount         *float64            `bson:"amount,omitempty"`
	Type           *string             `bson:"type,omitempty"`
	ProductType    *string             `bson:"product_type,omitempty"`
	Description    *string             `bson:"description,omitempty"`
	Metadata       *map[string]string  `bson:"metadata,omitempty"`
	Operation      *string             `bson:"operation,omitempty"`
	Status         uint8               `bson:"status,omitempty"`
}

const TransferCollection = "transfers"
