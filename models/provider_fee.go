package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FeeDetail struct {
	PaymentMethod            *string  `bson:"payment_method,omitempty"`
	FeeType                  *string  `bson:"fee_type,omitempty"`
	FeeValue                 *float64 `bson:"fee_value,omitempty"`
	ProviderFeeType          *string  `bson:"provider_fee_type,omitempty"`
	ProviderFeeValue         *float64 `bson:"provider_fee_value,omitempty"`
	PlatformFeeType          *string  `bson:"platform_fee_type,omitempty"`
	PlatformFeeValue         *float64 `bson:"platform_fee_value,omitempty"`
	CashbackProviderFeeType  *string  `bson:"cashback_provider_fee_type,omitempty"`
	CashbackProviderFeeValue *float64 `bson:"cashback_provider_fee_value,omitempty"`
	ReferralFeeType          *string  `bson:"referral_fee_type,omitempty"`
	ReferralFeeValue         *float64 `bson:"referral_fee_value,omitempty"`
	MarketingFeeType         *string  `bson:"marketing_fee_type,omitempty"`
	MarketingFeeValue        *float64 `bson:"marketing_fee_value,omitempty"`
}

type ProviderFee struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID `bson:"company_id,omitempty"`
	ProfileID     primitive.ObjectID `bson:"profile_id,omitempty"`
	ProviderID    primitive.ObjectID `bson:"provider_id,omitempty"`
	Fees          []FeeDetail        `bson:"fees,omitempty"`
	Metadata      *map[string]string `bson:"metadata,omitempty"`
	Provider      *string            `bson:"provider,omitempty"`
	Status        uint8              `bson:"status,omitempty"`
}

const ProviderFeeCollection = "provider_fees"
