package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Fee struct {
	mongorm.Model     `bson:",inline"`
	CompanyID         primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID         *primitive.ObjectID `bson:"profile_id,omitempty"`
	ObjectID          *primitive.ObjectID `bson:"object_id,omitempty"`
	ObjectType        *string             `bson:"object_type,omitempty"`
	PaymentMethod     *string             `bson:"payment_method,omitempty"`
	FeeType           *string             `bson:"fee_type,omitempty"`
	FeeValue          *float64            `bson:"fee_value,omitempty"`
	PGFeeType         *string             `bson:"pg_fee_type,omitempty"`
	PGFeeValue        *float64            `bson:"pg_fee_value,omitempty"`
	ProviderFeeType   *string             `bson:"provider_fee_type,omitempty"`
	ProviderFeeValue  *float64            `bson:"provider_fee_value,omitempty"`
	PlatformFeeType   *string             `bson:"platform_fee_type,omitempty"`
	PlatformFeeValue  *float64            `bson:"platform_fee_value,omitempty"`
	ReferralFeeType   *string             `bson:"referral_fee_type,omitempty"`
	ReferralFeeValue  *float64            `bson:"referral_fee_value,omitempty"`
	MarkeringFeeType  *string             `bson:"markering_fee_type,omitempty"`
	MarkeringFeeValue *float64            `bson:"markering_fee_value,omitempty"`
	IsDefault         *bool               `bson:"is_default,omitempty"`
	Currency          *string             `bson:"currency,omitempty"`
	Status            uint8               `bson:"status,omitempty"`
}

const FeeCollection = "fees"
