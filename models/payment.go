package models

import (
	"time"

	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Payment struct {
	mongorm.Model      `bson:",inline"`
	CompanyID          primitive.ObjectID  `bson:"company_id,omitempty"`
	ProfileID          primitive.ObjectID  `bson:"profile_id,omitempty"`
	MerchantID         primitive.ObjectID  `bson:"merchant_id,omitempty"`
	BuyerID            *primitive.ObjectID `bson:"buyer_id,omitempty"`
	ReferrerID         *primitive.ObjectID `bson:"referrer_id,omitempty"`
	MarketingID        *primitive.ObjectID `bson:"marketing_id,omitempty"`
	VoucherID          *primitive.ObjectID `bson:"voucher_id,omitempty"`
	TransactionID      primitive.ObjectID  `bson:"transaction_id,omitempty"`
	TransactionNo      *string             `bson:"transaction_no,omitempty"`
	BuyerEmail         *string             `bson:"buyer_email,omitempty"`
	UniqueNo           *string             `bson:"unique_no,omitempty"`
	PaymentNo          *string             `bson:"payment_no,omitempty"`
	ReferenceNo        *string             `bson:"reference_no,omitempty"`        // User-friendly transaction reference (e.g., TIP-20250209-2345)
	ExternalID         *string             `bson:"external_id,omitempty"`         // Unique external transaction ID (e.g., TR-YYYYMMDD-RANDOM-TIMESTAMP)
	ProviderInvoiceID  *string             `bson:"provider_invoice_id,omitempty"` // ID invoice from Xendit
	ProviderPaymentID  *string             `bson:"provider_payment_id,omitempty"` // ID payment from Xendit
	ProviderBusinessID *string             `bson:"provider_business_id,omitempty"`
	PaymentType        *string             `bson:"payment_type,omitempty"`
	PaymentMethod      *string             `bson:"payment_method,omitempty"`
	PaymentChannel     *string             `bson:"payment_channel,omitempty"` // Specific payment channel used (e.g., "BCA", "OVO", "DANA")
	// QRIS-related fields
	QRString *string `bson:"qr_string,omitempty"` // The actual QR Code string for scanning
	// Virtual Account (VA)-related fields
	VirtualAccount *string `bson:"virtual_account,omitempty"` // The Virtual Account number provided by the bank
	// E-Wallet-related fields
	EWalletType               *string                 `bson:"ewallet_type,omitempty"` // E-Wallet provider (e.g., OVO, DANA, ShopeePay)
	CheckoutMethod            *string                 `bson:"checkout_method,omitempty"`
	ChannelCode               *string                 `bson:"channel_code,omitempty"`
	SuccessRedirectURL        *string                 `bson:"success_redirect_url,omitempty"`
	FailureRedirectURL        *string                 `bson:"failure_redirect_url,omitempty"`
	IsRedirectRequired        *bool                   `bson:"is_redirect_required,omitempty"`
	MobileNumber              *string                 `bson:"mobile_number,omitempty"`
	CashTag                   *string                 `bson:"cash_tag,omitempty"`
	DesktopWebCheckoutURL     *string                 `bson:"desktop_web_checkout_url,omitempty"`
	MobileWebCheckoutURL      *string                 `bson:"mobile_web_checkout_url,omitempty"`
	MobileDeepLinkCheckoutURL *string                 `bson:"mobile_deeplink_checkout_url,omitempty"`
	Description               *string                 `bson:"description,omitempty"`
	Amount                    *float64                `bson:"amount,omitempty"`
	ChargeAmount              *float64                `bson:"charge_amount,omitempty"`
	PGFee                     *float64                `bson:"pg_fee,omitempty"`
	PGRealFee                 *float64                `bson:"pg_real_fee,omitempty"`
	PGPlatformFee             *float64                `bson:"pg_platform_fee,omitempty"`
	Notes                     *string                 `bson:"notes,omitempty"`
	Metadata                  *map[string]interface{} `bson:"metadata,omitempty"`
	Currency                  *string                 `bson:"currency,omitempty"`
	UseVoucher                *bool                   `bson:"use_voucher,omitempty"`
	VoucherCode               *string                 `bson:"voucher_code,omitempty"`
	Provider                  *string                 `bson:"provider,omitempty"`
	Status                    uint8                   `bson:"status,omitempty"`
	ExpiresAt                 *time.Time              `bson:"expires_at,omitempty"`
	PaidAt                    *time.Time              `bson:"paid_at,omitempty"`
	FailedAt                  *time.Time              `bson:"failed_at,omitempty"`
	OverdueAt                 *time.Time              `bson:"overdue_at,omitempty"`
}

const PaymentCollection = "payments"
