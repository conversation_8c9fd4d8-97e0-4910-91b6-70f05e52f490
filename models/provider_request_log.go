package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProviderRequestLog struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID  `bson:"company_id,omitempty"`
	TransactionID *primitive.ObjectID `bson:"transaction_id,omitempty"`
	Provider      string              `bson:"provider,omitempty"`
	URL           *string             `bson:"url,omitempty"`
	ExternalID    *string             `bson:"external_id,omitempty"`
	SubAccountID  *string             `bson:"sub_account_id,omitempty"`
	RequestBody   *string             `bson:"request_body,omitempty"`
	ResponseBody  *string             `bson:"response_body,omitempty"`
	StatusCode    *int                `bson:"status_code,omitempty"`
	Status        uint8               `bson:"status,omitempty"`
}

const ProviderRequestLogCollection = "provider_request_logs"
