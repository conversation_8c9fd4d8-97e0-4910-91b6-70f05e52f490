package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProviderPublishEventLog struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID  `bson:"company_id,omitempty"`
	TransactionID *primitive.ObjectID `bson:"transaction_id,omitempty"`
	Provider      string              `bson:"provider,omitempty"`
	Event         *string             `bson:"event,omitempty"`
	ExternalID    *string             `bson:"external_id,omitempty"`
	SubAccountID  *string             `bson:"sub_account_id,omitempty"`
	Data          *string             `bson:"data,omitempty"`
	Status        uint8               `bson:"status,omitempty"`
}

const ProviderPublishEventLogCollection = "provider_publish_event_logs"
