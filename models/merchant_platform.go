package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MerchantPlatform struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID `bson:"company_id,omitempty"`
	ProfileID     primitive.ObjectID `bson:"profile_id,omitempty"`
	MerchantID    primitive.ObjectID `bson:"merchant_id,omitempty"`
	Slug          *string            `bson:"slug,omitempty"`
	Name          *string            `bson:"name,omitempty"`
	DisplayName   *string            `bson:"display_name,omitempty"`
	Description   *string            `bson:"description,omitempty"`
	Metadata      *map[string]string `bson:"metadata,omitempty"`
	Status        uint8              `bson:"status,omitempty"`
}

const MerchantPlatformCollection = "merchant_platforms"
