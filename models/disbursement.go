package models

import (
	"time"

	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Disbursement represents a financial transaction where funds are sent to a specified bank account.
type Disbursement struct {
	mongorm.Model          `bson:",inline"`
	CompanyID              primitive.ObjectID `bson:"company_id,omitempty"`
	ProfileID              primitive.ObjectID `bson:"profile_id,omitempty"`
	MerchantID             primitive.ObjectID `bson:"merchant_id,omitempty"`
	MerchantBankID         primitive.ObjectID `bson:"merchant_bank_id,omitempty"`
	ExternalID             *string            `bson:"external_id,omitempty"`
	DisbursementNo         *string            `bson:"disbursement_no,omitempty"`
	ProviderDisbursementID *string            `bson:"provider_disbursement_id"`
	Amount                 *float64           `bson:"amount,omitempty"`
	NetAmount              *float64           `bson:"net_amount,omitempty"`
	PlatformFee            *float64           `bson:"platform_fee,omitempty"`
	PGFee                  *float64           `bson:"pg_fee,omitempty"`
	Fee                    *float64           `bson:"fee,omitempty"`
	BankCode               *string            `bson:"bank_code,omitempty"`
	AccountHolderName      *string            `bson:"account_holder_name,omitempty"`
	AccountNumber          *string            `bson:"account_number,omitempty"`
	Description            *string            `bson:"description,omitempty"`
	Provider               *string            `bson:"provider,omitempty"`
	DisbursedAt            *time.Time         `bson:"disbursed_at,omitempty"`
	FailedAt               *time.Time         `bson:"failed_at,omitempty"`
	IsAuto                 *bool              `bson:"is_auto,omitempty"`
	IsInstant              *bool              `bson:"is_instant,omitempty"`
	Status                 uint8              `bson:"status,omitempty"`        // Status of the disbursement (e.g., "PENDING", "COMPLETED", "FAILED")
	ErrorMessage           *string            `bson:"error_message,omitempty"` // Optional error message if the disbursement failed
	ProviderStatus         *string            `bson:"provider_status,omitempty"`
}

const DisbursementCollection = "disbursements"
