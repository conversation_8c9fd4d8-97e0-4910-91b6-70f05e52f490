package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CoinDisbursement struct {
	mongorm.Model `bson:",inline"`
	CompanyID     primitive.ObjectID      `bson:"company_id,omitempty"`
	ProfileID     primitive.ObjectID      `bson:"profile_id,omitempty"`
	CoinID        primitive.ObjectID      `bson:"coin_id,omitempty"`
	Amount        *float64                `bson:"amount,omitempty"`
	Description   *string                 `bson:"description,omitempty"`
	Metadata      *map[string]interface{} `bson:"metadata,omitempty"`
	Status        uint8                   `bson:"status,omitempty"`
}

const CoinDisbursementCollection = "coin_disbursements"
