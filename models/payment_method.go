package models

import (
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PaymentMethod struct {
	mongorm.Model      `bson:",inline"`
	CompanyID          primitive.ObjectID `bson:"company_id,omitempty"`
	Slug               *string            `bson:"slug,omitempty"`
	Name               *string            `bson:"name,omitempty"`
	DisplayName        *string            `bson:"display_name,omitempty"`
	Description        *string            `bson:"description,omitempty"`
	ChannelCode        *string            `bson:"channel_code,omitempty"`
	CheckoutMethod     *string            `bson:"checkout_method,omitempty"`
	FeeType            *string            `bson:"fee_type,omitempty"`
	FeeValue           *float64           `bson:"fee_value,omitempty"`
	ExtraFeeType       *string            `bson:"extra_fee_type,omitempty"`
	ExtraFeeValue      *float64           `bson:"extra_fee_value,omitempty"`
	PGFeeType          *string            `bson:"pg_fee_type,omitempty"`
	PGFeeValue         *float64           `bson:"pg_fee_value,omitempty"`
	PGExtraFeeType     *string            `bson:"pg_extra_fee_type,omitempty"`
	PGExtraFeeValue    *float64           `bson:"pg_extra_fee_value,omitempty"`
	SuccessRedirectURL *string            `bson:"success_redirect_url,omitempty"`
	FailureRedirectURL *string            `bson:"failure_redirect_url,omitempty"`
	IsRedirectRequired *bool              `bson:"is_redirect_required,omitempty"`
	Type               *string            `bson:"type,omitempty"`
	Metadata           *map[string]string `bson:"metadata,omitempty"`
	Provider           *string            `bson:"provider,omitempty"`
	Status             uint8              `bson:"status,omitempty"`
}

const PaymentMethodCollection = "payment_methods"
