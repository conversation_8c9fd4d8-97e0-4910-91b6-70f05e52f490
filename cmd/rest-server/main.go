package main

import (
	"context"
	"errors"
	"flag"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/continue-team/manaslu/config"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/manaslu/internal/repositories"
	"github.com/continue-team/manaslu/internal/rest"
	"github.com/continue-team/manaslu/internal/services"
	"github.com/continue-team/riot"
	riotMidd "github.com/continue-team/riot/middlewares"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"

	"github.com/didip/tollbooth/v6"
	"github.com/didip/tollbooth/v6/limiter"
)

func main() {
	var env, address string

	flag.StringVar(&env, "env", ".env", "Environment Variables filename")
	flag.StringVar(&address, "address", ":8084", "HTTP Server Address")
	flag.Parse()

	errC, err := run(env, address)
	if err != nil {
		log.Fatalf("Couldn't run: %s", err)
	}

	if err := <-errC; err != nil {
		log.Fatalf("Error while running: %s", err)
	}
}

// run starts the server
func run(env, address string) (<-chan error, error) {

	l := logger.Get()

	if err := riot.Load(env); err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeNotFound, "config.envvar.Load")
	}

	conf := riot.NewConfig()

	mongoDB, err := config.NewMongoSQL(conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.gorm.NewMongoSQL")
	}

	rmq, err := config.NewRabbitMQ(conf, l)
	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.rabbitmq.NewRabbitMQ")
	}

	srv, err := newServer(serverConfig{
		Address:  address,
		Mongo:    mongoDB,
		Logger:   l,
		RabbitMQ: rmq,
		Middlewares: []mux.MiddlewareFunc{
			(&riotMidd.MiddlewareNexus{Logger: l, Conf: conf}).Middleware(),
			(&riotMidd.RequestLogger{Logger: l, Conf: conf}).Middleware(),
			(&riotMidd.InterceptorReqID{Logger: l, Conf: conf}).Middleware(),
		},
		Conf: conf,
	})

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "newServer")
	}

	errC := make(chan error, 1)

	ctx, stop := signal.NotifyContext(context.Background(),
		os.Interrupt,
		syscall.SIGTERM,
		syscall.SIGQUIT)

	go func() {
		<-ctx.Done()

		l.Info("Shutdown signal received")

		ctxTimeout, cancel := context.WithTimeout(context.Background(), 5*time.Second)

		defer func() {
			_ = mongoDB.Client.Disconnect(context.Background())
			_ = l.Sync()
			stop()
			cancel()
			close(errC)
		}()

		srv.SetKeepAlivesEnabled(false)

		if err := srv.Shutdown(ctxTimeout); err != nil {
			errC <- err
		}
		l.Info("Shutdown Complete")
	}()

	go func() {
		l.Info("Listening and serving", zap.String("address", address))

		// "ListenAndServe always returns a non-nil error. After Shutdown or Close, the returned error is
		// ErrServerClosed."
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			errC <- err
		}
	}()

	return errC, nil
}

type serverConfig struct {
	Address     string
	Mongo       *riot.MongoDB
	Middlewares []mux.MiddlewareFunc
	Logger      *zap.Logger
	Conf        *riot.Configuration
	RabbitMQ    *riot.RabbitMQ
}

func newServer(conf serverConfig) (*http.Server, error) {
	util := riot.Util{
		Logger: conf.Logger,
		DB:     conf.Mongo.DB,
		Conf:   conf.Conf,
	}

	router := mux.NewRouter()
	excludeMiddlewareRouter := mux.NewRouter()

	for _, mw := range conf.Middlewares {
		router.Use(mw)
	}

	taskPublisher, err := rabbitmq.NewPublisher(util, conf.RabbitMQ)

	if err != nil {
		return nil, err
	}

	// repositories
	providerRepo := repositories.NewProviderRepository(util)
	coinRepo := repositories.NewCoinRepository(util)
	coinDisbRepo := repositories.NewCoinDisbursementRepository(util)
	coinTxnRepo := repositories.NewCoinTransactionRepository(util)
	feeRepo := repositories.NewFeeRepository(util)
	providerFeeRepo := repositories.NewProviderFeeRepository(util)
	merchantRepo := repositories.NewMerchantRepository(util)
	merchantFeeRepo := repositories.NewMerchantPlatformFeeRepository(util)
	bankRepo := repositories.NewBankRepository(util)
	platformFeeRepo := repositories.NewPlatformFeeRepository(util)
	transactionRepo := repositories.NewTransactionRepository(util)
	paymentRepo := repositories.NewPaymentRepository(util)
	voucherRepo := repositories.NewVoucherRepository(util)
	merchantVoucherRepo := repositories.NewMerchantVoucherRepository(util)
	featureFeeRepo := repositories.NewFeatureFeeRepository(util)
	merchantBankRepo := repositories.NewMerchantBankRepository(util)
	disbRepo := repositories.NewDisbursementRepository(util)
	disbLogRepo := repositories.NewDisbursementLogRepository(util)
	configRepo := repositories.NewConfigurationRepository(util)
	disbConfRepo := repositories.NewDisbursementConfigurationRepository(util)
	paymentMethodRepo := repositories.NewPaymentMethodRepository(util)
	merchantPlatformRepo := repositories.NewMerchantPlatformRepository(util)
	balanceTxnRepo := repositories.NewBalanceTransactionRepository(util)

	// services
	providerSvc := services.NewProviderService(util, providerRepo)
	providerFeeSvc := services.NewProviderFeeService(util, providerFeeRepo)
	merchantSvc := services.NewMerchantService(util, merchantRepo, taskPublisher)
	merchantFeeSvc := services.NewMerchantPlatformFeeService(util, merchantFeeRepo, merchantRepo, platformFeeRepo, feeRepo, configRepo)
	bankSvc := services.NewBankService(util, bankRepo)
	merchantBankSvc := services.NewMerchantBankService(util, merchantBankRepo, merchantRepo, bankRepo, taskPublisher)
	platformFeeSvc := services.NewPlatformFeeService(util, platformFeeRepo, feeRepo)
	transactionSvc := services.NewTransactionService(util, transactionRepo, merchantRepo, merchantFeeRepo, merchantVoucherRepo, voucherRepo, featureFeeRepo, feeRepo)
	paymentSvc := services.NewPaymentService(util, paymentRepo, merchantRepo, merchantFeeRepo, transactionRepo, merchantVoucherRepo, voucherRepo, featureFeeRepo, feeRepo, paymentMethodRepo, taskPublisher)
	voucherSvc := services.NewVoucherService(util, voucherRepo, feeRepo)
	merchantVoucerSvc := services.NewMerchantVoucherService(util, merchantVoucherRepo, merchantRepo, voucherRepo, feeRepo)
	featureFeeSvc := services.NewFeatureFeeService(util, featureFeeRepo)
	webhookSvc := services.NewWebhookService(util, taskPublisher)
	disbSvc := services.NewDisbursementService(util, disbRepo, merchantRepo, merchantBankRepo, configRepo, disbLogRepo, merchantPlatformRepo, taskPublisher)
	disbLogSvc := services.NewDisbursemenLogtService(util, disbLogRepo, taskPublisher)
	disbConfSvc := services.NewDisbursemenConfigurationtService(util, disbConfRepo, merchantRepo, merchantBankRepo, configRepo, taskPublisher)
	configSvc := services.NewConfigurationService(util, configRepo)
	merchantConfigSvc := services.NewMerchantConfigurationService(util, merchantRepo, merchantFeeRepo, merchantVoucherRepo, configRepo, feeRepo)
	paymentMethodSvc := services.NewPaymentMethodService(util, paymentMethodRepo)
	merchantPlatformSvc := services.NewMerchantPlatformService(util, merchantPlatformRepo)
	coinSvc := services.NewCoinService(util, coinRepo, configRepo, merchantRepo, coinDisbRepo, merchantPlatformRepo, taskPublisher)
	coinTxnSvc := services.NewCoinTransactionService(util, coinTxnRepo, coinRepo, transactionRepo)
	balanceTxnSvc := services.NewBalanceTransactionService(util, balanceTxnRepo, merchantRepo, transactionRepo, disbRepo)

	//webhooks
	rest.NewProviderHandler(util, providerSvc).Register(router)
	rest.NewProviderFeeHandler(util, providerFeeSvc).Register(router)
	rest.NewMerchantHandler(util, merchantSvc).Register(router)
	rest.NewMerchantPlatformFeeHandler(util, merchantFeeSvc).Register(router)
	rest.NewBankHandler(util, bankSvc).Register(router)
	rest.NewPlatformFeeHandler(util, platformFeeSvc).Register(router)
	rest.NewTransactionHandler(util, transactionSvc).Register(router)
	rest.NewPaymentHandler(util, paymentSvc).Register(router)
	rest.NewVoucherHandler(util, voucherSvc).Register(router)
	rest.NewMerchantVoucherHandler(util, merchantVoucerSvc).Register(router)
	rest.NewFeatureFeeHandler(util, featureFeeSvc).Register(router)
	rest.NewMerchantBankHandler(util, merchantBankSvc).Register(router)
	rest.NewDisbursementHandler(util, disbSvc).Register(router)
	rest.NewDisbursementLogHandler(util, disbLogSvc).Register(router)
	rest.NewConfigurationHandler(util, configSvc).Register(router)
	rest.NewMerchantDisbursementkHandler(util, disbConfSvc).Register(router)
	rest.NewMerchantConfigurationHandler(util, merchantConfigSvc).Register(router)
	rest.NewPaymentMethodHandler(util, paymentMethodSvc).Register(router)
	rest.NewMerchantPlatformHandler(util, merchantPlatformSvc).Register(router)
	rest.NewCoinHandler(util, coinSvc).Register(router)
	rest.NewCoinTransactionHandler(util, coinTxnSvc).Register(router)
	rest.NewBalanceTransactionHandler(util, balanceTxnSvc).Register(router)
	//webhooks
	rest.NewWebhookHandler(util, webhookSvc).Register(excludeMiddlewareRouter)

	limitValue, _ := strconv.ParseFloat(util.Conf.Get("RATE_LIMIT"), 64)
	// This could be the rate limit, indicating that the associated operation is allowed to occur up to 3 times within a specified time window.
	lmt := tollbooth.NewLimiter(limitValue, &limiter.ExpirableOptions{DefaultExpirationTTL: time.Second})

	lmtmw := tollbooth.LimitHandler(lmt, router)
	// Merge the routers
	mainRouter := mux.NewRouter()
	mainRouter.PathPrefix("/v1").Handler(lmtmw)
	mainRouter.PathPrefix("/auth").Handler(excludeMiddlewareRouter)
	mainRouter.PathPrefix("/webhook").Handler(excludeMiddlewareRouter)

	return &http.Server{
		Handler:           mainRouter,
		Addr:              conf.Address,
		ReadTimeout:       10 * time.Second,
		ReadHeaderTimeout: 10 * time.Second,
		WriteTimeout:      10 * time.Second,
		IdleTimeout:       10 * time.Second,
	}, nil
}
