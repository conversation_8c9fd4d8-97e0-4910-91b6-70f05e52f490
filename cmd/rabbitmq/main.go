package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/continue-team/manaslu/config"
	"github.com/continue-team/manaslu/internal/constant"
	"github.com/continue-team/manaslu/internal/rabbitmq"
	"github.com/continue-team/manaslu/internal/repositories"
	"github.com/continue-team/manaslu/internal/services"
	"github.com/continue-team/manaslu/internal/tasks"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/logger"
	r "github.com/continue-team/riot/rabbitmq"
	amqp "github.com/rabbitmq/amqp091-go"
	"go.uber.org/zap"
)

func main() {
	var env, exchange, queue, consumer string
	flag.StringVar(&env, "env", ".env", "Environment Variables filename")
	flag.StringVar(&exchange, "exchange", "manaslu.exchange.backend", "Type Exchange")
	flag.StringVar(&queue, "queue", "manaslu.queue", "Type Queue")
	flag.StringVar(&consumer, "consumer", "manaslu", "Consumer")
	flag.Parse()

	errC, err := run(env, exchange, queue, consumer)
	if err != nil {
		log.Fatalf("Couldn't run: %s", err)
	}

	if err := <-errC; err != nil {
		log.Fatalf("Error while running: %s", err)
	}
}

func run(env, exchange, queue, consumer string) (<-chan error, error) {
	l := logger.Get()

	if err := riot.Load(env); err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.envvar.Load")
	}

	conf := riot.NewConfig()

	rmq, err := config.NewRabbitMQ(conf, l)
	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.rabbitmq.NewRabbitMQ")
	}

	mongoDB, err := config.NewMongoSQL(conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.gorm.NewMongoSQL")
	}

	util := riot.Util{
		Logger: l,
		DB:     mongoDB.DB,
		Conf:   conf,
	}

	taskPublisher, err := rabbitmq.NewPublisher(util, rmq)

	if err != nil {
		return nil, err
	}

	merchantRepo := repositories.NewMerchantRepository(util)
	platformFeeRepo := repositories.NewPlatformFeeRepository(util)
	merchantPlatformFeeRepo := repositories.NewMerchantPlatformFeeRepository(util)
	feeRepo := repositories.NewFeeRepository(util)
	// merchantCommissionFeeRepo := repositories.NewMerchantCommisionFeeRepository(util)
	voucherRepo := repositories.NewVoucherRepository(util)
	merchantVoucherRepo := repositories.NewMerchantVoucherRepository(util)
	transactionRepo := repositories.NewTransactionRepository(util)
	paymentRepo := repositories.NewPaymentRepository(util)
	providerRequestLogRepo := repositories.NewProviderRequestLogRepository(util)
	providerPublishEventLogRepo := repositories.NewProviderPublishEventLogRepository(util)
	transferRepo := repositories.NewTransferRepository(util)
	subsidyRepo := repositories.NewSubsidyRepository(util)
	disburseRepo := repositories.NewDisbursementRepository(util)
	disburseConfigRepo := repositories.NewDisbursementConfigurationRepository(util)
	disburseLogRepo := repositories.NewDisbursementLogRepository(util)
	merchantBankRepo := repositories.NewMerchantBankRepository(util)
	merchantPlatformRepo := repositories.NewMerchantPlatformRepository(util)
	configRepo := repositories.NewConfigurationRepository(util)
	paymentMethodRepo := repositories.NewPaymentMethodRepository(util)
	featureFeeRepo := repositories.NewFeatureFeeRepository(util)
	txnBalanceRepo := repositories.NewBalanceTransactionRepository(util)
	coinRepo := repositories.NewCoinRepository(util)
	coinDisbRepo := repositories.NewCoinDisbursementRepository(util)
	coinTxnRepo := repositories.NewCoinTransactionRepository(util)

	merchantSvc := services.NewMerchantService(util, merchantRepo, taskPublisher)
	merchantPfSvc := services.NewMerchantPlatformFeeService(util, merchantPlatformFeeRepo, merchantRepo, platformFeeRepo, feeRepo, configRepo)
	merchantVoucherSvc := services.NewMerchantVoucherService(util, merchantVoucherRepo, merchantRepo, voucherRepo, feeRepo)
	disburseScv := services.NewDisbursementService(util, disburseRepo, merchantRepo, merchantBankRepo, configRepo, disburseLogRepo, merchantPlatformRepo, taskPublisher)
	txnBalanceSvc := services.NewBalanceTransactionService(util, txnBalanceRepo, merchantRepo, transactionRepo, disburseRepo)
	coinSvc := services.NewCoinService(util, coinRepo, configRepo, merchantRepo, coinDisbRepo, merchantPlatformRepo, taskPublisher)
	txnCoinSvc := services.NewCoinTransactionService(util, coinTxnRepo, coinRepo, transactionRepo)

	subs := r.NewSubscriber(rmq)

	task := tasks.NewTask(
		util,
		merchantSvc,
		merchantPfSvc,
		merchantVoucherSvc,
		merchantRepo,
		transactionRepo,
		paymentRepo,
		providerRequestLogRepo,
		providerPublishEventLogRepo,
		transferRepo,
		subsidyRepo,
		disburseRepo,
		disburseLogRepo,
		disburseConfigRepo,
		merchantBankRepo,
		configRepo,
		paymentMethodRepo,
		merchantPlatformRepo,
		featureFeeRepo,
		txnBalanceRepo,
		disburseScv,
		txnBalanceSvc,
		coinSvc,
		txnCoinSvc,
		taskPublisher,
	)

	srv := &Server{
		Exchange:     exchange,
		Queue:        queue,
		Consumer:     consumer,
		Logger:       l,
		taskConsumer: task,
		RabbitMQ:     rmq,
		Conf:         conf,
		Subscriber:   subs,
		done:         make(chan struct{}),
	}

	errC := make(chan error, 1)

	ctx, stop := signal.NotifyContext(context.Background(),
		os.Interrupt,
		syscall.SIGTERM,
		syscall.SIGQUIT,
	)

	go func() {
		<-ctx.Done()

		l.Info("Shutdown signal received")

		ctxTimeout, cancel := context.WithTimeout(context.Background(), 10*time.Second)

		defer func() {
			_ = l.Sync()

			rmq.Close()
			stop()
			cancel()
			close(errC)
		}()

		if err := srv.Shutdown(ctxTimeout); err != nil {
			errC <- err
		}

		l.Info("Shutdown completed")
	}()

	go func() {
		l.Info("Listening and serving")

		if err := srv.ListenAndServe(); err != nil {
			errC <- err
		}
	}()

	return errC, nil
}

type Server struct {
	Exchange     string
	Queue        string
	Consumer     string
	Mongo        *riot.MongoDB
	Conf         *riot.Configuration
	RabbitMQ     *riot.RabbitMQ
	taskConsumer *tasks.Task
	Subscriber   *r.Subscriber
	Logger       *zap.Logger
	done         chan struct{}
}

func (s *Server) ListenAndServe() error {

	// Consume QueueBind direct method
	// This consumer script subscribes to messages without any specific routing key pattern, i.e., it consumes messages directly.
	// Unlike in topic exchanges where routing keys are used for pattern matching, direct exchanges deliver messages based on a
	// one-to-one matching.

	if err := s.subscribeAndProcess(s.Exchange, "manaslu.route.backend", s.Queue, s.Consumer); err != nil {
		return err
	}

	return nil
}

// Shutdown ...
func (s *Server) Shutdown(ctx context.Context) error {
	s.Logger.Info("Shutting down server")

	_ = s.RabbitMQ.Channel.Cancel(s.Consumer, true)

	for {
		select {
		case <-ctx.Done():
			return riot.WrapErrorfLog(s.Logger, ctx.Err(), riot.ErrorCodeUnknown, "context.Done")
		case <-s.done:
			return nil
		}
	}
}

func (s *Server) processMessages(msgs <-chan amqp.Delivery, routingKey string) {
	go func() {
		for msg := range msgs {
			var nack bool
			var evt r.PayloadEvent

			// Unmarshal the JSON data into the struct
			if err := json.Unmarshal(msg.Body, &evt); err != nil {
				s.Logger.Error(fmt.Sprintf("Error decoding JSON: %v", err))
				nack = true
			} else {

				s.Logger.Info(fmt.Sprintf("Event:%s Received message: %v", evt.Event, zap.Any("Data", evt.Data)))

				switch evt.Event {
				case constant.TaskEvtCreateMerchant:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtCreateMerchant, constant.TaskEvtCreateMerchant, evt)
				case constant.TaskEvtProviderRequestLog:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtProviderRequestLog, constant.TaskEvtProviderRequestLog, evt)
				case constant.TaskEvtQrPayment:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtQrPayment, constant.TaskEvtQrPayment, evt)
				case constant.TaskEvtEWalletPayment:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtEWalletPayment, constant.TaskEvtEWalletPayment, evt)
				case constant.TaskEvtPlatformFee:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtPlatformFee, constant.TaskEvtPlatformFee, evt)
				case constant.TaskEvtTransfer:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtTransfer, constant.TaskEvtTransfer, evt)
				case constant.TaskEvtSubsidy:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtSubsidy, constant.TaskEvtSubsidy, evt)
				case constant.TaskEvtWebhookDisbursement:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtWebhookDisbursement, constant.TaskEvtWebhookDisbursement, evt)
				case constant.TaskEvtCronPopulateDisbursement:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtCronPopulateDisbursement, constant.TaskEvtCronPopulateDisbursement, evt)
				case constant.TaskEvtAutoDisbursement:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtAutoDisbursement, constant.TaskEvtAutoDisbursement, evt)
				case constant.TaskEvtTxnBalance:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtTxnBalance, constant.TaskEvtTxnBalance, evt)
				case constant.TaskEvtCoinBalance:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtCoinBalance, constant.TaskEvtCoinBalance, evt)
				case constant.TaskEvtCardPayment:
					nack = s.Subscriber.HandleEvent(s.taskConsumer.TaskEvtCardPayment, constant.TaskEvtCardPayment, evt)
				default:
					nack = false
				}
			}

			if nack {
				s.Logger.Info("NAcking :(")
				_ = msg.Nack(false, nack)
			} else {
				s.Logger.Info("Acking :)")
				_ = msg.Ack(false)
			}
		}

		s.Logger.Info(fmt.Sprintf("No more messages to consume for routing key: %s. Exiting.", routingKey))
		s.done <- struct{}{}
	}()
}

func (s *Server) subscribeAndProcess(exchange, routingKey, queue, consumer string) error {

	s.Logger.Info(fmt.Sprintf("Start: Exchange:%s Queue:%s Consumer:%s RoutingKey:%s", exchange, queue, consumer, routingKey))

	msgs, err := s.Subscriber.RunWithReconnect(exchange, routingKey, queue, consumer)
	if err != nil {
		return riot.WrapErrorfLog(s.Logger, err, riot.ErrorCodeUnknown, "Subscriber.Run.Routing")
	}

	s.processMessages(msgs, routingKey)
	return nil
}
