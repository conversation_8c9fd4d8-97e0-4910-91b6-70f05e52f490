package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/continue-team/manaslu/config"
	"github.com/continue-team/manaslu/internal/command"
	"github.com/continue-team/manaslu/internal/repositories"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/go-redis/redis/v8"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func main() {
	var env string

	flag.StringVar(&env, "env", ".env", "Environment Variables filename")
	flag.Parse()

	errC, err := run(env)
	if err != nil {
		log.Fatalf("Couldn't run: %s", err)
	}

	if err := <-errC; err != nil {
		log.Fatalf("Error while running: %s", err)
	}
}

// run starts the server
func run(env string) (<-chan error, error) {

	l := logger.Get()

	if err := riot.Load(env); err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeNotFound, "config.envvar.Load")
	}

	conf := riot.NewConfig()

	mongoDB, err := config.NewMongoSQL(conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.gorm.NewMongoSQL")
	}

	cmd, err := newServer(serverConfig{
		Mongo:  mongoDB,
		Logger: l,
		Conf:   conf,
	})

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "newServer")
	}

	errC := make(chan error, 1)

	ctx, stop := signal.NotifyContext(context.Background(),
		os.Interrupt,
		syscall.SIGTERM,
		syscall.SIGQUIT)

	go func() {
		<-ctx.Done()

		l.Info("Shutdown signal received")

		defer func() {
			_ = mongoDB.Client.Disconnect(context.Background())
			_ = l.Sync()
			stop()
			close(errC)
		}()

		l.Info("Shutdown Complete")
	}()

	if err := cmd.Execute(); err != nil {
		fmt.Println("🚨 CLI execution error:", err)
		os.Exit(1)
	}

	return errC, nil
}

type serverConfig struct {
	Address  string
	Mongo    *riot.MongoDB
	Logger   *zap.Logger
	Conf     *riot.Configuration
	Redis    *redis.Client
	RabbitMQ *riot.RabbitMQ
}

func newServer(conf serverConfig) (*cobra.Command, error) {
	util := riot.Util{
		Logger: conf.Logger,
		DB:     conf.Mongo.DB,
		Conf:   conf.Conf,
	}

	// repositories
	merchantRepo := repositories.NewMerchantRepository(util)
	coinRepo := repositories.NewCoinRepository(util)

	rootCmd := &cobra.Command{
		Use:   "cli",
		Short: "MyCLI is a sample command-line application",
		Long:  "A simple CLI application built with Go and Cobra",
	}

	ctx := context.Background()

	migrateCoinCmd := command.MigrateCoinCommand(ctx, util, merchantRepo, coinRepo)

	rootCmd.AddCommand(migrateCoinCmd)

	return rootCmd, nil
}
