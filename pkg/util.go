package pkg

import (
	"compress/gzip"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"math"
	mathRand "math/rand"
	"os"
	"reflect"
	"sort"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func SetBoolPointer(input *bool, action string) *bool {
	if input != nil {
		return input
	}
	if action == "create" {
		b := false
		return &b
	}
	return nil
}

func GetBoolValue(input *bool, defaultValue bool) bool {
	if input != nil {
		return *input
	}
	return defaultValue
}

func SetInt8Pointer(input *int8, action string) *int8 {
	if input != nil {
		return input
	}

	if action == "create" {
		b := int8(0)
		return &b
	}

	return nil
}

func SetInt64Pointer(input *int64, action string) *int64 {
	if input != nil {
		return input
	}

	if action == "create" {
		b := int64(0)
		return &b
	}

	return nil
}

func SetFloat64Pointer(input *float64, action string) *float64 {
	if input != nil {
		return input
	}

	if action == "create" {
		b := float64(0)
		return &b
	}

	return nil
}

func SetIntPointer(input *int, action string) *int {
	if input != nil {
		return input
	}

	if action == "create" {
		b := int(0)
		return &b
	}

	return nil
}

func GetInt8Value(input *int8, defaultValue int8) int8 {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetIntValue(input *int, defaultValue int) int {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetInt64Value(input *int64, defaultValue int64) int64 {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetFloat64Value(input *float64, defaultValue float64) float64 {
	if input != nil {
		return *input
	}
	return defaultValue
}

func SetStringPointer(input *string, action string) *string {
	if input != nil {
		return input
	}
	if action == "create" {
		b := ""
		return &b
	}
	return nil
}

func SetStringSlicerPointer(input *[]string, action string) *[]string {
	if input != nil {
		return input
	}
	if action == "create" {
		b := []string{}
		return &b
	}
	return nil
}

func SetMapstringPointer(input *map[string]string, action string) *map[string]string {
	if input != nil {
		return input
	}
	if action == "create" {
		b := map[string]string{}
		return &b
	}
	return nil
}

func SetArrStringPointer(input *[]string, action string) *[]string {
	if input != nil {
		return input
	}
	if action == "create" {
		b := []string{}
		return &b
	}
	return nil
}

func GetArrStringValue(input *[]string, defaultValue []string) []string {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetStringValue(input *string, defaultValue string) string {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetStringSlice(input *[]string, defaultValue []string) []string {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetMapstring(input *map[string]string, defaultValue map[string]string) map[string]string {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetMapInterface(input *map[string]interface{}, defaultValue map[string]interface{}) map[string]interface{} {
	if input != nil {
		return *input
	}
	return defaultValue
}

func SetStringUpperPointer(input *string, action string) *string {
	if input != nil {
		str := *input // Dereference the input pointer
		upperStr := strings.ToUpper(str)
		return &upperStr
	}
	if action == "create" {
		b := ""
		return &b
	}
	return nil
}

func SetMapInterfacePointer(input *map[string]interface{}, action string) *map[string]interface{} {
	if input != nil {
		str := *input // Dereference the input pointer
		return &str
	}

	if action == "create" {
		b := make(map[string]interface{})
		return &b
	}

	return nil
}

func SetStringLowerPointer(input *string, action string) *string {
	if input != nil {
		str := *input // Dereference the input pointer
		upperStr := strings.ToLower(str)
		return &upperStr
	}
	if action == "create" {
		b := ""
		return &b
	}
	return nil
}

const DateLayoutDefault = "2006-01-02 15:04:05"
const DateLayoutNano = "2020-10-21T13:57:43.355897Z"

func ParseDateString(dateStr, layout string) (time.Time, error) {
	if strings.HasSuffix(dateStr, " 24:00:00") {
		dateOnly := dateStr[:10]
		parsedDate, err := time.Parse("2006-01-02", dateOnly)
		if err != nil {
			return time.Time{}, fmt.Errorf("failed to parse date: %w", err)
		}
		return parsedDate.Add(24 * time.Hour).UTC(), nil
	}

	parsedTime, err := time.Parse(layout, dateStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse date string: %w", err)
	}
	return parsedTime.UTC(), nil
}

func ParseDateNanoString(dateStr string) (time.Time, error) {

	parsedTime, err := time.Parse(time.RFC3339Nano, dateStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse date string: %w", err)
	}
	return parsedTime.UTC(), nil
}

func GenerateRandomState(n int) (string, error) {
	// Create a byte slice with length n
	b := make([]byte, n)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	// Encode the byte slice into a base64 string
	return base64.URLEncoding.EncodeToString(b), nil
}

// GenerateShortKey generates a random short key of a specified length
func GenerateShortKey(minLength, maxLength int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	mathRand.Seed(time.Now().UnixNano())
	length := mathRand.Intn(maxLength-minLength+1) + minLength
	shortCode := make([]byte, length)
	for i := range shortCode {
		shortCode[i] = charset[mathRand.Intn(len(charset))]
	}
	return string(shortCode)
}

func GetPeriodRange(period string) (time.Time, time.Time) {
	now := time.Now()
	var start, end time.Time

	switch period {
	case "hour":
		start = now.Truncate(time.Hour)
		end = start.Add(time.Hour)
	case "day":
		start = now.Truncate(24 * time.Hour)
		end = start.Add(24 * time.Hour)
	case "month":
		start = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		end = start.AddDate(0, 1, 0)
	default:
		start = now.Truncate(24 * time.Hour)
		end = start.Add(24 * time.Hour)
	}

	return start, end
}

func GetDateToStringFormat(scale string) string {
	switch scale {
	case "hourly":
		return "%Y-%m-%dT%H:00:00.000Z"
	case "daily":
		return "%Y-%m-%dT%H:00:00.000Z"
	case "weekly":
		// MongoDB tidak langsung mendukung minggu, gunakan hari pertama dalam minggu.
		return "%Y-%m-%dT00:00:00.000Z"
	case "monthly":
		return "%Y-%m-01T00:00:00.000Z"
	default:
		panic("Unsupported scale: " + scale)
	}
}

// GenerateInstagramEmbed generates an Instagram embed code for a given permalink
func GenerateInstagramEmbed(permalink string) string {
	// Check if the permalink is valid
	if permalink == "" {
		return "Invalid permalink"
	}

	// Generate embed code
	embedCode := fmt.Sprintf(`<blockquote class="instagram-media" data-instgrm-permalink="%s" style="background:#FFF; border:0; margin:0; padding:0; width:100%%;">
<div style="padding:16px;">...</div>
</blockquote>
<script async src="//www.instagram.com/embed.js"></script>
`, permalink)

	return embedCode
}

func CompressToGZ(inputPath, outputPath string) error {
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %w", err)
	}
	defer inputFile.Close()

	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer outputFile.Close()

	gzipWriter := gzip.NewWriter(outputFile)
	defer gzipWriter.Close()

	_, err = io.Copy(gzipWriter, inputFile)
	if err != nil {
		return fmt.Errorf("failed to compress file: %w", err)
	}

	return nil
}

func GenerateHash(secretKey, strToHash string) string {
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(strToHash))
	return hex.EncodeToString(h.Sum(nil))
}

func GenerateUniqueNo() string {
	// Get current date in YYYYMMDD format
	now := time.Now()
	date := now.Format("20060102")

	// Generate an 8-digit random number
	mathRand.Seed(time.Now().UnixNano())
	randomDigits := mathRand.Intn(90000000) + 10000000 // Ensure it's an 8-digit number

	// Combine date and random digits
	id := fmt.Sprintf("%s-%08d", date, randomDigits)

	return id
}

func SetObjectID(input *string) (*primitive.ObjectID, error) {
	if input != nil && *input != "" {
		objectID, err := primitive.ObjectIDFromHex(*input)
		if err != nil {
			return nil, err
		}
		return &objectID, nil
	}

	return nil, nil
}

func GenerateExternalID(text string) string {

	// Combine date and random digits
	id := fmt.Sprintf("%s:%d", text, time.Now().UnixNano())
	return id
}

func CalculatePercentage(amount float64, percentage float64) float64 {
	return math.Round((amount * percentage) / 100)
}

func ToNegativeFloat(n float64) float64 {
	if n > 0 {
		return -n
	}
	return n
}

func ToNegative(n int) int {
	if n > 0 {
		return -n
	}
	return n
}

func HashStruct(secret string, inputStruct interface{}) (string, error) {
	values := reflect.ValueOf(inputStruct)
	fields := reflect.TypeOf(inputStruct)

	// Ensure input is a struct
	if values.Kind() != reflect.Struct {
		return "", fmt.Errorf("expected struct, got %s", values.Kind())
	}

	var fieldNames []string
	for i := 0; i < fields.NumField(); i++ {
		fieldNames = append(fieldNames, fields.Field(i).Name)
	}
	sort.Strings(fieldNames)

	var strToHash string
	for _, fieldName := range fieldNames {
		field := values.FieldByName(fieldName)

		// Skip invalid or zero fields
		if !field.IsValid() || field.IsZero() {
			continue
		}

		// Skip specific field types
		switch field.Kind() {
		case reflect.Map, reflect.Slice, reflect.Array, reflect.Ptr, reflect.Interface:
			continue
		}

		// Append field value to string
		fieldValue := field.Interface()
		strToHash += fmt.Sprintf(".%v", fieldValue)
	}

	// Remove leading dot
	if len(strToHash) > 0 {
		strToHash = strToHash[1:]
	}

	fmt.Println("HashStruct", strToHash)

	// Generate and return the hash
	return GenerateHash(secret, strToHash), nil
}
